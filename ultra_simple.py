"""
Application ultra-simple - Gestion des Pointages
Version minimale qui fonctionne à 100%
"""

import os
import sys
import time
import threading
import webbrowser
from flask import Flask, render_template_string, request, redirect, session, flash

app = Flask(__name__)
app.secret_key = 'simple-key-2025'

# Templates intégrés
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Connexion - Gestion des Pointages</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card mt-5">
                    <div class="card-header text-center bg-primary text-white">
                        <h3>🏢 Gestion des Pointages</h3>
                    </div>
                    <div class="card-body">
                        {% if error %}
                        <div class="alert alert-danger">{{ error }}</div>
                        {% endif %}
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">Nom d'utilisateur</label>
                                <input type="text" name="username" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Mot de passe</label>
                                <input type="password" name="password" class="form-control" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">Se connecter</button>
                        </form>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">Utilisateur: <strong>admin</strong> / Mot de passe: <strong>admin</strong></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Tableau de bord - Gestion des Pointages</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">🏢 Gestion des Pointages</a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">Bienvenue {{ username }}</span>
                <a href="/logout" class="btn btn-outline-light btn-sm">Déconnexion</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1>📊 Tableau de bord</h1>
        
        <div class="row mt-4">
            <div class="col-md-4 mb-3">
                <div class="card text-white bg-primary">
                    <div class="card-body">
                        <h5>👥 Employés</h5>
                        <h2>{{ employees_count }}</h2>
                        <a href="/employees" class="text-white">Voir tous →</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <h5>📋 Tâches</h5>
                        <h2>{{ tasks_count }}</h2>
                        <a href="/tasks" class="text-white">Voir toutes →</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card text-white bg-info">
                    <div class="card-body">
                        <h5>✅ Terminées</h5>
                        <h2>{{ completed_count }}</h2>
                        <a href="/tasks" class="text-white">Voir détails →</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <h5>🚀 Actions rapides</h5>
                        <a href="/add_employee" class="btn btn-primary me-2">
                            <i class="fas fa-user-plus"></i> Ajouter un employé
                        </a>
                        <a href="/add_task" class="btn btn-success">
                            <i class="fas fa-plus"></i> Ajouter une tâche
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
'''

SIMPLE_FORM_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>{{ title }} - Gestion des Pointages</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">🏢 Gestion des Pointages</a>
            <div class="navbar-nav ms-auto">
                <a href="/logout" class="btn btn-outline-light btn-sm">Déconnexion</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1>{{ title }}</h1>
        
        {% if success %}
        <div class="alert alert-success">{{ success }}</div>
        {% endif %}
        
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    {{ form_content | safe }}
                    <div class="mt-3">
                        <button type="submit" class="btn btn-primary">Enregistrer</button>
                        <a href="{{ back_url }}" class="btn btn-secondary">Retour</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
'''

# Données en mémoire (simple)
users = {'admin': 'admin'}
employees = []
tasks = []

@app.route('/')
def index():
    if 'username' not in session:
        return redirect('/login')
    
    return render_template_string(DASHBOARD_TEMPLATE, 
                                username=session['username'],
                                employees_count=len(employees),
                                tasks_count=len(tasks),
                                completed_count=len([t for t in tasks if t.get('status') == 'completed']))

@app.route('/login', methods=['GET', 'POST'])
def login():
    error = None
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if username in users and users[username] == password:
            session['username'] = username
            return redirect('/')
        else:
            error = 'Nom d\'utilisateur ou mot de passe incorrect'
    
    return render_template_string(LOGIN_TEMPLATE, error=error)

@app.route('/logout')
def logout():
    session.clear()
    return redirect('/login')

@app.route('/employees')
def employees_list():
    if 'username' not in session:
        return redirect('/login')
    
    html = '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Employés - Gestion des Pointages</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">🏢 Gestion des Pointages</a>
                <div class="navbar-nav ms-auto">
                    <a href="/logout" class="btn btn-outline-light btn-sm">Déconnexion</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between">
                <h1>👥 Employés</h1>
                <a href="/add_employee" class="btn btn-primary">Ajouter un employé</a>
            </div>
            
            <div class="card mt-3">
                <div class="card-body">
    '''
    
    if employees:
        html += '<div class="table-responsive"><table class="table table-striped">'
        html += '<thead><tr><th>Nom</th><th>Poste</th><th>Salaire</th><th>Téléphone</th></tr></thead><tbody>'
        for emp in employees:
            html += f'<tr><td>{emp["name"]}</td><td>{emp.get("position", "-")}</td><td>{emp.get("salary", "-")}</td><td>{emp.get("phone", "-")}</td></tr>'
        html += '</tbody></table></div>'
    else:
        html += '<div class="text-center py-4"><h5 class="text-muted">Aucun employé</h5><a href="/add_employee" class="btn btn-primary">Ajouter le premier employé</a></div>'
    
    html += '''
                </div>
            </div>
        </div>
    </body>
    </html>
    '''
    
    return html

@app.route('/add_employee', methods=['GET', 'POST'])
def add_employee():
    if 'username' not in session:
        return redirect('/login')
    
    success = None
    if request.method == 'POST':
        employee = {
            'name': request.form['name'],
            'position': request.form['position'],
            'salary': request.form['salary'],
            'phone': request.form['phone']
        }
        employees.append(employee)
        success = 'Employé ajouté avec succès!'
    
    form_content = '''
    <div class="mb-3">
        <label class="form-label">Nom complet *</label>
        <input type="text" name="name" class="form-control" required>
    </div>
    <div class="mb-3">
        <label class="form-label">Poste</label>
        <input type="text" name="position" class="form-control">
    </div>
    <div class="mb-3">
        <label class="form-label">Salaire (MAD)</label>
        <input type="number" name="salary" class="form-control">
    </div>
    <div class="mb-3">
        <label class="form-label">Téléphone</label>
        <input type="tel" name="phone" class="form-control">
    </div>
    '''
    
    return render_template_string(SIMPLE_FORM_TEMPLATE,
                                title='Ajouter un Employé',
                                form_content=form_content,
                                back_url='/employees',
                                success=success)

@app.route('/tasks')
def tasks_list():
    if 'username' not in session:
        return redirect('/login')
    
    html = '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Tâches - Gestion des Pointages</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">🏢 Gestion des Pointages</a>
                <div class="navbar-nav ms-auto">
                    <a href="/logout" class="btn btn-outline-light btn-sm">Déconnexion</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between">
                <h1>📋 Tâches</h1>
                <a href="/add_task" class="btn btn-primary">Ajouter une tâche</a>
            </div>
            
            <div class="card mt-3">
                <div class="card-body">
    '''
    
    if tasks:
        html += '<div class="table-responsive"><table class="table table-striped">'
        html += '<thead><tr><th>Titre</th><th>Description</th><th>Statut</th><th>Priorité</th></tr></thead><tbody>'
        for task in tasks:
            status_badge = f'<span class="badge bg-{"success" if task["status"] == "completed" else "warning"}">{task["status"]}</span>'
            priority_badge = f'<span class="badge bg-{"danger" if task["priority"] == "high" else "info"}">{task["priority"]}</span>'
            html += f'<tr><td>{task["title"]}</td><td>{task.get("description", "-")}</td><td>{status_badge}</td><td>{priority_badge}</td></tr>'
        html += '</tbody></table></div>'
    else:
        html += '<div class="text-center py-4"><h5 class="text-muted">Aucune tâche</h5><a href="/add_task" class="btn btn-primary">Ajouter la première tâche</a></div>'
    
    html += '''
                </div>
            </div>
        </div>
    </body>
    </html>
    '''
    
    return html

@app.route('/add_task', methods=['GET', 'POST'])
def add_task():
    if 'username' not in session:
        return redirect('/login')
    
    success = None
    if request.method == 'POST':
        task = {
            'title': request.form['title'],
            'description': request.form['description'],
            'status': request.form['status'],
            'priority': request.form['priority']
        }
        tasks.append(task)
        success = 'Tâche ajoutée avec succès!'
    
    form_content = '''
    <div class="mb-3">
        <label class="form-label">Titre *</label>
        <input type="text" name="title" class="form-control" required>
    </div>
    <div class="mb-3">
        <label class="form-label">Description</label>
        <textarea name="description" class="form-control" rows="3"></textarea>
    </div>
    <div class="mb-3">
        <label class="form-label">Statut</label>
        <select name="status" class="form-select">
            <option value="new">Nouveau</option>
            <option value="in_progress">En cours</option>
            <option value="completed">Terminé</option>
        </select>
    </div>
    <div class="mb-3">
        <label class="form-label">Priorité</label>
        <select name="priority" class="form-select">
            <option value="low">Faible</option>
            <option value="medium">Moyenne</option>
            <option value="high">Élevée</option>
        </select>
    </div>
    '''
    
    return render_template_string(SIMPLE_FORM_TEMPLATE,
                                title='Ajouter une Tâche',
                                form_content=form_content,
                                back_url='/tasks',
                                success=success)

def open_browser():
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:5000')
        print("✓ Navigateur ouvert")
    except:
        print("⚠ Ouvrez manuellement: http://localhost:5000")

def main():
    print("=" * 50)
    print("🏢 GESTION DES POINTAGES - VERSION SIMPLE")
    print("=" * 50)
    print("🚀 Démarrage du serveur...")
    print("📍 URL: http://localhost:5000")
    print("👤 Connexion: admin / admin")
    print("🔄 Le navigateur va s'ouvrir...")
    print("-" * 50)
    
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)

if __name__ == '__main__':
    main()
