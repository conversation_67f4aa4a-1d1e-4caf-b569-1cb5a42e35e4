#!/usr/bin/env python3
"""
Full diagnostic script for the Flask application
"""
import sys
import os
import traceback

def main():
    print("🔍 DIAGNOSTIC COMPLET - GESTION DES POINTAGES")
    print("=" * 60)
    print(f"Python version: {sys.version}")
    print(f"Current directory: {os.getcwd()}")
    print("=" * 60)
    
    # Test 1: Basic imports
    print("\n1️⃣ TEST DES IMPORTS DE BASE")
    try:
        import flask
        print(f"   ✅ Flask {flask.__version__}")
        
        import flask_sqlalchemy
        print(f"   ✅ Flask-SQLAlchemy")
        
        import flask_login
        print(f"   ✅ Flask-Login")
        
        import flask_wtf
        print(f"   ✅ Flask-WTF")
        
    except Exception as e:
        print(f"   ❌ Erreur imports de base: {e}")
        return False
    
    # Test 2: Application modules
    print("\n2️⃣ TEST DES MODULES DE L'APPLICATION")
    try:
        import config
        print("   ✅ Config")
        
        import extensions
        print("   ✅ Extensions")
        
        import models
        print("   ✅ Models")
        
        import routes
        print("   ✅ Routes")
        
    except Exception as e:
        print(f"   ❌ Erreur modules app: {e}")
        traceback.print_exc()
        return False
    
    # Test 3: App creation
    print("\n3️⃣ TEST DE CRÉATION DE L'APPLICATION")
    try:
        from app import create_app
        app = create_app()
        print("   ✅ Application créée avec succès")
        
        # Test app context
        with app.app_context():
            print("   ✅ Contexte d'application OK")
            
    except Exception as e:
        print(f"   ❌ Erreur création app: {e}")
        traceback.print_exc()
        return False
    
    # Test 4: Database
    print("\n4️⃣ TEST DE LA BASE DE DONNÉES")
    try:
        from extensions import db
        with app.app_context():
            # Check if database file exists
            db_path = os.path.join(os.getcwd(), 'instance', 'app.db')
            if os.path.exists(db_path):
                print(f"   ✅ Base de données trouvée: {db_path}")
            else:
                print(f"   ⚠️  Base de données non trouvée, sera créée")
                
            # Try to create tables
            db.create_all()
            print("   ✅ Tables créées/vérifiées")
            
    except Exception as e:
        print(f"   ❌ Erreur base de données: {e}")
        traceback.print_exc()
        return False
    
    # Test 5: Routes
    print("\n5️⃣ TEST DES ROUTES")
    try:
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            print(f"   ✅ {len(rules)} routes enregistrées")
            for rule in rules[:5]:  # Show first 5 routes
                print(f"      - {rule.rule}")
            if len(rules) > 5:
                print(f"      ... et {len(rules) - 5} autres")
                
    except Exception as e:
        print(f"   ❌ Erreur routes: {e}")
        traceback.print_exc()
        return False
    
    print("\n✅ TOUS LES TESTS RÉUSSIS!")
    print("🚀 L'application devrait pouvoir démarrer")
    
    # Try to start the server
    print("\n6️⃣ TENTATIVE DE DÉMARRAGE DU SERVEUR")
    try:
        print("📍 URL: http://127.0.0.1:5001")
        print("👤 Utilisateur: admin")
        print("🔑 Mot de passe: admin")
        print("⏹️  Appuyez sur Ctrl+C pour arrêter")
        print("=" * 60)
        
        app.run(host='127.0.0.1', port=5001, debug=True)
        
    except KeyboardInterrupt:
        print("\n🛑 Serveur arrêté par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur démarrage serveur: {e}")
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\nAppuyez sur Entrée pour fermer...")
    except Exception as e:
        print(f"❌ Erreur fatale: {e}")
        traceback.print_exc()
        input("\nAppuyez sur Entrée pour fermer...")
