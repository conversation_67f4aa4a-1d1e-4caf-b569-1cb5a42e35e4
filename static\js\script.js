// Script personnalisé pour Gestion des Pointages

document.addEventListener('DOMContentLoaded', function() {
    // Activer les tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Activer les popovers Bootstrap
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Confirmation de suppression
    document.querySelectorAll('.delete-confirm').forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cet élément ? Cette action est irréversible.')) {
                e.preventDefault();
            }
        });
    });
    
    // Calcul automatique du total pour les transactions
    function calculateTotal() {
        const transactionRows = document.querySelectorAll('.transaction-row');
        let total = 0;
        
        transactionRows.forEach(row => {
            const amountInput = row.querySelector('.transaction-amount');
            if (amountInput && amountInput.value) {
                total += parseFloat(amountInput.value);
            }
        });
        
        const totalElement = document.getElementById('transaction-total');
        if (totalElement) {
            totalElement.textContent = total.toFixed(2) + ' MAD';
        }
    }
    
    // Ajouter un écouteur d'événements pour les champs de montant
    document.querySelectorAll('.transaction-amount').forEach(input => {
        input.addEventListener('input', calculateTotal);
    });
    
    // Calculer le total initial
    calculateTotal();
    
    // Ajouter une nouvelle ligne de transaction
    const addTransactionButton = document.getElementById('add-transaction-row');
    if (addTransactionButton) {
        addTransactionButton.addEventListener('click', function() {
            const transactionContainer = document.getElementById('transaction-container');
            const rowCount = transactionContainer.querySelectorAll('.transaction-row').length + 1;
            
            const newRow = document.createElement('div');
            newRow.className = 'transaction-row row mb-3';
            newRow.innerHTML = `
                <div class="col-md-8">
                    <input type="text" name="description_${rowCount}" class="form-control" placeholder="Description">
                </div>
                <div class="col-md-3">
                    <input type="number" step="0.01" name="amount_${rowCount}" class="form-control transaction-amount" placeholder="Montant (MAD)">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger remove-transaction-row">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            
            transactionContainer.appendChild(newRow);
            
            // Ajouter un écouteur d'événements pour le nouveau champ de montant
            newRow.querySelector('.transaction-amount').addEventListener('input', calculateTotal);
            
            // Ajouter un écouteur d'événements pour le bouton de suppression
            newRow.querySelector('.remove-transaction-row').addEventListener('click', function() {
                newRow.remove();
                calculateTotal();
            });
        });
    }
    
    // Supprimer une ligne de transaction
    document.querySelectorAll('.remove-transaction-row').forEach(button => {
        button.addEventListener('click', function() {
            this.closest('.transaction-row').remove();
            calculateTotal();
        });
    });
    
    // Filtrer les tableaux
    const tableFilter = document.getElementById('table-filter');
    if (tableFilter) {
        tableFilter.addEventListener('input', function() {
            const filterValue = this.value.toLowerCase();
            const table = document.querySelector('.filterable-table');
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.indexOf(filterValue) > -1) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
});

// Fonction pour formater les montants en MAD
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-MA', { 
        style: 'currency', 
        currency: 'MAD',
        minimumFractionDigits: 2
    }).format(amount);
}

// Fonction pour convertir les montants en MAD
function convertToMAD(amount, fromCurrency) {
    // Taux de change fictifs (à remplacer par des taux réels)
    const exchangeRates = {
        'EUR': 10.85, // 1 EUR = 10.85 MAD
        'USD': 9.95,  // 1 USD = 9.95 MAD
        'GBP': 12.75  // 1 GBP = 12.75 MAD
    };
    
    if (fromCurrency === 'MAD') {
        return amount;
    }
    
    if (exchangeRates[fromCurrency]) {
        return amount * exchangeRates[fromCurrency];
    }
    
    return amount; // Si la devise n'est pas reconnue, retourner le montant original
}
