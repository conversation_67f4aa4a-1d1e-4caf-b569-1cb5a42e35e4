#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simple pour initialiser la base de données
"""

import os
import sqlite3
from datetime import datetime

def create_simple_database():
    """Créer une base de données SQLite simple"""
    print("=== CRÉATION D'UNE NOUVELLE BASE DE DONNÉES ===")
    
    # C<PERSON>er le dossier instance
    base_dir = os.path.dirname(os.path.abspath(__file__))
    instance_dir = os.path.join(base_dir, 'instance')
    os.makedirs(instance_dir, exist_ok=True)
    
    db_path = os.path.join(instance_dir, 'app.db')
    
    # Supprimer l'ancienne base si elle existe
    if os.path.exists(db_path):
        try:
            os.remove(db_path)
            print("Ancienne base de données supprimée")
        except Exception as e:
            print(f"Erreur lors de la suppression: {e}")
    
    # Créer une nouvelle base de données vide
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Créer une table de test simple
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_table (
                id INTEGER PRIMARY KEY,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Insérer un enregistrement de test
        cursor.execute('INSERT INTO test_table (id) VALUES (1)')
        
        conn.commit()
        conn.close()
        
        print(f"Base de données créée avec succès: {db_path}")
        return True
        
    except Exception as e:
        print(f"Erreur lors de la création: {e}")
        return False

if __name__ == '__main__':
    if create_simple_database():
        print("\n✅ Base de données créée!")
        print("Vous pouvez maintenant démarrer l'application avec: python app.py")
    else:
        print("\n❌ Erreur lors de la création")
