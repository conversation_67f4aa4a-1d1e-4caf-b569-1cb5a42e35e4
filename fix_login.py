#!/usr/bin/env python3
"""
Script to fix login issues
"""
import sys
import os

# A<PERSON>ter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_login():
    print("🔧 CORRECTION DES PROBLÈMES DE CONNEXION")
    print("=" * 50)
    
    try:
        # Import de l'application
        from app import create_app, create_tables
        from models import User
        from extensions import db
        
        app = create_app()
        
        # Désactiver CSRF pour les tests
        app.config['WTF_CSRF_ENABLED'] = False
        app.config['TESTING'] = True
        
        with app.app_context():
            print("1️⃣ Vérification de la base de données...")
            
            # Créer les tables si nécessaire
            create_tables(app)
            
            # Vérifier l'utilisateur admin
            admin = User.query.filter_by(username='admin').first()
            
            if not admin:
                print("🔧 Création de l'utilisateur admin...")
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    is_admin=True,
                    can_manage_users=True,
                    can_manage_employees=True,
                    can_manage_tasks=True,
                    can_manage_finances=True,
                    can_access_registration_files=True,
                    can_access_technical_files=True,
                    can_access_reimbursement_files=True,
                    can_access_organization_files=True,
                    can_access_trainer_files=True,
                    can_access_trainer_schedule=True
                )
                admin.set_password('admin')
                db.session.add(admin)
                db.session.commit()
                print("✅ Utilisateur admin créé")
            else:
                print("✅ Utilisateur admin existe")
                # Réinitialiser le mot de passe pour être sûr
                admin.set_password('admin')
                db.session.commit()
                print("✅ Mot de passe admin réinitialisé")
            
            print("\n2️⃣ Test de connexion...")
            
            # Test avec le client de test Flask
            with app.test_client() as client:
                # Test GET login page
                response = client.get('/login')
                print(f"   GET /login: {response.status_code}")
                
                if response.status_code == 200:
                    print("   ✅ Page de connexion accessible")
                    
                    # Test POST login (sans CSRF car désactivé)
                    response = client.post('/login', data={
                        'username': 'admin',
                        'password': 'admin',
                        'submit': 'Se connecter'
                    }, follow_redirects=False)
                    
                    print(f"   POST /login: {response.status_code}")
                    
                    if response.status_code == 302:
                        print("   ✅ Connexion réussie - redirection")
                        print(f"   📍 Redirection vers: {response.location}")
                        
                        # Test d'accès au dashboard
                        response = client.get('/dashboard', follow_redirects=True)
                        print(f"   GET /dashboard: {response.status_code}")
                        
                        if response.status_code == 200:
                            print("   ✅ Dashboard accessible")
                        else:
                            print("   ❌ Dashboard inaccessible")
                            
                    else:
                        print("   ❌ Échec de la connexion")
                        
                        # Afficher le contenu de la réponse pour debug
                        content = response.data.decode('utf-8')
                        if 'incorrect' in content.lower():
                            print("   ❌ Credentials incorrects")
                        else:
                            print("   ❌ Autre erreur")
                            
                else:
                    print(f"   ❌ Page de connexion inaccessible: {response.status_code}")
            
            print("\n3️⃣ Création d'un utilisateur de test supplémentaire...")
            
            # Créer un utilisateur de test
            test_user = User.query.filter_by(username='test').first()
            if not test_user:
                test_user = User(
                    username='test',
                    email='<EMAIL>',
                    is_admin=False,
                    can_manage_users=False,
                    can_manage_employees=True,
                    can_manage_tasks=True,
                    can_manage_finances=False
                )
                test_user.set_password('test')
                db.session.add(test_user)
                db.session.commit()
                print("✅ Utilisateur test créé (test/test)")
            else:
                print("✅ Utilisateur test existe déjà")
            
            print("\n✅ CORRECTION TERMINÉE")
            print("=" * 50)
            print("🔑 Credentials disponibles:")
            print("   Admin: admin / admin")
            print("   Test:  test / test")
            print("=" * 50)
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_login()
