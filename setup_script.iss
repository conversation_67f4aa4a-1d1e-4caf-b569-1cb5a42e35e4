
; Script Inno Setup pour Gestion des Pointages
; Compatible avec Windows 7, 8, 8.1, 10, 11 (32/64 bits)

[Setup]
AppName=Gestion des Pointages
AppVersion=1.0.0
AppPublisher=Gestion des Pointages
AppPublisherURL=https://www.gestion-pointages.com
AppSupportURL=https://www.gestion-pointages.com/support
AppUpdatesURL=https://www.gestion-pointages.com/updates
DefaultDirName={autopf}\Gestion des Pointages
DefaultGroupName=Gestion des Pointages
AllowNoIcons=yes
LicenseFile=
OutputDir=setup
OutputBaseFilename=GestionPointages_Setup_v1.0.0
SetupIconFile=
Compression=lzma
SolidCompression=yes
WizardStyle=modern
ArchitecturesAllowed=x86 x64
ArchitecturesInstallIn64BitMode=x64
MinVersion=6.1
PrivilegesRequired=admin

[Languages]
Name: "french"; MessagesFile: "compiler:Languages\French.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
Source: "dist\GestionPointages.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "installer\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\Gestion des Pointages"; Filename: "{app}\GestionPointages.exe"
Name: "{group}\{cm:UninstallProgram,Gestion des Pointages}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\Gestion des Pointages"; Filename: "{app}\GestionPointages.exe"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\Gestion des Pointages"; Filename: "{app}\GestionPointages.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\GestionPointages.exe"; Description: "{cm:LaunchProgram,Gestion des Pointages}"; Flags: nowait postinstall skipifsilent

[Code]
function InitializeSetup(): Boolean;
begin
  Result := True;
  if not IsAdminLoggedOn then
  begin
    MsgBox('Ce programme nécessite des privilèges administrateur pour l''installation.', mbError, MB_OK);
    Result := False;
  end;
end;
