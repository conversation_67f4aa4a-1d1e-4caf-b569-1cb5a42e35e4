#!/usr/bin/env python3
"""
Script pour démarrer le serveur de façon fiable
"""

import os
import sys
import time
import webbrowser
from threading import Timer

def check_dependencies():
    """Vérifier les dépendances"""
    print("🔍 Vérification des dépendances...")
    
    required_modules = [
        'flask', 'flask_sqlalchemy', 'flask_login', 
        'flask_wtf', 'wtforms', 'werkzeug'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module.replace('_', '.'))
            print(f"✅ {module}")
        except ImportError:
            missing.append(module)
            print(f"❌ {module}")
    
    if missing:
        print(f"\n⚠️  Modules manquants: {', '.join(missing)}")
        print("💡 Installez avec: pip install flask flask-sqlalchemy flask-login flask-wtf")
        return False
    
    return True

def check_database():
    """Vérifier la base de données"""
    print("\n🔍 Vérification de la base de données...")
    
    db_path = os.path.join('instance', 'app.db')
    
    if not os.path.exists('instance'):
        os.makedirs('instance')
        print("✅ Dossier instance créé")
    
    if not os.path.exists(db_path):
        print("⚠️  Base de données non trouvée, création...")
        try:
            from app import create_app
            from extensions import db
            
            app = create_app()
            with app.app_context():
                db.create_all()
                print("✅ Base de données créée")
        except Exception as e:
            print(f"❌ Erreur création DB: {e}")
            return False
    else:
        print("✅ Base de données trouvée")
    
    return True

def open_browser_delayed():
    """Ouvrir le navigateur après un délai"""
    time.sleep(2)
    try:
        webbrowser.open('http://127.0.0.1:5001')
        print("🌐 Navigateur ouvert sur http://127.0.0.1:5001")
    except:
        print("⚠️  Impossible d'ouvrir le navigateur automatiquement")
        print("💡 Ouvrez manuellement: http://127.0.0.1:5001")

def start_server():
    """Démarrer le serveur"""
    print("\n🚀 Démarrage du serveur...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        # Ouvrir le navigateur après 2 secondes
        timer = Timer(2.0, open_browser_delayed)
        timer.start()
        
        print("✅ Serveur démarré avec succès!")
        print("🌐 URL: http://127.0.0.1:5001")
        print("👤 Utilisateur: admin")
        print("🔑 Mot de passe: admin")
        print("\n💡 Appuyez sur Ctrl+C pour arrêter le serveur")
        print("=" * 50)
        
        # Démarrer le serveur
        app.run(
            host='127.0.0.1',
            port=5001,
            debug=True,
            use_reloader=False  # Éviter le double démarrage
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 Serveur arrêté par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur serveur: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Fonction principale"""
    print("🚀 Démarrage de Gestion des Pointages")
    print("=" * 50)
    
    # Vérifications
    if not check_dependencies():
        return
    
    if not check_database():
        return
    
    # Démarrer le serveur
    start_server()

if __name__ == "__main__":
    main()
