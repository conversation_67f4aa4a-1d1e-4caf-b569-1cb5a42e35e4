#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de construction pour Gestion des Pointages
Version réseau multi-utilisateurs
Compatible Windows 7-11 (32/64 bit)
"""

import os
import sys
import shutil
import subprocess
import platform
import json
from pathlib import Path
from datetime import datetime

class NetworkAppBuilder:
    """Constructeur d'application réseau"""
    
    def __init__(self):
        self.base_path = Path(__file__).parent
        self.app_name = "GestionPointages"
        self.app_version = "1.0.0"
        self.build_dir = self.base_path / "build"
        self.dist_dir = self.base_path / "dist"
        self.spec_file = self.base_path / "GestionPointages_Network.spec"
        
        # Configuration de construction
        self.python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        self.platform_info = platform.platform()
        self.architecture = platform.architecture()[0]
        
        print(f"=== Constructeur Gestion des Pointages v{self.app_version} ===")
        print(f"Python: {self.python_version}")
        print(f"Plateforme: {self.platform_info}")
        print(f"Architecture: {self.architecture}")
        print("=" * 60)
    
    def check_requirements(self):
        """Vérifier les prérequis"""
        print("🔍 Vérification des prérequis...")
        
        # Vérifier PyInstaller
        try:
            import PyInstaller
            print(f"✅ PyInstaller {PyInstaller.__version__} installé")
        except ImportError:
            print("❌ PyInstaller non installé")
            print("Installation en cours...")
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("✅ PyInstaller installé")
        
        # Vérifier les dépendances principales
        required_modules = [
            'flask', 'flask_sqlalchemy', 'flask_login', 'flask_wtf',
            'flask_migrate', 'flask_bootstrap4', 'flask_babel',
            'wtforms', 'sqlalchemy', 'babel'
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
                print(f"✅ {module}")
            except ImportError:
                missing_modules.append(module)
                print(f"❌ {module}")
        
        if missing_modules:
            print(f"\n⚠️  Modules manquants: {', '.join(missing_modules)}")
            print("Installation des dépendances...")
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
            print("✅ Dépendances installées")
        
        return True
    
    def prepare_build_environment(self):
        """Préparer l'environnement de construction"""
        print("\n🔧 Préparation de l'environnement...")
        
        # Nettoyer les anciens builds
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print("🗑️  Ancien dossier build supprimé")
        
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
            print("🗑️  Ancien dossier dist supprimé")
        
        # Créer les dossiers nécessaires
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)
        
        # Vérifier les fichiers essentiels
        essential_files = [
            "app.py", "config.py", "config_network.py",
            "extensions.py", "models.py", "routes.py",
            "templates", "static"
        ]
        
        for file_path in essential_files:
            if not (self.base_path / file_path).exists():
                raise FileNotFoundError(f"Fichier essentiel manquant: {file_path}")
        
        print("✅ Environnement préparé")
        return True
    
    def create_version_info(self):
        """Créer le fichier d'informations de version"""
        print("\n📝 Création des informations de version...")
        
        version_info = {
            "app_name": self.app_name,
            "app_version": self.app_version,
            "build_date": datetime.now().isoformat(),
            "python_version": self.python_version,
            "platform": self.platform_info,
            "architecture": self.architecture,
            "network_enabled": True,
            "multi_user_support": True,
            "supported_windows": ["Windows 7", "Windows 8", "Windows 8.1", "Windows 10", "Windows 11"],
            "supported_architectures": ["32-bit", "64-bit"]
        }
        
        version_file = self.base_path / "version_info.json"
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, indent=4, ensure_ascii=False)
        
        print(f"✅ Fichier de version créé: {version_file}")
        return True
    
    def build_executable(self):
        """Construire l'exécutable"""
        print("\n🔨 Construction de l'exécutable...")
        
        if not self.spec_file.exists():
            raise FileNotFoundError(f"Fichier spec manquant: {self.spec_file}")
        
        # Commande PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            str(self.spec_file)
        ]
        
        print(f"Commande: {' '.join(cmd)}")
        
        # Exécuter PyInstaller
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("✅ Construction réussie")
            
            if result.stdout:
                print("📋 Sortie PyInstaller:")
                print(result.stdout)
                
        except subprocess.CalledProcessError as e:
            print("❌ Erreur lors de la construction")
            print(f"Code de retour: {e.returncode}")
            if e.stdout:
                print("Sortie standard:")
                print(e.stdout)
            if e.stderr:
                print("Erreur standard:")
                print(e.stderr)
            raise
        
        return True
    
    def create_network_scripts(self):
        """Créer les scripts de réseau"""
        print("\n📜 Création des scripts réseau...")
        
        try:
            from config_network import network_config
            network_config.create_startup_scripts()
            print("✅ Scripts réseau créés")
        except Exception as e:
            print(f"⚠️  Erreur lors de la création des scripts: {e}")
        
        return True
    
    def copy_additional_files(self):
        """Copier les fichiers additionnels"""
        print("\n📁 Copie des fichiers additionnels...")
        
        dist_app_dir = self.dist_dir / f"{self.app_name}_v{self.app_version}"
        
        if not dist_app_dir.exists():
            print(f"❌ Dossier de distribution non trouvé: {dist_app_dir}")
            return False
        
        # Fichiers à copier
        additional_files = [
            ("README.md", "README.md"),
            ("version_info.json", "version_info.json"),
            ("start_server.bat", "start_server.bat"),
            ("start_client.bat", "start_client.bat"),
            ("network_config.json", "network_config.json"),
        ]
        
        for src, dst in additional_files:
            src_path = self.base_path / src
            dst_path = dist_app_dir / dst
            
            if src_path.exists():
                shutil.copy2(src_path, dst_path)
                print(f"✅ {src} → {dst}")
            else:
                print(f"⚠️  Fichier non trouvé: {src}")
        
        return True
    
    def create_installer_config(self):
        """Créer la configuration pour l'installateur"""
        print("\n⚙️  Création de la configuration d'installation...")
        
        installer_config = {
            "app_name": self.app_name,
            "app_version": self.app_version,
            "company_name": "Système de Gestion des Pointages",
            "app_description": "Système de gestion des pointages multi-utilisateurs",
            "app_publisher": "Gestion des Pointages",
            "app_url": "https://github.com/gestion-pointages",
            "app_exe": f"{self.app_name}.exe",
            "app_icon": "static\\Image\\App Gestion Des Pointages.png",
            "output_dir": str(self.dist_dir),
            "source_dir": str(self.dist_dir / f"{self.app_name}_v{self.app_version}"),
            "supported_windows": "Windows 7, 8, 8.1, 10, 11",
            "supported_arch": "32-bit et 64-bit",
            "install_dir": "{autopf}\\Gestion des Pointages",
            "create_desktop_icon": True,
            "create_start_menu": True,
            "create_uninstaller": True
        }
        
        config_file = self.base_path / "installer_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(installer_config, f, indent=4, ensure_ascii=False)
        
        print(f"✅ Configuration d'installation créée: {config_file}")
        return True
    
    def build(self):
        """Processus de construction complet"""
        try:
            print(f"\n🚀 Début de la construction - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Étapes de construction
            self.check_requirements()
            self.prepare_build_environment()
            self.create_version_info()
            self.create_network_scripts()
            self.build_executable()
            self.copy_additional_files()
            self.create_installer_config()
            
            print(f"\n🎉 Construction terminée avec succès!")
            print(f"📦 Fichiers de sortie dans: {self.dist_dir}")
            
            # Afficher les informations finales
            dist_app_dir = self.dist_dir / f"{self.app_name}_v{self.app_version}"
            if dist_app_dir.exists():
                exe_file = dist_app_dir / f"{self.app_name}.exe"
                if exe_file.exists():
                    size_mb = exe_file.stat().st_size / (1024 * 1024)
                    print(f"📊 Taille de l'exécutable: {size_mb:.1f} MB")
                    print(f"🎯 Exécutable: {exe_file}")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Erreur lors de la construction: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    builder = NetworkAppBuilder()
    success = builder.build()
    
    if success:
        print("\n✅ Construction réussie!")
        sys.exit(0)
    else:
        print("\n❌ Construction échouée!")
        sys.exit(1)
