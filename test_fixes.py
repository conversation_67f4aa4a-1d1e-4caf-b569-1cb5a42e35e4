#!/usr/bin/env python3
"""
Script de test pour vérifier les corrections apportées
"""

import os
import sys
import requests
import time
from datetime import datetime

def test_server_running():
    """Test si le serveur est en cours d'exécution"""
    try:
        response = requests.get('http://127.0.0.1:5001', timeout=5)
        if response.status_code == 200 or response.status_code == 302:
            print("✅ Serveur en cours d'exécution")
            return True
        else:
            print(f"❌ Serveur répond avec le code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Serveur non accessible: {e}")
        return False

def test_login_page():
    """Test de la page de connexion"""
    try:
        response = requests.get('http://127.0.0.1:5001/login', timeout=5)
        if response.status_code == 200:
            print("✅ Page de connexion accessible")
            return True
        else:
            print(f"❌ Page de connexion inaccessible: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur page de connexion: {e}")
        return False

def test_tasks_page():
    """Test de la page des tâches (nécessite une session)"""
    try:
        # Créer une session
        session = requests.Session()
        
        # Obtenir la page de connexion pour récupérer le token CSRF
        login_page = session.get('http://127.0.0.1:5001/login')
        
        # Tenter de se connecter (ceci peut échouer sans les bons identifiants)
        login_data = {
            'username': 'admin',
            'password': 'admin'
        }
        
        login_response = session.post('http://127.0.0.1:5001/login', data=login_data)
        
        if login_response.status_code == 200 or login_response.status_code == 302:
            # Tester la page des tâches
            tasks_response = session.get('http://127.0.0.1:5001/tasks')
            if tasks_response.status_code == 200:
                print("✅ Page des tâches accessible")
                return True
            else:
                print(f"❌ Page des tâches inaccessible: {tasks_response.status_code}")
                return False
        else:
            print("⚠️  Connexion échouée (normal si pas d'utilisateur admin)")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur test tâches: {e}")
        return False

def test_file_structure():
    """Test de la structure des fichiers"""
    required_files = [
        'app.py',
        'routes.py',
        'models.py',
        'forms.py',
        'templates/task/tasks.html',
        'templates/task/edit_task.html',
        'static/css/style.css',
        'static/js/script.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if not missing_files:
        print("✅ Tous les fichiers requis sont présents")
        return True
    else:
        print(f"❌ Fichiers manquants: {', '.join(missing_files)}")
        return False

def test_javascript_functions():
    """Test de la présence des fonctions JavaScript"""
    try:
        with open('templates/task/tasks.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_functions = [
            'confirmDelete',
            'sendTaskWhatsApp',
            'sendTaskEmail'
        ]
        
        missing_functions = []
        for func in required_functions:
            if f'function {func}' not in content:
                missing_functions.append(func)
        
        if not missing_functions:
            print("✅ Toutes les fonctions JavaScript sont présentes")
            return True
        else:
            print(f"❌ Fonctions JavaScript manquantes: {', '.join(missing_functions)}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test JavaScript: {e}")
        return False

def test_css_classes():
    """Test de la présence des classes CSS"""
    try:
        with open('templates/task/tasks.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_classes = [
            'status-new',
            'status-in_progress',
            'status-completed',
            'status-cancelled'
        ]
        
        missing_classes = []
        for css_class in required_classes:
            if f'.{css_class}' not in content:
                missing_classes.append(css_class)
        
        if not missing_classes:
            print("✅ Toutes les classes CSS sont présentes")
            return True
        else:
            print(f"❌ Classes CSS manquantes: {', '.join(missing_classes)}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test CSS: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 TESTS DE VÉRIFICATION DES CORRECTIONS")
    print("=" * 50)
    
    tests = [
        ("Structure des fichiers", test_file_structure),
        ("Fonctions JavaScript", test_javascript_functions),
        ("Classes CSS", test_css_classes),
        ("Serveur en cours", test_server_running),
        ("Page de connexion", test_login_page),
        ("Page des tâches", test_tasks_page)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Test: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS:")
    
    passed = 0
    failed = 0
    skipped = 0
    
    for test_name, result in results:
        if result is True:
            print(f"✅ {test_name}: RÉUSSI")
            passed += 1
        elif result is False:
            print(f"❌ {test_name}: ÉCHOUÉ")
            failed += 1
        else:
            print(f"⚠️  {test_name}: IGNORÉ")
            skipped += 1
    
    print(f"\n📈 Statistiques:")
    print(f"   Réussis: {passed}")
    print(f"   Échoués: {failed}")
    print(f"   Ignorés: {skipped}")
    
    if failed == 0:
        print("\n🎉 TOUS LES TESTS SONT RÉUSSIS!")
    else:
        print(f"\n⚠️  {failed} test(s) ont échoué")
    
    print("\n💡 Pour tester manuellement:")
    print("1. Démarrez le serveur: python start.py")
    print("2. Ouvrez: http://127.0.0.1:5001")
    print("3. Connectez-vous: admin/admin")
    print("4. Testez les fonctions de suppression, WhatsApp et Email")

if __name__ == '__main__':
    main()
