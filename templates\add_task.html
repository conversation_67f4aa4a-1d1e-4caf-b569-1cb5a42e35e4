{% extends "base_simple.html" %}

{% block title %}Ajouter une Tâche - Gestion des Pointages{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-plus"></i> Ajouter une Tâche</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('tasks') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour à la liste
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-task"></i> Informations de la Tâche</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="title" class="form-label">Titre *</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="4"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Statut</label>
                            <select class="form-select" id="status" name="status">
                                <option value="new">Nouveau</option>
                                <option value="in_progress">En cours</option>
                                <option value="completed">Terminé</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="priority" class="form-label">Priorité</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="low">Faible</option>
                                <option value="medium" selected>Moyenne</option>
                                <option value="high">Élevée</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="assigned_to" class="form-label">Assigné à</label>
                            <select class="form-select" id="assigned_to" name="assigned_to">
                                <option value="">Sélectionner un employé</option>
                                {% for employee in employees %}
                                <option value="{{ employee.id }}">{{ employee.first_name }} {{ employee.last_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="due_date" class="form-label">Date d'échéance</label>
                            <input type="date" class="form-control" id="due_date" name="due_date">
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('tasks') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> Aide</h6>
            </div>
            <div class="card-body">
                <p><small class="text-muted">
                    Créez une nouvelle tâche en remplissant les informations ci-contre.
                </small></p>
                
                <hr>
                
                <h6>Statuts :</h6>
                <ul class="small text-muted">
                    <li><strong>Nouveau</strong> : Tâche créée</li>
                    <li><strong>En cours</strong> : Tâche en progression</li>
                    <li><strong>Terminé</strong> : Tâche achevée</li>
                </ul>

                <h6>Priorités :</h6>
                <ul class="small text-muted">
                    <li><strong>Faible</strong> : Pas urgent</li>
                    <li><strong>Moyenne</strong> : Normal</li>
                    <li><strong>Élevée</strong> : Urgent</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
