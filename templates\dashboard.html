{% extends "base_simple.html" %}

{% block title %}Tableau de bord - Gestion des Pointages{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-tachometer-alt"></i> Tableau de bord</h1>
</div>

<div class="row">
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Employés</h5>
                        <h2 class="card-text">--</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('employees') }}" class="text-white text-decoration-none">
                    Voir tous <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Tâches</h5>
                        <h2 class="card-text">--</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tasks fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('tasks') }}" class="text-white text-decoration-none">
                    Voir toutes <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">En cours</h5>
                        <h2 class="card-text">--</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('tasks') }}" class="text-white text-decoration-none">
                    Voir détails <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Terminées</h5>
                        <h2 class="card-text">--</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('tasks') }}" class="text-white text-decoration-none">
                    Voir toutes <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Bienvenue dans le système de gestion</h5>
            </div>
            <div class="card-body">
                <p>Bienvenue <strong>{{ session.username }}</strong> dans le système de gestion des pointages.</p>
                <p>Utilisez le menu de navigation pour accéder aux différentes fonctionnalités :</p>
                <ul>
                    <li><strong>Employés</strong> : Gérer les informations des employés</li>
                    <li><strong>Tâches</strong> : Créer et suivre les tâches</li>
                </ul>
                
                <div class="mt-3">
                    <a href="{{ url_for('add_employee') }}" class="btn btn-primary me-2">
                        <i class="fas fa-user-plus"></i> Ajouter un employé
                    </a>
                    <a href="{{ url_for('add_task') }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Ajouter une tâche
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
