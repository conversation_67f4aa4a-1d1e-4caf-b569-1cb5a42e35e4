#!/usr/bin/env python3
"""
Script simple pour créer les tables d'administration
"""

import os
import sqlite3

def create_tables_manually():
    """Créer les tables manuellement avec SQL"""
    try:
        # Chemin vers la base de données
        db_path = os.path.join('instance', 'app.db')
        
        if not os.path.exists('instance'):
            os.makedirs('instance')
            print("✅ Dossier instance créé")
        
        # Connexion à la base de données
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 Création des tables d'administration...")
        
        # Créer la table user_activities
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_activities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action VARCHAR(100) NOT NULL,
                description TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                extra_data TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        print("✅ Table user_activities créée")
        
        # Créer la table database_backups
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS database_backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                backup_type VARCHAR(50) NOT NULL,
                format VARCHAR(10) NOT NULL,
                file_size_mb REAL NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        print("✅ Table database_backups créée")
        
        # Valider les changements
        conn.commit()
        
        # Vérifier les tables créées
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"\n📋 Tables dans la base de données ({len(tables)}):")
        for table in sorted(tables):
            print(f"  - {table}")
        
        # Vérifier spécifiquement nos tables
        admin_tables = ['user_activities', 'database_backups']
        for table in admin_tables:
            if table in tables:
                print(f"✅ {table} disponible")
            else:
                print(f"❌ {table} manquante")
        
        conn.close()
        print("\n🎉 Tables d'administration créées avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_tables():
    """Tester l'accès aux tables"""
    try:
        from app import create_app
        from models import UserActivity, DatabaseBackup
        
        app = create_app()
        with app.app_context():
            # Test UserActivity
            count_activities = UserActivity.query.count()
            print(f"📊 Activités utilisateurs: {count_activities}")
            
            # Test DatabaseBackup
            count_backups = DatabaseBackup.query.count()
            print(f"📊 Sauvegardes: {count_backups}")
            
            print("✅ Accès aux tables réussi")
            return True
            
    except Exception as e:
        print(f"❌ Erreur test: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Réparation des tables d'administration")
    print("=" * 50)
    
    # Créer les tables
    if create_tables_manually():
        print("\n🧪 Test d'accès aux tables...")
        if test_tables():
            print("\n🎉 RÉPARATION RÉUSSIE!")
            print("💡 Vous pouvez maintenant accéder aux fonctionnalités d'administration")
        else:
            print("\n⚠️  Tables créées mais problème d'accès")
    else:
        print("\n❌ ÉCHEC DE LA RÉPARATION")
    
    print("\n🚀 Pour démarrer le serveur:")
    print("   python start.py")
    
    input("\nAppuyez sur Entrée pour fermer...")
