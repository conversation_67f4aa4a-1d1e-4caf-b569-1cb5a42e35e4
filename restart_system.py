#!/usr/bin/env python3
"""
Script pour redémarrer le système proprement
"""

import os
import sys
import sqlite3
from werkzeug.security import generate_password_hash

def reset_database():
    """Réinitialiser la base de données"""
    print("🔄 Réinitialisation de la base de données...")
    
    try:
        from app import create_app
        from models import User, Employee, SimpleTask
        from extensions import db
        
        app = create_app()
        
        with app.app_context():
            # Supprimer toutes les tables
            db.drop_all()
            print("✅ Tables supprimées")
            
            # Recréer les tables
            db.create_all()
            print("✅ Tables recréées")
            
            # Créer l'utilisateur admin
            admin = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True,
                can_manage_employees=True,
                can_manage_tasks=True,
                can_manage_finances=True
            )
            admin.set_password('admin')
            db.session.add(admin)
            
            # Créer un employé de test
            employee = Employee(
                first_name='Test',
                last_name='Employee',
                position='Développeur',
                cin='TEST123',
                daily_rate=150.0
            )
            db.session.add(employee)
            
            # Créer quelques tâches de test
            tasks = [
                SimpleTask(
                    title='Tâche de test 1',
                    description='Première tâche pour tester le système',
                    status='new',
                    priority='high'
                ),
                SimpleTask(
                    title='Tâche de test 2',
                    description='Deuxième tâche pour tester le système',
                    status='in_progress',
                    priority='normal'
                ),
                SimpleTask(
                    title='Tâche de test 3',
                    description='Troisième tâche pour tester le système',
                    status='completed',
                    priority='low'
                )
            ]
            
            for task in tasks:
                db.session.add(task)
            
            db.session.commit()
            print("✅ Données de test créées")
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_system():
    """Tester le système"""
    print("🧪 Test du système...")
    
    try:
        from app import create_app
        from models import User, SimpleTask
        
        app = create_app()
        
        with app.app_context():
            # Vérifier l'admin
            admin = User.query.filter_by(username='admin').first()
            if admin and admin.check_password('admin'):
                print("✅ Utilisateur admin OK")
            else:
                print("❌ Problème avec l'utilisateur admin")
                return False
            
            # Vérifier les tâches
            task_count = SimpleTask.query.count()
            print(f"✅ {task_count} tâches trouvées")
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur de test: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Redémarrage du système Gestion des Pointages")
    print("=" * 50)
    
    # Réinitialiser la base de données
    if not reset_database():
        print("❌ Échec de la réinitialisation")
        return
    
    # Tester le système
    if not test_system():
        print("❌ Échec du test")
        return
    
    print("\n" + "=" * 50)
    print("🎉 SYSTÈME REDÉMARRÉ AVEC SUCCÈS!")
    print("\n💡 INFORMATIONS DE CONNEXION:")
    print("   Utilisateur: admin")
    print("   Mot de passe: admin")
    print("   URL: http://127.0.0.1:5001")
    print("\n🚀 POUR DÉMARRER LE SERVEUR:")
    print("   python run_app.py")
    print("\n📋 FONCTIONNALITÉS DISPONIBLES:")
    print("   ✅ Connexion/Déconnexion")
    print("   ✅ Gestion des tâches (avec suppression)")
    print("   ✅ Gestion des employés")
    print("   ✅ Mot de passe oublié")
    print("   ✅ Interface responsive")

if __name__ == "__main__":
    main()
