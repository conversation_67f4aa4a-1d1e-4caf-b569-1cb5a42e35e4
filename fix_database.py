#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger les problèmes de base de données
"""

import os
import sys
import sqlite3
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

def fix_database():
    """Corriger les problèmes de base de données"""
    print("=== CORRECTION DE LA BASE DE DONNÉES ===")
    
    # Configuration simple
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///app.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'test-key'
    
    db = SQLAlchemy(app)
    
    # Modèle utilisateur simple
    class User(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        password_hash = db.Column(db.String(128))
        is_admin = db.Column(db.Boolean, default=False)
        can_manage_users = db.Column(db.Boolean, default=False)
        can_manage_employees = db.Column(db.Boolean, default=False)
        can_manage_tasks = db.Column(db.Boolean, default=False)
        can_manage_finances = db.Column(db.Boolean, default=False)
        can_access_registration_files = db.Column(db.Boolean, default=False)
        can_access_technical_files = db.Column(db.Boolean, default=False)
        can_access_reimbursement_files = db.Column(db.Boolean, default=False)
        can_access_organization_files = db.Column(db.Boolean, default=False)
        can_access_trainer_files = db.Column(db.Boolean, default=False)
        can_access_trainer_schedule = db.Column(db.Boolean, default=False)
        
        def set_password(self, password):
            from werkzeug.security import generate_password_hash
            self.password_hash = generate_password_hash(password)
    
    with app.app_context():
        try:
            # Supprimer l'ancienne base de données
            if os.path.exists('app.db'):
                os.remove('app.db')
                print("Ancienne base de données supprimée")
            
            # Créer les tables
            db.create_all()
            print("Tables créées avec succès")
            
            # Créer l'utilisateur admin
            admin = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True,
                can_manage_users=True,
                can_manage_employees=True,
                can_manage_tasks=True,
                can_manage_finances=True,
                can_access_registration_files=True,
                can_access_technical_files=True,
                can_access_reimbursement_files=True,
                can_access_organization_files=True,
                can_access_trainer_files=True,
                can_access_trainer_schedule=True
            )
            admin.set_password('admin')
            db.session.add(admin)
            db.session.commit()
            print("Utilisateur admin créé: admin / admin")
            
            return True
            
        except Exception as e:
            print(f"Erreur: {e}")
            return False

if __name__ == '__main__':
    if fix_database():
        print("\n✅ Base de données corrigée!")
        print("Vous pouvez maintenant démarrer l'application")
    else:
        print("\n❌ Erreur lors de la correction")
        sys.exit(1)
