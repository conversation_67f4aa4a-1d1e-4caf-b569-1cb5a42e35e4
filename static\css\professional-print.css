/**
 * Professional Print Styles for Gestion des Pointages
 * High-quality document templates with modern design
 */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: #1a1a1a;
    background: #ffffff;
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Document Container */
.professional-document {
    max-width: 210mm;
    margin: 0 auto;
    padding: 20mm;
    min-height: 297mm;
    position: relative;
    background: white;
    box-shadow: 0 0 30px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

/* Header Styles */
.document-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding-bottom: 30px;
    margin-bottom: 40px;
    border-bottom: 3px solid #2563eb;
    position: relative;
}

.document-header::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.company-branding {
    display: flex;
    align-items: center;
    gap: 20px;
}

.company-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 32px;
    font-weight: 700;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    position: relative;
    overflow: hidden;
}

.company-logo::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: logoShine 3s ease-in-out infinite;
}

@keyframes logoShine {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.company-info {
    flex: 1;
}

.company-name {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
    background: linear-gradient(135deg, #1a1a1a, #374151);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.company-details {
    color: #6b7280;
    font-size: 13px;
    line-height: 1.5;
}

.company-details strong {
    color: #374151;
    font-weight: 600;
}

/* Document Meta */
.document-meta {
    text-align: right;
    color: #6b7280;
    font-size: 12px;
}

.document-type {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
    display: inline-block;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.document-date {
    font-weight: 500;
    color: #374151;
    margin-top: 4px;
}

/* Document Title */
.document-title {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

.main-title {
    font-size: 36px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
    position: relative;
}

.main-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #2563eb, #f59e0b);
    border-radius: 2px;
}

.subtitle {
    color: #6b7280;
    font-size: 16px;
    font-weight: 400;
    margin-top: 20px;
}

/* Content Sections */
.content-section {
    margin-bottom: 35px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
}

.section-title::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, #2563eb, #f59e0b);
}

.section-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}

/* Information Grid */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.info-card {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #2563eb, #f59e0b);
}

.info-card:hover {
    border-color: #2563eb;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
    transform: translateY(-2px);
}

.info-label {
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 6px;
}

.info-value {
    font-size: 16px;
    font-weight: 500;
    color: #1a1a1a;
    word-wrap: break-word;
    line-height: 1.4;
}

/* Status and Priority Badges */
.status-badge, .priority-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 14px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.status-badge::before, .priority-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.status-badge:hover::before, .priority-badge:hover::before {
    left: 100%;
}

/* Status Colors */
.status-new { 
    background: linear-gradient(135deg, #dbeafe, #bfdbfe); 
    color: #1d4ed8; 
    border: 1px solid #93c5fd;
}

.status-progress { 
    background: linear-gradient(135deg, #fef3c7, #fde68a); 
    color: #d97706; 
    border: 1px solid #fbbf24;
}

.status-completed { 
    background: linear-gradient(135deg, #d1fae5, #a7f3d0); 
    color: #059669; 
    border: 1px solid #34d399;
}

.status-cancelled { 
    background: linear-gradient(135deg, #fee2e2, #fecaca); 
    color: #dc2626; 
    border: 1px solid #f87171;
}

/* Priority Colors */
.priority-low { 
    background: linear-gradient(135deg, #d1fae5, #a7f3d0); 
    color: #059669; 
    border: 1px solid #34d399;
}

.priority-medium { 
    background: linear-gradient(135deg, #fef3c7, #fde68a); 
    color: #d97706; 
    border: 1px solid #fbbf24;
}

.priority-high { 
    background: linear-gradient(135deg, #fed7aa, #fdba74); 
    color: #ea580c; 
    border: 1px solid #fb923c;
}

.priority-urgent { 
    background: linear-gradient(135deg, #fee2e2, #fecaca); 
    color: #dc2626; 
    border: 1px solid #f87171;
    animation: urgentPulse 2s ease-in-out infinite;
}

@keyframes urgentPulse {
    0%, 100% { box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3); }
    50% { box-shadow: 0 4px 16px rgba(220, 38, 38, 0.5); }
}

/* Description Box */
.description-box {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
    margin: 20px 0;
    min-height: 120px;
    position: relative;
}

.description-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563eb, #f59e0b);
    border-radius: 12px 12px 0 0;
}

.description-text {
    font-size: 14px;
    line-height: 1.7;
    color: #374151;
    white-space: pre-wrap;
}

/* Notes Section */
.notes-section {
    background: linear-gradient(135deg, #fffbeb, #fef3c7);
    border: 1px solid #fbbf24;
    border-radius: 12px;
    padding: 24px;
    margin-top: 30px;
    position: relative;
}

.notes-section::before {
    content: '💡';
    position: absolute;
    top: -12px;
    left: 20px;
    background: #fbbf24;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.notes-title {
    font-size: 16px;
    font-weight: 600;
    color: #92400e;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notes-content {
    min-height: 80px;
    border: 1px dashed #fbbf24;
    border-radius: 8px;
    padding: 16px;
    background: white;
    color: #6b7280;
    font-style: italic;
    line-height: 1.6;
}

/* Footer */
.document-footer {
    position: absolute;
    bottom: 20mm;
    left: 20mm;
    right: 20mm;
    border-top: 2px solid #e5e7eb;
    padding-top: 20px;
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 20px;
    align-items: center;
    font-size: 11px;
    color: #6b7280;
    background: white;
}

.footer-left, .footer-right {
    line-height: 1.4;
}

.footer-center {
    text-align: center;
    font-weight: 600;
    color: #374151;
    padding: 8px 16px;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.footer-right {
    text-align: right;
}

.footer-highlight {
    color: #2563eb;
    font-weight: 600;
}

/* Print Optimizations */
@media print {
    body { 
        -webkit-print-color-adjust: exact; 
        print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .professional-document { 
        box-shadow: none; 
        margin: 0; 
        padding: 15mm;
        border-radius: 0;
    }
    
    @page { 
        margin: 0; 
        size: A4;
    }
    
    .info-card:hover {
        transform: none;
        box-shadow: 0 2px 8px rgba(37, 99, 235, 0.1);
    }
    
    .company-logo::before {
        animation: none;
    }
    
    .priority-urgent {
        animation: none;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .professional-document { 
        padding: 15mm; 
        margin: 10px;
    }
    
    .info-grid { 
        grid-template-columns: 1fr; 
        gap: 15px; 
    }
    
    .document-header { 
        flex-direction: column; 
        gap: 20px; 
    }
    
    .company-branding { 
        align-items: center; 
        justify-content: center;
        text-align: center;
    }
    
    .document-meta { 
        text-align: center; 
    }
    
    .main-title {
        font-size: 28px;
    }
    
    .document-footer {
        grid-template-columns: 1fr;
        gap: 10px;
        text-align: center;
    }
    
    .footer-left, .footer-right {
        text-align: center;
    }
}
