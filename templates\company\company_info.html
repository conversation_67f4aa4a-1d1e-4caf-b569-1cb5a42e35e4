{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-building text-primary me-2"></i>
                        Informations de l'Entreprise
                    </h3>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <!-- Informations de base -->
                            <div class="col-md-6">
                                <h5 class="mb-3 text-primary">
                                    <i class="fas fa-info-circle me-2"></i>Informations de Base
                                </h5>
                                
                                <div class="mb-3">
                                    {{ form.nom_entreprise.label(class="form-label") }}
                                    {{ form.nom_entreprise(class="form-control") }}
                                    {% if form.nom_entreprise.errors %}
                                        <div class="text-danger">{{ form.nom_entreprise.errors[0] }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.raison_sociale.label(class="form-label") }}
                                    {{ form.raison_sociale(class="form-control") }}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.secteur_activite.label(class="form-label") }}
                                    {{ form.secteur_activite(class="form-control") }}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.forme_juridique.label(class="form-label") }}
                                    {{ form.forme_juridique(class="form-select") }}
                                </div>
                            </div>
                            
                            <!-- Informations de contact -->
                            <div class="col-md-6">
                                <h5 class="mb-3 text-primary">
                                    <i class="fas fa-address-book me-2"></i>Informations de Contact
                                </h5>
                                
                                <div class="mb-3">
                                    {{ form.adresse.label(class="form-label") }}
                                    {{ form.adresse(class="form-control") }}
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.ville.label(class="form-label") }}
                                            {{ form.ville(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.code_postal.label(class="form-label") }}
                                            {{ form.code_postal(class="form-control") }}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.pays.label(class="form-label") }}
                                    {{ form.pays(class="form-control") }}
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.telephone.label(class="form-label") }}
                                            {{ form.telephone(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.fax.label(class="form-label") }}
                                            {{ form.fax(class="form-control") }}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.email.label(class="form-label") }}
                                            {{ form.email(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.site_web.label(class="form-label") }}
                                            {{ form.site_web(class="form-control") }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="row">
                            <!-- Informations légales -->
                            <div class="col-md-6">
                                <h5 class="mb-3 text-primary">
                                    <i class="fas fa-gavel me-2"></i>Informations Légales
                                </h5>
                                
                                <div class="mb-3">
                                    {{ form.numero_registre_commerce.label(class="form-label") }}
                                    {{ form.numero_registre_commerce(class="form-control") }}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.numero_ice.label(class="form-label") }}
                                    {{ form.numero_ice(class="form-control") }}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.numero_if.label(class="form-label") }}
                                    {{ form.numero_if(class="form-control") }}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.numero_cnss.label(class="form-label") }}
                                    {{ form.numero_cnss(class="form-control") }}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.numero_patente.label(class="form-label") }}
                                    {{ form.numero_patente(class="form-control") }}
                                </div>
                            </div>
                            
                            <!-- Capital et banque -->
                            <div class="col-md-6">
                                <h5 class="mb-3 text-primary">
                                    <i class="fas fa-university me-2"></i>Capital et Banque
                                </h5>
                                
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            {{ form.capital_social.label(class="form-label") }}
                                            {{ form.capital_social(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form.devise_capital.label(class="form-label") }}
                                            {{ form.devise_capital(class="form-select") }}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.banque_principale.label(class="form-label") }}
                                    {{ form.banque_principale(class="form-control") }}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.numero_compte_bancaire.label(class="form-label") }}
                                    {{ form.numero_compte_bancaire(class="form-control") }}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.rib.label(class="form-label") }}
                                    {{ form.rib(class="form-control") }}
                                </div>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="row">
                            <!-- Fichiers et logos -->
                            <div class="col-md-6">
                                <h5 class="mb-3 text-primary">
                                    <i class="fas fa-images me-2"></i>Logos et Fichiers
                                </h5>
                                
                                <div class="mb-3">
                                    {{ form.logo.label(class="form-label") }}
                                    {{ form.logo(class="form-control") }}
                                    {% if company and company.logo_path %}
                                        <div class="mt-2">
                                            <img src="{{ url_for('static', filename='uploads/' + company.logo_path) }}" 
                                                 alt="Logo actuel" class="img-thumbnail" style="max-height: 100px;">
                                            <small class="text-muted d-block">Logo actuel</small>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.signature.label(class="form-label") }}
                                    {{ form.signature(class="form-control") }}
                                    {% if company and company.signature_path %}
                                        <div class="mt-2">
                                            <img src="{{ url_for('static', filename='uploads/' + company.signature_path) }}" 
                                                 alt="Signature actuelle" class="img-thumbnail" style="max-height: 80px;">
                                            <small class="text-muted d-block">Signature actuelle</small>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.cachet.label(class="form-label") }}
                                    {{ form.cachet(class="form-control") }}
                                    {% if company and company.cachet_path %}
                                        <div class="mt-2">
                                            <img src="{{ url_for('static', filename='uploads/' + company.cachet_path) }}" 
                                                 alt="Cachet actuel" class="img-thumbnail" style="max-height: 80px;">
                                            <small class="text-muted d-block">Cachet actuel</small>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Pied de page -->
                            <div class="col-md-6">
                                <h5 class="mb-3 text-primary">
                                    <i class="fas fa-file-alt me-2"></i>Pied de Page et Mentions
                                </h5>
                                
                                <div class="mb-3">
                                    {{ form.pied_de_page.label(class="form-label") }}
                                    {{ form.pied_de_page(class="form-control") }}
                                    <small class="form-text text-muted">
                                        Ce texte apparaîtra en bas de tous les documents imprimés
                                    </small>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.mentions_legales.label(class="form-label") }}
                                    {{ form.mentions_legales(class="form-control") }}
                                    <small class="form-text text-muted">
                                        Mentions légales et informations complémentaires
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Enregistrer les Informations
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.img-thumbnail {
    border: 2px solid #dee2e6;
    border-radius: 8px;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.text-primary {
    color: #0d6efd !important;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}
</style>
{% endblock %}
