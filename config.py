import os
from dotenv import load_dotenv

# Chargement des variables d'environnement
load_dotenv()

class Config:
    # Configuration générale
    SECRET_KEY = os.environ.get('SECRET_KEY', 'default-secret-key-for-gestion-pointages-2024')

    # Configuration CSRF
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None  # Pas de limite de temps pour CSRF

    # Configuration de la base de données
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Configuration de Babel pour l'internationalisation
    BABEL_DEFAULT_LOCALE = os.environ.get('BABEL_DEFAULT_LOCALE', 'fr')
    BABEL_DEFAULT_TIMEZONE = os.environ.get('BABEL_DEFAULT_TIMEZONE', 'Africa/Casablanca')
    
    # Configuration pour le téléchargement de fichiers
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static/uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16 MB max upload
    
    # Configuration pour la devise (MAD - Dirham marocain)
    CURRENCY_CODE = 'MAD'
    CURRENCY_SYMBOL = 'DH'
    
    # Autres configurations
    DEBUG = os.environ.get('FLASK_ENV') == 'development'
