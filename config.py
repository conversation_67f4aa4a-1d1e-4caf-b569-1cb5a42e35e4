import os
from dotenv import load_dotenv

# Chargement des variables d'environnement
load_dotenv()

# Importer la configuration réseau
try:
    from config_network import network_config
except ImportError:
    network_config = None

class Config:
    # Configuration générale
    SECRET_KEY = os.environ.get('SECRET_KEY', 'default-secret-key-for-gestion-pointages-2024')

    # Configuration CSRF
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None  # Pas de limite de temps pour CSRF

    # Configuration de la base de données
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'connect_args': {
            'check_same_thread': False,
            'timeout': 20
        }
    }
    
    # Configuration de Babel pour l'internationalisation
    BABEL_DEFAULT_LOCALE = os.environ.get('BABEL_DEFAULT_LOCALE', 'fr')
    BABEL_DEFAULT_TIMEZONE = os.environ.get('BABEL_DEFAULT_TIMEZONE', 'Africa/Casablanca')
    
    # Configuration pour le téléchargement de fichiers
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static/uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16 MB max upload
    
    # Configuration pour la devise (MAD - Dirham marocain)
    CURRENCY_CODE = 'MAD'
    CURRENCY_SYMBOL = 'DH'
    
    # Configuration réseau
    @staticmethod
    def get_server_config():
        """Obtenir la configuration du serveur"""
        if network_config:
            return network_config.get_server_config()
        else:
            return {
                'host': '127.0.0.1',
                'port': 5000,
                'debug': False,
                'threaded': True
            }

    # Configuration pour l'environnement standalone
    STANDALONE_MODE = True
    NETWORK_ENABLED = True if network_config else False

    # Autres configurations
    DEBUG = os.environ.get('FLASK_ENV') == 'development'

    # Configuration pour l'application standalone
    APP_NAME = "Gestion des Pointages"
    APP_VERSION = "1.0.0"
    COMPANY_NAME = "Système de Gestion des Pointages"

    # Configuration de sécurité pour l'environnement réseau
    SESSION_COOKIE_SECURE = False  # HTTP pour réseau local
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = 3600  # 1 heure
