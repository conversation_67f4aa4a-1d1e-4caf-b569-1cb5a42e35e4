@echo off
echo ========================================
echo   Construction Systeme Gestion Pointages
echo ========================================
echo.

echo Installation des dependances...
pip install pyinstaller==5.13.2

echo.
echo Nettoyage des repertoires precedents...
if exist "dist" rmdir /s /q dist
if exist "build" rmdir /s /q build
if exist "__pycache__" rmdir /s /q __pycache__

echo.
echo Construction directe avec PyInstaller...
pyinstaller --onefile --console --name=GestionPointages --add-data="templates;templates" --add-data="static;static" --add-data="instance;instance" direct_start.py

echo.
echo Construction terminee!
echo.
echo Fichier genere:
echo - dist\GestionPointages.exe
echo.
echo Pour tester:
echo 1. Allez dans le dossier dist
echo 2. Executez GestionPointages.exe
echo 3. Le navigateur s'ouvrira automatiquement
echo.

pause
