@echo off
title Gestion des Pointages - Serveur
color 0A

echo.
echo ========================================
echo    GESTION DES POINTAGES - SERVEUR
echo ========================================
echo.

echo 🔍 Verification de l'environnement...

REM Activer l'environnement virtuel s'il existe
if exist ".venv\Scripts\activate.bat" (
    echo ✅ Activation de l'environnement virtuel...
    call .venv\Scripts\activate.bat
) else (
    echo ⚠️  Environnement virtuel non trouve
)

echo.
echo 🚀 Demarrage du serveur...
echo.
echo 💡 INFORMATIONS DE CONNEXION:
echo    URL: http://127.0.0.1:5001
echo    Utilisateur: admin
echo    Mot de passe: admin
echo.
echo ⚠️  Appuyez sur Ctrl+C pour arreter le serveur
echo ========================================
echo.

REM Démarrer le serveur
python simple_start.py

echo.
echo ========================================
echo    SERVEUR ARRETE
echo ========================================
pause
