#!/usr/bin/env python3
"""
Script de démarrage fixé pour Gestion des Pointages
"""
import os
import sys

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🚀 GESTION DES POINTAGES - DÉMARRAGE")
    print("=" * 50)
    
    try:
        # Import et création de l'application
        print("📦 Chargement de l'application...")
        from app import create_app, create_tables
        
        app = create_app()
        print("✅ Application créée avec succès")
        
        # Création des tables
        print("🗄️  Initialisation de la base de données...")
        create_tables(app)
        print("✅ Base de données initialisée")
        
        # Informations de connexion
        print("\n" + "=" * 50)
        print("🌐 SERVEUR DÉMARRÉ AVEC SUCCÈS!")
        print("=" * 50)
        print("📍 URL: http://127.0.0.1:5001")
        print("👤 Utilisateur: admin")
        print("🔑 Mot de passe: admin")
        print("=" * 50)
        print("⏹️  Appuyez sur Ctrl+C pour arrêter le serveur")
        print("=" * 50)
        
        # Démarrage du serveur
        app.run(
            host='127.0.0.1',
            port=5001,
            debug=True,
            use_reloader=False  # Éviter les problèmes de redémarrage
        )
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        print("\n💡 Solutions possibles:")
        print("   1. Vérifiez que tous les modules sont installés")
        print("   2. Exécutez: pip install -r requirements.txt")
        
    except OSError as e:
        if "Address already in use" in str(e):
            print("❌ Le port 5001 est déjà utilisé")
            print("\n💡 Solutions:")
            print("   1. Fermez l'autre application qui utilise le port 5001")
            print("   2. Ou changez le port dans ce script")
        else:
            print(f"❌ Erreur système: {e}")
            
    except KeyboardInterrupt:
        print("\n🛑 Serveur arrêté par l'utilisateur")
        
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("\n👋 Au revoir!")
        input("Appuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
