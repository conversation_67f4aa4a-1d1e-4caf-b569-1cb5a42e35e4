<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Imprimer - {{ task.title }}</title>
    <style>
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; padding: 20px; }
            .page-break { page-break-before: always; }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 28px;
        }
        
        .header p {
            color: #6c757d;
            margin: 5px 0 0 0;
            font-size: 14px;
        }
        
        .task-info {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-left: 4px solid #007bff;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-item {
            margin-bottom: 15px;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .info-value {
            color: #212529;
            font-size: 16px;
        }
        
        .description-section {
            margin-bottom: 25px;
        }
        
        .description-content {
            background: white;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            white-space: pre-wrap;
            min-height: 100px;
        }
        
        .status-badge, .priority-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-new { background: #6c757d; color: white; }
        .status-in_progress { background: #ffc107; color: #000; }
        .status-completed { background: #28a745; color: white; }
        .status-cancelled { background: #dc3545; color: white; }
        
        .priority-low { background: #e9ecef; color: #495057; }
        .priority-normal { background: #17a2b8; color: white; }
        .priority-high { background: #fd7e14; color: white; }
        .priority-urgent { background: #dc3545; color: white; }
        
        .attachment-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 25px;
            border: 1px solid #bbdefb;
        }
        
        .attachment-icon {
            font-size: 24px;
            color: #1976d2;
            margin-right: 10px;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 12px;
        }
        
        .print-controls {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 0 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        .tags {
            margin-top: 10px;
        }
        
        .tag {
            background: #e9ecef;
            color: #495057;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 5px;
            display: inline-block;
        }
        
        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="no-print print-controls">
        <button class="btn btn-primary" onclick="window.print()">🖨️ Imprimer</button>
        <button class="btn btn-secondary" onclick="window.close()">❌ Fermer</button>
    </div>

    <div class="header">
        <h1>{{ task.title }}</h1>
        <p>Gestion des Pointages - Détails de la Tâche #{{ task.id }}</p>
    </div>

    <div class="task-info">
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">ID de la tâche</div>
                <div class="info-value">#{{ task.id }}</div>
            </div>

            <div class="info-item">
                <div class="info-label">Date de création</div>
                <div class="info-value">{{ task.created_at.strftime('%d/%m/%Y à %H:%M') if task.created_at else 'Non définie' }}</div>
            </div>

            <div class="info-item">
                <div class="info-label">Statut</div>
                <div class="info-value">
                    {% if task.status == 'new' %}
                    <span class="status-badge status-new">Nouvelle</span>
                    {% elif task.status == 'in_progress' %}
                    <span class="status-badge status-in_progress">En cours</span>
                    {% elif task.status == 'completed' %}
                    <span class="status-badge status-completed">Terminée</span>
                    {% else %}
                    <span class="status-badge status-cancelled">Annulée</span>
                    {% endif %}
                </div>
            </div>

            <div class="info-item">
                <div class="info-label">Priorité</div>
                <div class="info-value">
                    {% if task.priority == 'low' %}
                    <span class="priority-badge priority-low">Faible</span>
                    {% elif task.priority == 'normal' %}
                    <span class="priority-badge priority-normal">Normale</span>
                    {% elif task.priority == 'high' %}
                    <span class="priority-badge priority-high">Élevée</span>
                    {% else %}
                    <span class="priority-badge priority-urgent">Urgente</span>
                    {% endif %}
                </div>
            </div>

            <div class="info-item">
                <div class="info-label">Date d'échéance</div>
                <div class="info-value">{{ task.due_date.strftime('%d/%m/%Y') if task.due_date else 'Non définie' }}</div>
            </div>

            <div class="info-item">
                <div class="info-label">Catégorie</div>
                <div class="info-value">{{ task.category or 'Aucune catégorie' }}</div>
            </div>
        </div>

        {% if task.tags %}
        <div class="info-item">
            <div class="info-label">Tags</div>
            <div class="info-value">
                <div class="tags">
                    {% for tag in task.tags.split(',') %}
                    <span class="tag">{{ tag.strip() }}</span>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="description-section">
        <div class="info-label">Description</div>
        <div class="description-content">
            {% if task.description %}
                {{ task.description }}
            {% else %}
                <em style="color: #6c757d;">Aucune description fournie</em>
            {% endif %}
        </div>
    </div>

    {% if task.attachment_path %}
    <div class="attachment-section">
        <div class="info-label">Fichier joint</div>
        <div style="display: flex; align-items: center; margin-top: 10px;">
            <span class="attachment-icon">📎</span>
            <div>
                <strong>{{ task.attachment_path.split('/')[-1] }}</strong><br>
                <small style="color: #6c757d;">Ajouté le {{ task.created_at.strftime('%d/%m/%Y') if task.created_at else 'Date inconnue' }}</small>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="footer">
        <p>Document généré le {{ moment().strftime('%d/%m/%Y à %H:%M') if moment else '' }}</p>
        <p><strong>Gestion des Pointages</strong> - Système de Gestion des Tâches</p>
    </div>

    <script>
        // Auto-print si demandé
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('autoprint') === 'true') {
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            };
        }
    </script>
</body>
</html>
