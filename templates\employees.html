{% extends "base_simple.html" %}

{% block title %}Employés - Gestion des Pointages{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-users"></i> Gestion des Employés</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
            <i class="fas fa-user-plus"></i> Ajouter un employé
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-list"></i> Liste des Employés</h5>
    </div>
    <div class="card-body">
        {% if employees %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Nom complet</th>
                        <th>CIN</th>
                        <th>Poste</th>
                        <th>Salaire (MAD)</th>
                        <th>Téléphone</th>
                        <th>Email</th>
                        <th>Date d'embauche</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr>
                        <td>{{ employee.id }}</td>
                        <td>
                            <strong>{{ employee.first_name }} {{ employee.last_name }}</strong>
                        </td>
                        <td>{{ employee.cin or '-' }}</td>
                        <td>{{ employee.position or '-' }}</td>
                        <td>
                            {% if employee.salary %}
                                {{ "%.2f"|format(employee.salary) }} MAD
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>{{ employee.phone or '-' }}</td>
                        <td>{{ employee.email or '-' }}</td>
                        <td>{{ employee.hire_date or '-' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" title="Imprimer">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucun employé trouvé</h5>
            <p class="text-muted">Commencez par ajouter votre premier employé.</p>
            <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Ajouter un employé
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
