{% extends "base.html" %}

{% block title %}Encaissements - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3"><i class="fas fa-money-bill-wave me-2"></i>Encaissements</h1>
        <p class="text-muted">Gérez les entrées d'argent et suivez les encaissements.</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('finance.reports') }}" class="btn btn-secondary">
            <i class="fas fa-chart-bar me-1"></i> Rapports financiers
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Nouvel encaissement</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('finance.income') }}">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        <label for="date" class="form-label">{{ form.date.label }}</label>
                        {{ form.date(class="form-control", type="date") }}
                        {% for error in form.date.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">{{ form.description.label }}</label>
                        {{ form.description(class="form-control", rows=3, placeholder="Description de l'encaissement") }}
                        {% for error in form.description.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="amount" class="form-label">{{ form.amount.label }}</label>
                        <div class="input-group">
                            {{ form.amount(class="form-control", placeholder="Montant") }}
                            <span class="input-group-text">MAD</span>
                        </div>
                        {% for error in form.amount.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    {{ form.transaction_type(style="display: none;") }}
                    
                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-success") }}
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations</h5>
            </div>
            <div class="card-body">
                <p>Les encaissements représentent toutes les entrées d'argent dans votre organisation.</p>
                <p>Exemples d'encaissements :</p>
                <ul>
                    <li>Paiements clients</li>
                    <li>Subventions</li>
                    <li>Dons</li>
                    <li>Remboursements</li>
                </ul>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> Tous les montants sont en Dirhams Marocains (MAD).
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Liste des encaissements</h5>
                    </div>
                    <div class="col-md-6">
                        <input type="text" id="table-filter" class="form-control" placeholder="Rechercher...">
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped filterable-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Description</th>
                                <th class="text-end">Montant (MAD)</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in transactions %}
                            <tr>
                                <td>{{ transaction.date.strftime('%d/%m/%Y') }}</td>
                                <td>{{ transaction.description }}</td>
                                <td class="text-end">{{ transaction.amount|round(2) }} MAD</td>
                                <td>
                                    <form action="{{ url_for('finance.delete_transaction', id=transaction.id) }}" method="POST" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-danger delete-confirm" data-bs-toggle="tooltip" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center">Aucun encaissement trouvé</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-primary">
                                <th colspan="2" class="text-end">Total :</th>
                                <th class="text-end">{{ total|round(2) }} MAD</th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
