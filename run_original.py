"""
تشغيل البرنامج الأصلي مباشرة
بدون واجهات إضافية
"""

import os
import sys
import time
import threading
import webbrowser
import subprocess

# إعداد المسار
if getattr(sys, 'frozen', False):
    application_path = os.path.dirname(sys.executable)
    os.chdir(application_path)
else:
    application_path = os.path.dirname(os.path.abspath(__file__))
    os.chdir(application_path)

sys.path.insert(0, application_path)

def setup_environment():
    """إعداد البيئة"""
    directories = ['instance', 'logs', 'backups', 'uploads']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def fix_database():
    """إصلاح قاعدة البيانات"""
    print("تصحيح قاعدة البيانات...")
    try:
        result = subprocess.run([sys.executable, 'fix_database_final.py'],
                              capture_output=True, text=True, cwd=application_path)
        if result.returncode == 0:
            print("تم تصحيح قاعدة البيانات")
            return True
        else:
            print(f"خطأ في الإصلاح: {result.stderr}")
            return False
    except Exception as e:
        print(f"خطأ في تشغيل الإصلاح: {e}")
        return False

def open_browser():
    """فتح المتصفح"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5001')
        print("تم فتح المتصفح")
    except:
        print("افتح المتصفح يدوياً: http://localhost:5001")

def start_app():
    """تشغيل التطبيق"""
    try:
        from start import main
        main()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        try:
            from app import create_app
            app = create_app()
            app.run(host='127.0.0.1', port=5001, debug=False, use_reloader=False)
        except Exception as e2:
            print(f"خطأ في التشغيل البديل: {e2}")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("نظام إدارة الحضور والانصراف")
    print("=" * 60)
    print()

    # إعداد البيئة
    print("إعداد البيئة...")
    setup_environment()

    # إصلاح قاعدة البيانات
    if not os.path.exists('instance/app.db'):
        if not fix_database():
            print("فشل في إصلاح قاعدة البيانات")
            input("اضغط Enter للإغلاق...")
            return

    print("بدء تشغيل الخادم...")
    print("الرابط: http://localhost:5001")
    print("المستخدم: admin")
    print("كلمة المرور: admin")
    print("سيتم فتح المتصفح تلقائياً...")
    print("-" * 60)

    # فتح المتصفح
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()

    # تشغيل التطبيق
    start_app()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\nتم إيقاف الخادم")
    except Exception as e:
        print(f"خطأ: {e}")
        input("اضغط Enter للإغلاق...")
