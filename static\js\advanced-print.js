/**
 * Advanced Print System for Gestion des Pointages
 * Professional document generation with modern design
 */

class AdvancedPrintSystem {
    constructor() {
        this.companyInfo = null;
        this.templates = new Map();
        this.init();
    }

    async init() {
        await this.loadCompanyInfo();
        this.initializeTemplates();
    }

    async loadCompanyInfo() {
        try {
            const response = await fetch('/api/company-info');
            const data = await response.json();
            this.companyInfo = data.company;
        } catch (error) {
            console.error('Erreur lors du chargement des informations de l\'entreprise:', error);
            this.companyInfo = this.getDefaultCompanyInfo();
        }
    }

    getDefaultCompanyInfo() {
        return {
            name: 'GESTION DES POINTAGES',
            address: 'Adresse de l\'entreprise',
            phone: '+212 XXX XXX XXX',
            email: '<EMAIL>',
            website: 'www.entreprise.ma',
            logo_path: null,
            footer_text: 'Document généré automatiquement par le système de gestion'
        };
    }

    initializeTemplates() {
        this.templates.set('task', this.createTaskTemplate());
        this.templates.set('employee', this.createEmployeeTemplate());
        this.templates.set('report', this.createReportTemplate());
    }

    createTaskTemplate() {
        return `
        <!DOCTYPE html>
        <html lang="fr">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{DOCUMENT_TITLE}}</title>
            <link rel="stylesheet" href="/static/css/professional-print.css">
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
                
                body {
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: #f8fafc;
                    color: #1a1a1a;
                }

                .professional-document {
                    max-width: 210mm;
                    margin: 0 auto;
                    background: white;
                    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
                    border-radius: 12px;
                    overflow: hidden;
                    position: relative;
                }

                .document-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    position: relative;
                    overflow: hidden;
                }

                .document-header::before {
                    content: '';
                    position: absolute;
                    top: -50%;
                    right: -50%;
                    width: 200%;
                    height: 200%;
                    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
                    animation: headerGlow 4s ease-in-out infinite;
                }

                @keyframes headerGlow {
                    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.5; }
                    50% { transform: scale(1.1) rotate(180deg); opacity: 0.8; }
                }

                .header-content {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    position: relative;
                    z-index: 2;
                }

                .company-section {
                    display: flex;
                    align-items: center;
                    gap: 20px;
                }

                .company-logo-container {
                    width: 80px;
                    height: 80px;
                    background: rgba(255,255,255,0.2);
                    border-radius: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.3);
                    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                }

                .company-logo-text {
                    font-size: 32px;
                    font-weight: 700;
                    color: white;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                }

                .company-details {
                    flex: 1;
                }

                .company-name {
                    font-size: 28px;
                    font-weight: 700;
                    margin-bottom: 8px;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                }

                .company-info {
                    font-size: 14px;
                    opacity: 0.9;
                    line-height: 1.5;
                }

                .document-meta {
                    text-align: right;
                    font-size: 12px;
                }

                .doc-type-badge {
                    background: rgba(255,255,255,0.2);
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                    margin-bottom: 8px;
                    display: inline-block;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.3);
                }

                .document-body {
                    padding: 40px;
                }

                .document-title {
                    text-align: center;
                    margin-bottom: 40px;
                    position: relative;
                }

                .main-title {
                    font-size: 36px;
                    font-weight: 700;
                    color: #1a1a1a;
                    margin-bottom: 12px;
                    position: relative;
                    display: inline-block;
                }

                .main-title::after {
                    content: '';
                    position: absolute;
                    bottom: -8px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 100px;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea, #764ba2);
                    border-radius: 2px;
                }

                .subtitle {
                    color: #6b7280;
                    font-size: 16px;
                    margin-top: 20px;
                }

                .content-section {
                    margin-bottom: 40px;
                }

                .section-header {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    margin-bottom: 24px;
                    padding-bottom: 12px;
                    border-bottom: 2px solid #e5e7eb;
                    position: relative;
                }

                .section-header::after {
                    content: '';
                    position: absolute;
                    bottom: -2px;
                    left: 0;
                    width: 60px;
                    height: 2px;
                    background: linear-gradient(90deg, #667eea, #764ba2);
                }

                .section-icon {
                    width: 32px;
                    height: 32px;
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 16px;
                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
                }

                .section-title {
                    font-size: 20px;
                    font-weight: 600;
                    color: #1a1a1a;
                }

                .info-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                    gap: 20px;
                    margin-bottom: 30px;
                }

                .info-card {
                    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                    border: 1px solid #e2e8f0;
                    border-radius: 16px;
                    padding: 24px;
                    position: relative;
                    transition: all 0.3s ease;
                    overflow: hidden;
                }

                .info-card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 4px;
                    height: 100%;
                    background: linear-gradient(180deg, #667eea, #764ba2);
                }

                .info-card:hover {
                    transform: translateY(-4px);
                    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
                    border-color: #667eea;
                }

                .info-label {
                    font-size: 12px;
                    font-weight: 600;
                    color: #6b7280;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    margin-bottom: 8px;
                }

                .info-value {
                    font-size: 16px;
                    font-weight: 500;
                    color: #1a1a1a;
                    line-height: 1.4;
                }

                .status-badge, .priority-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;
                    padding: 8px 16px;
                    border-radius: 25px;
                    font-size: 12px;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                    position: relative;
                    overflow: hidden;
                }

                .status-badge::before, .priority-badge::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
                    transition: left 0.6s ease;
                }

                .status-badge:hover::before, .priority-badge:hover::before {
                    left: 100%;
                }

                .status-new { background: linear-gradient(135deg, #dbeafe, #bfdbfe); color: #1d4ed8; }
                .status-progress { background: linear-gradient(135deg, #fef3c7, #fde68a); color: #d97706; }
                .status-completed { background: linear-gradient(135deg, #d1fae5, #a7f3d0); color: #059669; }
                .status-cancelled { background: linear-gradient(135deg, #fee2e2, #fecaca); color: #dc2626; }

                .priority-low { background: linear-gradient(135deg, #d1fae5, #a7f3d0); color: #059669; }
                .priority-medium { background: linear-gradient(135deg, #fef3c7, #fde68a); color: #d97706; }
                .priority-high { background: linear-gradient(135deg, #fed7aa, #fdba74); color: #ea580c; }
                .priority-urgent { 
                    background: linear-gradient(135deg, #fee2e2, #fecaca); 
                    color: #dc2626;
                    animation: urgentPulse 2s ease-in-out infinite;
                }

                @keyframes urgentPulse {
                    0%, 100% { box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3); }
                    50% { box-shadow: 0 8px 24px rgba(220, 38, 38, 0.5); }
                }

                .description-section {
                    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                    border: 1px solid #e2e8f0;
                    border-radius: 16px;
                    padding: 30px;
                    margin: 30px 0;
                    position: relative;
                    overflow: hidden;
                }

                .description-section::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea, #764ba2);
                }

                .description-text {
                    font-size: 15px;
                    line-height: 1.7;
                    color: #374151;
                    white-space: pre-wrap;
                    min-height: 100px;
                }

                .notes-section {
                    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
                    border: 1px solid #fbbf24;
                    border-radius: 16px;
                    padding: 30px;
                    margin-top: 40px;
                    position: relative;
                }

                .notes-section::before {
                    content: '💡';
                    position: absolute;
                    top: -15px;
                    left: 30px;
                    background: #fbbf24;
                    color: white;
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
                }

                .notes-title {
                    font-size: 18px;
                    font-weight: 600;
                    color: #92400e;
                    margin-bottom: 16px;
                }

                .notes-content {
                    min-height: 80px;
                    border: 2px dashed #fbbf24;
                    border-radius: 12px;
                    padding: 20px;
                    background: rgba(255,255,255,0.8);
                    color: #6b7280;
                    font-style: italic;
                    line-height: 1.6;
                    backdrop-filter: blur(5px);
                }

                .document-footer {
                    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                    border-top: 1px solid #e2e8f0;
                    padding: 30px 40px;
                    display: grid;
                    grid-template-columns: 1fr auto 1fr;
                    gap: 30px;
                    align-items: center;
                    font-size: 12px;
                    color: #6b7280;
                }

                .footer-section {
                    line-height: 1.5;
                }

                .footer-center {
                    text-align: center;
                    background: white;
                    padding: 16px 24px;
                    border-radius: 12px;
                    border: 1px solid #e2e8f0;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                }

                .footer-center strong {
                    color: #374151;
                    font-weight: 600;
                }

                .footer-right {
                    text-align: right;
                }

                .footer-highlight {
                    color: #667eea;
                    font-weight: 600;
                }

                @media print {
                    body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
                    .professional-document { box-shadow: none; margin: 0; border-radius: 0; }
                    @page { margin: 15mm; size: A4; }
                    .document-header::before { animation: none; }
                    .priority-urgent { animation: none; }
                    .info-card:hover { transform: none; box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1); }
                }

                @media (max-width: 768px) {
                    .professional-document { margin: 10px; border-radius: 8px; }
                    .document-body { padding: 20px; }
                    .info-grid { grid-template-columns: 1fr; gap: 15px; }
                    .header-content { flex-direction: column; gap: 20px; text-align: center; }
                    .document-meta { text-align: center; }
                    .document-footer { grid-template-columns: 1fr; gap: 20px; text-align: center; }
                    .footer-right { text-align: center; }
                }
            </style>
        </head>
        <body>
            <div class="professional-document">
                <div class="document-header">
                    <div class="header-content">
                        <div class="company-section">
                            <div class="company-logo-container">
                                <div class="company-logo-text">{{COMPANY_LOGO}}</div>
                            </div>
                            <div class="company-details">
                                <div class="company-name">{{COMPANY_NAME}}</div>
                                <div class="company-info">
                                    {{COMPANY_ADDRESS}}<br>
                                    Tél: {{COMPANY_PHONE}} | Email: {{COMPANY_EMAIL}}<br>
                                    Site web: {{COMPANY_WEBSITE}}
                                </div>
                            </div>
                        </div>
                        <div class="document-meta">
                            <div class="doc-type-badge">Fiche de Tâche</div>
                            <div>{{CURRENT_DATE}}</div>
                            <div>{{CURRENT_TIME}}</div>
                        </div>
                    </div>
                </div>

                <div class="document-body">
                    <div class="document-title">
                        <h1 class="main-title">FICHE DE TÂCHE</h1>
                        <p class="subtitle">Détails et informations de suivi</p>
                    </div>

                    <div class="content-section">
                        <div class="section-header">
                            <div class="section-icon">📋</div>
                            <h2 class="section-title">Informations Générales</h2>
                        </div>
                        
                        <div class="info-grid">
                            <div class="info-card">
                                <div class="info-label">Titre de la tâche</div>
                                <div class="info-value">{{TASK_TITLE}}</div>
                            </div>
                            
                            <div class="info-card">
                                <div class="info-label">Statut</div>
                                <div class="info-value">
                                    <span class="status-badge {{STATUS_CLASS}}">{{TASK_STATUS}}</span>
                                </div>
                            </div>
                            
                            <div class="info-card">
                                <div class="info-label">Priorité</div>
                                <div class="info-value">
                                    <span class="priority-badge {{PRIORITY_CLASS}}">{{TASK_PRIORITY}}</span>
                                </div>
                            </div>
                            
                            <div class="info-card">
                                <div class="info-label">Date d'échéance</div>
                                <div class="info-value">{{TASK_DUE_DATE}}</div>
                            </div>
                            
                            <div class="info-card">
                                <div class="info-label">Assigné à</div>
                                <div class="info-value">{{TASK_ASSIGNEE}}</div>
                            </div>
                            
                            <div class="info-card">
                                <div class="info-label">Catégorie</div>
                                <div class="info-value">{{TASK_CATEGORY}}</div>
                            </div>
                        </div>
                    </div>

                    <div class="content-section">
                        <div class="section-header">
                            <div class="section-icon">📝</div>
                            <h2 class="section-title">Description Détaillée</h2>
                        </div>
                        
                        <div class="description-section">
                            <div class="description-text">{{TASK_DESCRIPTION}}</div>
                        </div>
                    </div>

                    <div class="notes-section">
                        <div class="notes-title">Notes et Commentaires</div>
                        <div class="notes-content">
                            Espace réservé pour les notes, commentaires et observations supplémentaires...
                        </div>
                    </div>
                </div>

                <div class="document-footer">
                    <div class="footer-section">
                        <strong>{{COMPANY_NAME}}</strong><br>
                        {{COMPANY_ADDRESS}}<br>
                        Tél: {{COMPANY_PHONE}}
                    </div>
                    <div class="footer-center">
                        <strong>{{FOOTER_TEXT}}</strong>
                    </div>
                    <div class="footer-section footer-right">
                        Document généré le:<br>
                        <span class="footer-highlight">{{CURRENT_DATE}} à {{CURRENT_TIME}}</span>
                    </div>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    createEmployeeTemplate() {
        // Template pour les fiches employés (à implémenter)
        return '';
    }

    createReportTemplate() {
        // Template pour les rapports (à implémenter)
        return '';
    }

    generateDocument(type, data) {
        const template = this.templates.get(type);
        if (!template) {
            throw new Error(`Template '${type}' non trouvé`);
        }

        return this.processTemplate(template, data);
    }

    processTemplate(template, data) {
        const currentDate = new Date().toLocaleDateString('fr-FR');
        const currentTime = new Date().toLocaleTimeString('fr-FR');

        const replacements = {
            '{{DOCUMENT_TITLE}}': `Fiche de Tâche - ${data.title || 'Sans titre'}`,
            '{{COMPANY_LOGO}}': this.companyInfo.logo_path ? 
                `<img src="${this.companyInfo.logo_path}" alt="Logo" style="width:100%;height:100%;object-fit:contain;">` : 
                '🏢',
            '{{COMPANY_NAME}}': this.companyInfo.name,
            '{{COMPANY_ADDRESS}}': this.companyInfo.address,
            '{{COMPANY_PHONE}}': this.companyInfo.phone,
            '{{COMPANY_EMAIL}}': this.companyInfo.email,
            '{{COMPANY_WEBSITE}}': this.companyInfo.website,
            '{{CURRENT_DATE}}': currentDate,
            '{{CURRENT_TIME}}': currentTime,
            '{{TASK_TITLE}}': data.title || 'Non défini',
            '{{TASK_STATUS}}': this.formatStatus(data.status),
            '{{STATUS_CLASS}}': this.getStatusClass(data.status),
            '{{TASK_PRIORITY}}': this.formatPriority(data.priority),
            '{{PRIORITY_CLASS}}': this.getPriorityClass(data.priority),
            '{{TASK_DUE_DATE}}': data.dueDate || 'Non définie',
            '{{TASK_ASSIGNEE}}': data.assignedTo || 'Non assigné',
            '{{TASK_CATEGORY}}': data.category || 'Général',
            '{{TASK_DESCRIPTION}}': data.description || 'Aucune description disponible',
            '{{FOOTER_TEXT}}': this.companyInfo.footer_text
        };

        let processedTemplate = template;
        for (const [placeholder, value] of Object.entries(replacements)) {
            processedTemplate = processedTemplate.replace(new RegExp(placeholder, 'g'), value);
        }

        return processedTemplate;
    }

    formatStatus(status) {
        const statusLabels = {
            'new': 'Nouveau',
            'in_progress': 'En cours',
            'completed': 'Terminé',
            'cancelled': 'Annulé'
        };
        return statusLabels[status] || 'Non défini';
    }

    formatPriority(priority) {
        const priorityLabels = {
            'low': 'Faible',
            'medium': 'Moyenne',
            'high': 'Élevée',
            'urgent': 'Urgent'
        };
        return priorityLabels[priority] || 'Moyenne';
    }

    getStatusClass(status) {
        const classes = {
            'new': 'status-new',
            'in_progress': 'status-progress',
            'completed': 'status-completed',
            'cancelled': 'status-cancelled'
        };
        return classes[status] || 'status-new';
    }

    getPriorityClass(priority) {
        const classes = {
            'low': 'priority-low',
            'medium': 'priority-medium',
            'high': 'priority-high',
            'urgent': 'priority-urgent'
        };
        return classes[priority] || 'priority-medium';
    }

    print(type, data) {
        try {
            const documentHTML = this.generateDocument(type, data);
            const printWindow = window.open('', '_blank', 'width=1200,height=800');
            
            printWindow.document.write(documentHTML);
            printWindow.document.close();
            
            // Attendre le chargement complet
            printWindow.onload = function() {
                setTimeout(() => {
                    printWindow.print();
                    // Laisser la fenêtre ouverte pour prévisualisation
                }, 1000);
            };
            
            return true;
        } catch (error) {
            console.error('Erreur lors de la génération du document:', error);
            alert('Erreur lors de la génération du document: ' + error.message);
            return false;
        }
    }
}

// Instance globale
const advancedPrintSystem = new AdvancedPrintSystem();
