#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple pour diagnostiquer les problèmes
"""

import os
import sys
from pathlib import Path

# Configuration du répertoire de travail
if getattr(sys, 'frozen', False):
    application_path = os.path.dirname(sys.executable)
else:
    application_path = os.path.dirname(os.path.abspath(__file__))

os.chdir(application_path)
sys.path.insert(0, application_path)

print(f"Répertoire de travail: {os.getcwd()}")
print(f"Python path: {sys.path[0]}")

try:
    from app import create_app
    print("✅ Import de create_app réussi")
    
    app = create_app()
    print("✅ Création de l'app réussie")
    
    # Lister toutes les routes
    print("\n📋 Routes disponibles:")
    for rule in app.url_map.iter_rules():
        print(f"  {rule.endpoint}: {rule.rule} [{', '.join(rule.methods)}]")
    
    print(f"\n🚀 Démarrage du serveur sur http://localhost:5001")
    app.run(host='0.0.0.0', port=5001, debug=True)
    
except Exception as e:
    print(f"❌ Erreur: {e}")
    import traceback
    traceback.print_exc()
    input("Appuyez sur Entrée pour continuer...")
