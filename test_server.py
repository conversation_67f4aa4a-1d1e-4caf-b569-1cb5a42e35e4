#!/usr/bin/env python3
"""
Test script to start the server with detailed error handling
"""
import sys
import traceback
import os

print("🚀 Starting Gestion des Pointages...")
print(f"Python version: {sys.version}")
print(f"Current directory: {os.getcwd()}")
print("-" * 50)

try:
    print("1. Importing app...")
    from app import app, create_tables
    print("   ✅ App imported successfully")
    
    print("2. Creating tables...")
    create_tables(app)
    print("   ✅ Tables created successfully")
    
    print("3. Starting Flask server...")
    print("📍 URL: http://127.0.0.1:5001")
    print("⏹️  Press Ctrl+C to stop")
    print("-" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5001)
    
except KeyboardInterrupt:
    print("\n🛑 Server stopped by user")
except Exception as e:
    print(f"❌ Error: {e}")
    print("\n📋 Full traceback:")
    traceback.print_exc()
    input("\nPress Enter to close...")
