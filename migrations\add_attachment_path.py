"""
Script de migration pour ajouter la colonne attachment_path à la table tasks
"""
import sqlite3
import os
from config import Config

def run_migration():
    """Exécute la migration pour ajouter la colonne attachment_path à la table tasks"""
    # La base de données est dans le dossier 'instance'
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'instance', 'app.db')

    print(f"Connexion à la base de données: {db_path}")

    # Vérifier si le fichier existe
    if not os.path.exists(db_path):
        print(f"Erreur: Le fichier de base de données {db_path} n'existe pas.")
        return False

    try:
        # Connexion à la base de données
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Vérifier si la colonne existe déjà
        cursor.execute("PRAGMA table_info(tasks)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        if 'attachment_path' in column_names:
            print("La colonne attachment_path existe déjà dans la table tasks.")
            conn.close()
            return True

        # Ajouter la colonne
        cursor.execute("ALTER TABLE tasks ADD COLUMN attachment_path TEXT")

        # Valider les changements
        conn.commit()
        print("Migration réussie: La colonne attachment_path a été ajoutée à la table tasks.")

        # Fermer la connexion
        conn.close()
        return True

    except sqlite3.Error as e:
        print(f"Erreur SQLite: {e}")
        return False
    except Exception as e:
        print(f"Erreur: {e}")
        return False

if __name__ == "__main__":
    run_migration()
