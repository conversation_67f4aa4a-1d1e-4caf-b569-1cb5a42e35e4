{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <!-- En-tête avec boutons d'action -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-edit text-warning me-2"></i>
            Modifier la Tâche
        </h2>
        <div class="btn-group">
            <a href="{{ url_for('task.tasks') }}" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-arrow-left me-1"></i> Retour
            </a>
            <button type="submit" form="task-form" class="btn btn-warning btn-sm">
                <i class="fas fa-save me-1"></i> Mettre à jour
            </button>
        </div>
    </div>

    <!-- Formulaire dans le style du tableau -->
    <div class="task-group mb-4">
        <div class="group-header">
            <div class="d-flex align-items-center">
                <div class="group-indicator bg-warning"></div>
                <h5 class="mb-0 me-3">Modification: {{ task.title }}</h5>
                <span class="badge bg-warning rounded-pill">Édition</span>
            </div>
        </div>
        
        <div class="group-content">
            <form method="POST" enctype="multipart/form-data" id="task-form">
                {{ form.hidden_tag() }}
                
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="20%">Champ</th>
                                <th width="80%">Valeur</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Titre -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-heading me-2 text-primary"></i>
                                    {{ form.title.label.text }}
                                </td>
                                <td>
                                    {{ form.title(class="form-control form-control-sm") }}
                                    {% if form.title.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.title.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Description -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-align-left me-2 text-info"></i>
                                    {{ form.description.label.text }}
                                </td>
                                <td>
                                    {{ form.description(class="form-control form-control-sm", rows="3") }}
                                    {% if form.description.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.description.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Statut -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-flag me-2 text-warning"></i>
                                    {{ form.status.label.text }}
                                </td>
                                <td>
                                    {{ form.status(class="form-select form-select-sm") }}
                                    {% if form.status.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.status.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Priorité -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                                    {{ form.priority.label.text }}
                                </td>
                                <td>
                                    {{ form.priority(class="form-select form-select-sm") }}
                                    {% if form.priority.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.priority.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Date d'échéance -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-calendar me-2 text-success"></i>
                                    {{ form.due_date.label.text }}
                                </td>
                                <td>
                                    {{ form.due_date(class="form-control form-control-sm", type="date") }}
                                    {% if form.due_date.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.due_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Catégorie -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-tag me-2 text-secondary"></i>
                                    {{ form.category.label.text }}
                                </td>
                                <td>
                                    {{ form.category(class="form-control form-control-sm") }}
                                    {% if form.category.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.category.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Assigné à -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-user me-2 text-info"></i>
                                    Assigné à
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-secondary text-white me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <select class="form-select form-select-sm" name="assigned_to">
                                            <option value="">Non assigné</option>
                                            {% if employees %}
                                                {% for employee in employees %}
                                                <option value="{{ employee.id }}" {% if task.assigned_to == employee.id %}selected{% endif %}>
                                                    {{ employee.first_name }} {{ employee.last_name }}
                                                </option>
                                                {% endfor %}
                                            {% endif %}
                                        </select>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- Pièce jointe existante -->
                            {% if task.attachment_path %}
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-paperclip me-2 text-warning"></i>
                                    Pièce jointe actuelle
                                </td>
                                <td>
                                    <a href="{{ url_for('static', filename='uploads/' + task.attachment_path) }}" target="_blank" class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-download me-1"></i>
                                        {{ task.attachment_path.split('/')[-1] }}
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                            
                            <!-- Nouvelle pièce jointe -->
                            {% if form.attachment %}
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-paperclip me-2 text-warning"></i>
                                    {{ form.attachment.label.text }}
                                </td>
                                <td>
                                    {{ form.attachment(class="form-control form-control-sm", type="file") }}
                                    {% if form.attachment.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.attachment.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="text-muted">Formats acceptés: PDF, DOC, DOCX, JPG, PNG (Max: 5MB)</small>
                                </td>
                            </tr>
                            {% endif %}
                            
                            <!-- Actions de communication -->
                            <tr class="table-warning">
                                <td class="fw-medium">
                                    <i class="fas fa-share me-2 text-primary"></i>
                                    Communication
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-success" onclick="sendTaskWhatsApp()">
                                            <i class="fab fa-whatsapp me-1"></i> Envoyer par WhatsApp
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="sendTaskEmail()">
                                            <i class="fas fa-envelope me-1"></i> Envoyer par Email
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="generateTaskPDF()">
                                            <i class="fas fa-file-pdf me-1"></i> Générer PDF
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="printTaskForm()">
                                            <i class="fas fa-print me-1"></i> Imprimer
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block styles %}
<style>
.task-group {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.group-header {
    background: #f8f9fa;
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
}

.group-indicator {
    width: 4px;
    height: 20px;
    border-radius: 2px;
    margin-right: 12px;
}

.group-content {
    background: white;
}

.avatar-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 13px;
    padding: 12px 8px;
}

.table td {
    padding: 12px 8px;
    vertical-align: middle;
    border-top: 1px solid #f1f3f4;
}

.form-control-sm, .form-select-sm {
    border-radius: 6px;
}

.btn-group .btn {
    border-radius: 6px;
    margin-left: 4px;
}

.btn-group .btn:first-child {
    margin-left: 0;
}

.table-warning {
    background-color: #fff3cd;
}
</style>
{% endblock %}

{% block scripts %}
<script>
function sendTaskWhatsApp() {
    // Générer et envoyer la fiche complète de la tâche
    generateTaskPrintSheet('whatsapp');
}

function generateTaskPrintSheet(method) {
    // Récupérer toutes les informations de la tâche
    const title = document.querySelector('input[name="title"]').value;
    const description = document.querySelector('textarea[name="description"]').value;
    const status = document.querySelector('select[name="status"]').value;
    const priority = document.querySelector('select[name="priority"]').value;
    const dueDate = document.querySelector('input[name="due_date"]').value;
    const assignedTo = document.querySelector('select[name="assigned_to"]').value;

    // Créer la fiche de tâche simplifiée
    const taskSheet = `📋 FICHE DE TÂCHE

• Titre: ${title || 'Non défini'}
• Description: ${description || 'Aucune description'}
• Statut: ${getStatusLabel(status)}
• Priorité: ${getPriorityLabel(priority)}
• Assigné à: ${getEmployeeName(assignedTo)}
• Échéance: ${dueDate || 'Non définie'}

📅 INFORMATIONS DE SUIVI:
• Date d'émission: ${new Date().toLocaleDateString('fr-FR')}
• Heure d'émission: ${new Date().toLocaleTimeString('fr-FR')}`;

    if (method === 'whatsapp') {
        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(taskSheet)}`;
        window.open(whatsappUrl, '_blank');
    } else if (method === 'email') {
        const subject = `📋 Fiche de Tâche: ${title}`;
        const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(taskSheet)}`;
        window.location.href = mailtoUrl;
    }
}

function getStatusLabel(status) {
    const labels = {
        'pending': '⏳ En attente',
        'in_progress': '🔄 En cours',
        'completed': '✅ Terminée',
        'cancelled': '❌ Annulée'
    };
    return labels[status] || status;
}

function getPriorityLabel(priority) {
    const labels = {
        'low': '🟢 Faible',
        'medium': '🟡 Moyenne',
        'high': '🟠 Élevée',
        'urgent': '🔴 Urgente'
    };
    return labels[priority] || priority;
}

function getEmployeeName(employeeId) {
    const select = document.querySelector('select[name="assigned_to"]');
    const option = select.querySelector(`option[value="${employeeId}"]`);
    return option ? option.textContent : 'Non assigné';
}

function sendTaskEmail() {
    // Générer et envoyer la fiche complète de la tâche
    generateTaskPrintSheet('email');
}

function generateTaskPDF() {
    window.open(`/task/print/{{ task.id }}`, '_blank');
}

function printTaskForm() {
    // Imprimer la fiche de tâche avec logo et pied de page
    const title = document.querySelector('input[name="title"]').value;
    const description = document.querySelector('textarea[name="description"]').value;
    const status = document.querySelector('select[name="status"]').value;
    const priority = document.querySelector('select[name="priority"]').value;
    const dueDate = document.querySelector('input[name="due_date"]').value;
    const assignedTo = document.querySelector('select[name="assigned_to"]').value;

    const companyInfo = {
        name: 'GESTION DES POINTAGES',
        address: 'Adresse de l\'entreprise',
        phone: '+212 XXX XXX XXX',
        email: '<EMAIL>',
        website: 'www.entreprise.ma',
        footer_text: 'Document généré automatiquement par le système de gestion'
    };

    const printContent = `<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Fiche de Tâche</title>
<style>
body{font-family:Arial,sans-serif;margin:0;padding:20px;font-size:12px;line-height:1.4}
.print-header{display:flex;align-items:center;margin-bottom:20px;padding-bottom:15px;border-bottom:2px solid #333}
.company-logo{margin-right:20px;flex-shrink:0}
.logo-placeholder{font-size:48px;color:#666;width:80px;height:80px;display:flex;align-items:center;justify-content:center;border:2px solid #ddd;border-radius:8px}
.company-info h1{margin:0 0 10px 0;font-size:24px;color:#333;font-weight:bold}
.company-info p{margin:5px 0;color:#666;font-size:11px}
.task-info-table{width:100%;border-collapse:collapse;margin:15px 0}
.task-info-table th,.task-info-table td{border:1px solid #ddd;padding:12px;text-align:left}
.task-info-table th{background-color:#f5f5f5;font-weight:bold;width:30%}
.print-footer{margin-top:30px;padding-top:15px;border-top:2px solid #333;text-align:center}
.footer-details{display:flex;justify-content:space-between;margin-top:15px;font-size:10px}
.footer-left,.footer-center,.footer-right{flex:1}
.footer-left{text-align:left}.footer-center{text-align:center}.footer-right{text-align:right}
.footer-details p{margin:2px 0;color:#666}
@page{margin:2cm;size:A4}
</style></head><body>
<div class="print-header">
<div class="company-logo"><div class="logo-placeholder">🏢</div></div>
<div class="company-info"><h1>${companyInfo.name}</h1><p>${companyInfo.address}</p><p>Tél: ${companyInfo.phone} | Email: ${companyInfo.email}</p><p>Site web: ${companyInfo.website}</p></div>
</div>
<h2 style="text-align:center;margin:20px 0;color:#333">FICHE DE TÂCHE</h2>
<table class="task-info-table">
<tr><th>Titre</th><td>${title || 'Non défini'}</td></tr>
<tr><th>Description</th><td style="white-space:pre-wrap">${description || 'Aucune description'}</td></tr>
<tr><th>Statut</th><td>${getStatusLabel(status)}</td></tr>
<tr><th>Priorité</th><td>${getPriorityLabel(priority)}</td></tr>
<tr><th>Assigné à</th><td>${getEmployeeName(assignedTo)}</td></tr>
<tr><th>Échéance</th><td>${dueDate || 'Non définie'}</td></tr>
</table>
<div style="margin-top:30px;">
<h3>Notes et Commentaires:</h3>
<div style="border:1px solid #ddd;padding:15px;min-height:100px;background-color:#f9f9f9;">
Espace pour notes supplémentaires...
</div>
</div>
<div class="print-footer">
<p><strong>${companyInfo.footer_text}</strong></p>
<div class="footer-details">
<div class="footer-left"><p>${companyInfo.name}</p><p>${companyInfo.address}</p></div>
<div class="footer-center"><p>Tél: ${companyInfo.phone}</p><p>Email: ${companyInfo.email}</p></div>
<div class="footer-right"><p>Document généré le:</p><p>${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}</p></div>
</div></div></body></html>`;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.onload = () => setTimeout(() => { printWindow.print(); printWindow.close(); }, 500);
}
</script>
{% endblock %}
