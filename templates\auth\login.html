{% extends "base.html" %}

{% block title %}Connexion - Gestion des Pointages{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-logo">
        <h1><i class="fas fa-clipboard-check me-2"></i>Gestion des Pointages</h1>
        <p class="text-muted">Système de gestion multi-utilisateurs</p>
    </div>
    
    <div class="card login-card">
        <div class="card-body">
            <h2 class="card-title text-center mb-4">Connexion</h2>
            
            <form method="POST" action="{{ url_for('auth.login') }}">
                {{ form.hidden_tag() }}
                
                <div class="mb-3">
                    <label for="username" class="form-label">{{ form.username.label }}</label>
                    {{ form.username(class="form-control", placeholder="Entrez votre nom d'utilisateur") }}
                    {% for error in form.username.errors %}
                        <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">{{ form.password.label }}</label>
                    {{ form.password(class="form-control", placeholder="Entrez votre mot de passe") }}
                    {% for error in form.password.errors %}
                        <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                
                <div class="mb-3 form-check">
                    {{ form.remember_me(class="form-check-input") }}
                    <label class="form-check-label" for="remember_me">{{ form.remember_me.label }}</label>
                </div>
                
                <div class="d-grid gap-2">
                    {{ form.submit(class="btn btn-primary btn-lg") }}
                </div>

                <div class="text-center mt-3">
                    <a href="{{ url_for('auth.forgot_password') }}" class="text-decoration-none">
                        <i class="fas fa-key me-1"></i>
                        Mot de passe oublié ?
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="text-center mt-3">
        <p class="text-muted">
            Système de gestion interne. Accès réservé au personnel autorisé.
        </p>
    </div>
</div>
{% endblock %}
