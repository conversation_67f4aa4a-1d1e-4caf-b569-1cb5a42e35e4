#!/usr/bin/env python3
"""
Script de démarrage avec correction CSRF
"""
import os
import sys

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🚀 GESTION DES POINTAGES - DÉMARRAGE AVEC CORRECTION CSRF")
    print("=" * 60)
    
    try:
        # Import et création de l'application
        print("📦 Chargement de l'application...")
        from app import create_app, create_tables
        
        app = create_app()
        
        # Configuration CSRF améliorée
        app.config['SECRET_KEY'] = 'gestion-pointages-secret-key-2024-very-secure'
        app.config['WTF_CSRF_ENABLED'] = True
        app.config['WTF_CSRF_TIME_LIMIT'] = None
        app.config['WTF_CSRF_SSL_STRICT'] = False
        
        print("✅ Application créée avec succès")
        
        # Création des tables et utilisateur admin
        print("🗄️  Initialisation de la base de données...")
        create_tables(app)
        print("✅ Base de données initialisée")
        
        # Vérifier le port
        import socket
        port = 5001
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
        except OSError:
            print(f"⚠️  Port {port} occupé, utilisation du port 5002")
            port = 5002
        
        # Informations de connexion
        print("\n" + "=" * 60)
        print("🌐 SERVEUR DÉMARRÉ AVEC SUCCÈS!")
        print("=" * 60)
        print(f"📍 URL: http://127.0.0.1:{port}")
        print("👤 Utilisateur: admin")
        print("🔑 Mot de passe: admin")
        print("=" * 60)
        print("💡 CSRF activé et configuré correctement")
        print("⏹️  Appuyez sur Ctrl+C pour arrêter le serveur")
        print("=" * 60)
        
        # Démarrage du serveur
        app.run(
            host='127.0.0.1',
            port=port,
            debug=True,
            use_reloader=False,
            threaded=True
        )
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        print("\n💡 Solutions possibles:")
        print("   1. Vérifiez que tous les modules sont installés")
        print("   2. Exécutez: pip install -r requirements.txt")
        
    except OSError as e:
        if "Address already in use" in str(e):
            print("❌ Tous les ports sont occupés")
            print("\n💡 Solutions:")
            print("   1. Fermez les autres applications qui utilisent les ports")
            print("   2. Redémarrez votre ordinateur")
        else:
            print(f"❌ Erreur système: {e}")
            
    except KeyboardInterrupt:
        print("\n🛑 Serveur arrêté par l'utilisateur")
        
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("\n👋 Au revoir!")
        input("Appuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
