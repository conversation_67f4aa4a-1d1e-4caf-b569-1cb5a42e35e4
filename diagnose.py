#!/usr/bin/env python3
"""
Script de diagnostic rapide
"""

import os
import sys
import subprocess

def check_python():
    """Vérifier Python"""
    print("🐍 Version Python:", sys.version)
    return True

def check_files():
    """Vérifier les fichiers essentiels"""
    print("\n📁 Vérification des fichiers...")
    
    essential_files = [
        'app.py',
        'run_app.py', 
        'models.py',
        'routes.py',
        'forms.py',
        'extensions.py'
    ]
    
    missing = []
    for file in essential_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing.append(file)
    
    return len(missing) == 0

def check_modules():
    """Vérifier les modules Python"""
    print("\n📦 Vérification des modules...")
    
    modules = [
        'flask',
        'flask_sqlalchemy', 
        'flask_login',
        'flask_wtf',
        'wtforms',
        'werkzeug'
    ]
    
    missing = []
    for module in modules:
        try:
            __import__(module.replace('_', '.'))
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module}")
            missing.append(module)
    
    if missing:
        print(f"\n💡 Pour installer les modules manquants:")
        print(f"pip install {' '.join(missing)}")
    
    return len(missing) == 0

def check_database():
    """Vérifier la base de données"""
    print("\n🗃️  Vérification de la base de données...")
    
    if not os.path.exists('instance'):
        print("⚠️  Dossier instance manquant")
        return False
    
    db_path = os.path.join('instance', 'app.db')
    if os.path.exists(db_path):
        print("✅ Base de données trouvée")
        
        # Vérifier la taille
        size = os.path.getsize(db_path)
        print(f"📊 Taille: {size} bytes")
        
        return True
    else:
        print("❌ Base de données manquante")
        return False

def test_import():
    """Tester l'import de l'application"""
    print("\n🧪 Test d'import de l'application...")
    
    try:
        from app import create_app
        print("✅ Import app réussi")
        
        app = create_app()
        print("✅ Création app réussie")
        
        return True
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def suggest_fix():
    """Suggérer des solutions"""
    print("\n🔧 SOLUTIONS SUGGÉRÉES:")
    print("1. Installer les dépendances:")
    print("   pip install flask flask-sqlalchemy flask-login flask-wtf")
    print()
    print("2. Démarrer le serveur:")
    print("   python start_server.py")
    print("   OU")
    print("   python run_app.py")
    print()
    print("3. Ouvrir dans le navigateur:")
    print("   http://127.0.0.1:5001")
    print()
    print("4. Se connecter avec:")
    print("   Utilisateur: admin")
    print("   Mot de passe: admin")

def main():
    """Fonction principale"""
    print("🔍 DIAGNOSTIC DU SYSTÈME")
    print("=" * 40)
    
    checks = [
        ("Python", check_python),
        ("Fichiers", check_files),
        ("Modules", check_modules),
        ("Base de données", check_database),
        ("Import application", test_import)
    ]
    
    results = []
    for name, check_func in checks:
        print(f"\n📋 {name}:")
        result = check_func()
        results.append((name, result))
    
    # Résumé
    print("\n" + "=" * 40)
    print("📊 RÉSUMÉ:")
    
    all_good = True
    for name, result in results:
        status = "✅ OK" if result else "❌ PROBLÈME"
        print(f"  {name}: {status}")
        if not result:
            all_good = False
    
    if all_good:
        print("\n🎉 TOUT EST OK!")
        print("💡 Vous pouvez démarrer avec: python start_server.py")
    else:
        print("\n⚠️  PROBLÈMES DÉTECTÉS")
        suggest_fix()

if __name__ == "__main__":
    main()
