"""
Démarrage direct du système de gestion des pointages
Sans interface launcher - démarrage automatique du serveur web
"""

import os
import sys
import time
import threading
import webbrowser
from pathlib import Path

# Ajouter le répertoire du projet au path
if getattr(sys, 'frozen', False):
    # Mode exécutable
    application_path = os.path.dirname(sys.executable)
    os.chdir(application_path)
else:
    # Mode développement
    application_path = os.path.dirname(os.path.abspath(__file__))
    os.chdir(application_path)

sys.path.insert(0, application_path)

def setup_environment():
    """Configurer l'environnement d'exécution"""
    # Créer les répertoires nécessaires
    directories = ['instance', 'logs', 'backups', 'uploads']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    # Configurer les variables d'environnement
    os.environ['FLASK_ENV'] = 'production'
    os.environ['FLASK_DEBUG'] = 'False'

def create_database():
    """Créer la base de données si elle n'existe pas"""
    try:
        from app import create_app
        from models import db
        
        app = create_app()
        with app.app_context():
            # Créer toutes les tables
            db.create_all()
            
            # Créer l'utilisateur admin par défaut s'il n'existe pas
            from models import User
            from werkzeug.security import generate_password_hash
            
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin'),
                    role='admin',
                    is_active=True
                )
                db.session.add(admin_user)
                db.session.commit()
                print("✓ Utilisateur admin créé (admin/admin)")
            
            print("✓ Base de données initialisée")
            return True
            
    except Exception as e:
        print(f"✗ Erreur lors de la création de la base de données: {e}")
        return False

def start_flask_server():
    """Démarrer le serveur Flask"""
    try:
        from app import create_app

        app = create_app()

        # Configuration pour la production
        app.config['DEBUG'] = False
        app.config['TESTING'] = False
        app.config['ENV'] = 'production'

        print("🚀 Démarrage du serveur...")
        print("📍 URL: http://localhost:5001")
        print("👤 Connexion: admin / admin")
        print("🔄 Le navigateur va s'ouvrir automatiquement...")
        print("⚠️  Pour arrêter le serveur, fermez cette fenêtre")
        print("-" * 50)

        # Démarrer le serveur
        app.run(
            host='127.0.0.1',  # Changé de 0.0.0.0 à 127.0.0.1 pour plus de sécurité
            port=5001,
            debug=False,
            threaded=True,
            use_reloader=False
        )

    except Exception as e:
        print(f"✗ Erreur lors du démarrage du serveur: {e}")
        print(f"Détails de l'erreur: {str(e)}")
        input("Appuyez sur Entrée pour fermer...")

def open_browser():
    """Ouvrir le navigateur après un délai"""
    time.sleep(3)  # Attendre que le serveur démarre
    try:
        webbrowser.open('http://localhost:5001')
        print("✓ Navigateur ouvert")
    except Exception as e:
        print(f"⚠ Impossible d'ouvrir le navigateur automatiquement: {e}")
        print("📍 Ouvrez manuellement: http://localhost:5001")

def main():
    """Fonction principale"""
    print("=" * 60)
    print("🏢 SYSTÈME DE GESTION DES POINTAGES")
    print("=" * 60)
    print()
    
    # Configurer l'environnement
    print("⚙️ Configuration de l'environnement...")
    setup_environment()
    
    # Créer la base de données
    print("🗄️ Initialisation de la base de données...")
    if not create_database():
        print("✗ Échec de l'initialisation de la base de données")
        input("Appuyez sur Entrée pour fermer...")
        return
    
    # Démarrer le navigateur dans un thread séparé
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    # Démarrer le serveur Flask (bloquant)
    start_flask_server()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur...")
    except Exception as e:
        print(f"✗ Erreur fatale: {e}")
        input("Appuyez sur Entrée pour fermer...")
