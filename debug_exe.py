"""
تشخيص مشاكل الملف التنفيذي
"""

import os
import sys
import traceback

def debug_environment():
    """تشخيص البيئة"""
    print("=" * 60)
    print("تشخيص البيئة")
    print("=" * 60)
    
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print(f"Executable: {sys.executable}")
    print(f"Frozen: {getattr(sys, 'frozen', False)}")
    
    if getattr(sys, 'frozen', False):
        print(f"Bundle dir: {sys._MEIPASS}")
        print(f"Executable dir: {os.path.dirname(sys.executable)}")
    
    print(f"Current dir: {os.getcwd()}")
    print(f"Python path: {sys.path[:3]}...")
    
    print("\nملفات في المجلد الحالي:")
    try:
        files = os.listdir('.')
        for f in files[:10]:  # أول 10 ملفات
            print(f"  {f}")
        if len(files) > 10:
            print(f"  ... و {len(files) - 10} ملف آخر")
    except Exception as e:
        print(f"خطأ في قراءة الملفات: {e}")

def test_imports():
    """اختبار الاستيراد"""
    print("\n" + "=" * 60)
    print("اختبار الاستيراد")
    print("=" * 60)
    
    modules_to_test = [
        'flask', 'flask_sqlalchemy', 'flask_login', 'flask_wtf',
        'wtforms', 'sqlalchemy', 'jinja2', 'werkzeug',
        'sqlite3', 'os', 'sys', 'threading', 'webbrowser'
    ]
    
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError as e:
            print(f"✗ {module}: {e}")
        except Exception as e:
            print(f"? {module}: {e}")

def test_app_files():
    """اختبار ملفات التطبيق"""
    print("\n" + "=" * 60)
    print("اختبار ملفات التطبيق")
    print("=" * 60)
    
    app_files = [
        'app.py', 'models.py', 'routes.py', 'forms.py', 'config.py',
        'extensions.py', 'templates', 'static'
    ]
    
    for file in app_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file} - مفقود")

def test_app_import():
    """اختبار استيراد التطبيق"""
    print("\n" + "=" * 60)
    print("اختبار استيراد التطبيق")
    print("=" * 60)
    
    try:
        print("محاولة استيراد app...")
        from app import create_app
        print("✓ تم استيراد create_app")
        
        print("محاولة إنشاء التطبيق...")
        app = create_app()
        print("✓ تم إنشاء التطبيق")
        
        print("اختبار إعدادات التطبيق...")
        print(f"  SECRET_KEY: {'موجود' if app.config.get('SECRET_KEY') else 'مفقود'}")
        print(f"  SQLALCHEMY_DATABASE_URI: {app.config.get('SQLALCHEMY_DATABASE_URI', 'مفقود')}")
        
        return app
        
    except ImportError as e:
        print(f"✗ خطأ في الاستيراد: {e}")
        traceback.print_exc()
        return None
    except Exception as e:
        print(f"✗ خطأ في إنشاء التطبيق: {e}")
        traceback.print_exc()
        return None

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n" + "=" * 60)
    print("اختبار قاعدة البيانات")
    print("=" * 60)
    
    db_path = 'instance/app.db'
    
    if os.path.exists('instance'):
        print("✓ مجلد instance موجود")
    else:
        print("✗ مجلد instance مفقود")
        try:
            os.makedirs('instance')
            print("✓ تم إنشاء مجلد instance")
        except Exception as e:
            print(f"✗ فشل في إنشاء مجلد instance: {e}")
    
    if os.path.exists(db_path):
        print(f"✓ قاعدة البيانات موجودة: {db_path}")
        size = os.path.getsize(db_path)
        print(f"  الحجم: {size} بايت")
    else:
        print(f"✗ قاعدة البيانات مفقودة: {db_path}")

def main():
    """الدالة الرئيسية"""
    print("تشخيص مشاكل الملف التنفيذي")
    print("=" * 60)
    
    try:
        # تشخيص البيئة
        debug_environment()
        
        # اختبار الاستيراد
        test_imports()
        
        # اختبار ملفات التطبيق
        test_app_files()
        
        # اختبار قاعدة البيانات
        test_database()
        
        # اختبار استيراد التطبيق
        app = test_app_import()
        
        if app:
            print("\n" + "=" * 60)
            print("النتيجة: التطبيق جاهز للتشغيل")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("النتيجة: هناك مشاكل في التطبيق")
            print("=" * 60)
            
    except Exception as e:
        print(f"\nخطأ عام في التشخيص: {e}")
        traceback.print_exc()
    
    print("\nانتهى التشخيص")
    input("اضغط Enter للإغلاق...")

if __name__ == '__main__':
    main()
