# Guide d'Installation et d'Utilisation - Gestion des Pointages

## Aperçu Général

**Gestion des Pointages** est une application complète conçue pour fonctionner en environnement réseau local avec support multi-utilisateurs. Le programme supporte toutes les versions de Windows de 7 à 11 pour les systèmes 32 et 64 bits.

### Fonctionnalités Principales
- ✅ Support réseau local multi-utilisateurs
- ✅ Base de données centralisée sur le serveur principal
- ✅ Interface entièrement en français
- ✅ Système de sécurité avancé
- ✅ Rapports complets et impression professionnelle
- ✅ Sauvegarde automatique

## Configuration Système Requise

### Configuration Minimale
- **Système d'exploitation:** Windows 7 SP1 ou plus récent
- **Processeur:** Intel/AMD 1 GHz ou plus rapide
- **Mémoire:** 2 GB RAM
- **Espace disque:** 500 MB d'espace libre
- **Réseau:** Connexion réseau local (LAN)

### Configuration Recommandée
- **Système d'exploitation:** Windows 10/11
- **Processeur:** Intel/AMD 2 GHz ou plus rapide
- **Mémoire:** 4 GB RAM ou plus
- **Espace disque:** 1 GB d'espace libre
- **Réseau:** Réseau local rapide (100 Mbps ou plus)

## Installation

### Étape 1: Téléchargement du Programme
1. Téléchargez le fichier `GestionPointages_Setup_v1.0.0.exe`
2. Assurez-vous d'avoir les droits administrateur sur l'ordinateur

### Étape 2: Exécution de l'Installateur
1. Clic droit sur le fichier d'installation
2. Sélectionnez "Exécuter en tant qu'administrateur"
3. Suivez les instructions de l'installateur

### Étape 3: Choix du Type d'Installation
**Pour le serveur principal (premier ordinateur):**
- Choisissez "Installer comme serveur"
- Le programme sera configuré comme serveur principal

**Pour les autres machines:**
- Choisissez "Installer comme client"
- Vous devrez saisir l'adresse IP du serveur

### Étape 4: Configuration Réseau
1. L'installateur configurera automatiquement les règles de pare-feu
2. Assurez-vous que le réseau local fonctionne correctement
3. Testez la connexion entre les machines

## Configuration Initiale

### Configuration du Serveur Principal

1. **Premier lancement du programme:**
   ```
   Démarrer → Tous les programmes → Gestion des Pointages → Gestion des Pointages - Serveur
   ```

2. **Création du compte administrateur:**
   - Nom d'utilisateur: admin
   - Mot de passe: (choisissez un mot de passe fort)
   - Email: (optionnel)

3. **Configuration des informations de l'entreprise:**
   - Nom de l'entreprise
   - Adresse
   - Numéro de téléphone
   - Logo de l'entreprise (optionnel)

### Configuration des Machines Clientes

1. **Lancement du programme:**
   ```
   Démarrer → Tous les programmes → Gestion des Pointages → Gestion des Pointages - Client
   ```

2. **Saisie de l'adresse du serveur:**
   - Il vous sera demandé de saisir l'adresse IP du serveur
   - Exemple: *************

3. **Test de connexion:**
   - La connexion sera testée automatiquement
   - En cas d'échec de connexion, vérifiez:
     - L'adresse IP est correcte
     - Le serveur est en marche
     - Aucun problème réseau

## Utilisation du Programme

### Connexion
1. Ouvrez le programme
2. Saisissez le nom d'utilisateur et le mot de passe
3. Cliquez sur "Se connecter"

### Interface Principale
- **Tableau de bord:** Affichage des statistiques rapides
- **Employés:** Gestion des données des employés
- **Pointages:** Enregistrement des présences et absences
- **Rapports:** Création et impression des rapports
- **Paramètres:** Configuration du système

### Ajouter un Nouvel Employé
1. Allez dans la section "Employés"
2. Cliquez sur "Ajouter un nouvel employé"
3. Remplissez les données requises:
   - Nom complet
   - Numéro d'identité
   - Poste
   - Salaire
   - Date d'embauche
4. Cliquez sur "Enregistrer"

### Enregistrement des Pointages
1. Allez dans la section "Pointages"
2. Sélectionnez l'employé dans la liste
3. Cliquez sur "Enregistrer l'arrivée" ou "Enregistrer le départ"
4. L'heure sera enregistrée automatiquement

### Génération de Rapports
1. Allez dans la section "Rapports"
2. Choisissez le type de rapport:
   - Rapport journalier
   - Rapport hebdomadaire
   - Rapport mensuel
   - Rapport personnalisé
3. Définissez la période
4. Cliquez sur "Générer le rapport"
5. Vous pouvez imprimer ou exporter le rapport

## Dépannage

### Problèmes de Connexion Réseau

**Problème:** Impossible de se connecter au serveur
**Solutions:**
1. Vérifiez que le serveur fonctionne
2. Assurez-vous que l'adresse IP est correcte
3. Vérifiez les paramètres du pare-feu
4. Testez la connexion réseau: `ping [adresse_IP_serveur]`

**Problème:** Lenteur de réponse
**Solutions:**
1. Vérifiez la vitesse du réseau
2. Fermez les applications non nécessaires
3. Redémarrez le programme
4. Redémarrez l'ordinateur

### Problèmes de Base de Données

**Problème:** Erreur de base de données
**Solutions:**
1. Vérifiez l'espace disque disponible
2. Assurez-vous des droits d'écriture dans le dossier du programme
3. Utilisez la sauvegarde si nécessaire

### Problèmes d'Impression

**Problème:** L'impression ne fonctionne pas
**Solutions:**
1. Vérifiez l'installation de l'imprimante
2. Assurez-vous qu'il y a du papier et de l'encre
3. Testez l'impression depuis un autre programme

## Sauvegarde et Restauration

### Création d'une Sauvegarde
1. Allez dans "Paramètres" → "Sauvegarde"
2. Cliquez sur "Créer une sauvegarde"
3. Choisissez l'emplacement de sauvegarde
4. Attendez la fin de l'opération

### Restauration d'une Sauvegarde
1. Allez dans "Paramètres" → "Sauvegarde"
2. Cliquez sur "Restaurer une sauvegarde"
3. Sélectionnez le fichier de sauvegarde
4. Cliquez sur "Restaurer"

### Sauvegarde Automatique
- Une sauvegarde automatique est créée toutes les 24 heures
- Les 30 dernières sauvegardes sont conservées
- Ces paramètres peuvent être modifiés dans "Paramètres"

## Sécurité et Autorisations

### Niveaux d'Utilisateurs
1. **Administrateur système:** Autorisations complètes
2. **Gestionnaire RH:** Gestion des employés et rapports
3. **Réceptionniste:** Enregistrement des pointages uniquement
4. **Utilisateur standard:** Consultation des données uniquement

### Changement de Mot de Passe
1. Cliquez sur le nom d'utilisateur en haut à droite
2. Sélectionnez "Changer le mot de passe"
3. Saisissez le mot de passe actuel
4. Saisissez le nouveau mot de passe deux fois
5. Cliquez sur "Enregistrer"

## Support Technique

### Informations de Contact
- **Email:** <EMAIL>
- **Téléphone:** +212 XXX XXX XXX
- **Heures d'ouverture:** Lundi à Vendredi, 9h00 - 17h00

### Avant de Contacter le Support
Assurez-vous d'avoir les informations suivantes:
- Version du programme
- Système d'exploitation
- Description détaillée du problème
- Messages d'erreur (le cas échéant)
- Étapes pour reproduire le problème

## Mises à Jour

### Vérification des Mises à Jour
1. Allez dans "Aide" → "Vérifier les mises à jour"
2. Le système recherchera les mises à jour disponibles
3. Suivez les instructions pour installer la mise à jour

### Mise à Jour Réseau
- Le serveur doit être mis à jour en premier
- Puis toutes les machines clientes
- Assurez-vous de créer une sauvegarde avant la mise à jour

---

**Note:** Ce guide couvre l'utilisation de base du programme. Pour des informations plus détaillées, veuillez consulter le guide utilisateur avancé ou contacter le support technique.
