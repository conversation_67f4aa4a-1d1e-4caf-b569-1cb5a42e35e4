{% extends "base.html" %}

{% block title %}Rapports financiers - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3"><i class="fas fa-chart-bar me-2"></i>Rapports financiers</h1>
        <p class="text-muted">Consultez et analysez les données financières.</p>
    </div>
    <div class="col-md-4 text-end">
        <div class="btn-group" role="group">
            <a href="{{ url_for('finance.income') }}" class="btn btn-success">
                <i class="fas fa-money-bill-wave me-1"></i> Encaissements
            </a>
            <a href="{{ url_for('finance.expenses') }}" class="btn btn-danger">
                <i class="fas fa-hand-holding-usd me-1"></i> Décaissements
            </a>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filtres</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('finance.reports') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Date de début</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">Date de fin</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="type" class="form-label">Type de transaction</label>
                                <select class="form-select" id="type" name="type">
                                    <option value="" {% if not transaction_type %}selected{% endif %}>Tous</option>
                                    <option value="encaissement" {% if transaction_type == 'encaissement' %}selected{% endif %}>Encaissements</option>
                                    <option value="decaissement" {% if transaction_type == 'decaissement' %}selected{% endif %}>Décaissements</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <div class="mb-3 w-100">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i> Filtrer
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-4">
        <div class="card dashboard-card card-income">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">Encaissements</h5>
                        <h2 class="mb-0">{{ income|round(2) }} MAD</h2>
                    </div>
                    <div class="col-4 text-end">
                        <div class="card-icon text-success">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card dashboard-card card-expenses">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">Décaissements</h5>
                        <h2 class="mb-0">{{ expenses|round(2) }} MAD</h2>
                    </div>
                    <div class="col-4 text-end">
                        <div class="card-icon text-danger">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card dashboard-card card-balance">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">Solde</h5>
                        <h2 class="mb-0">{{ balance|round(2) }} MAD</h2>
                    </div>
                    <div class="col-4 text-end">
                        <div class="card-icon text-info">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Transactions</h5>
                    </div>
                    <div class="col-md-6">
                        <input type="text" id="table-filter" class="form-control" placeholder="Rechercher...">
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped filterable-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Description</th>
                                <th>Type</th>
                                <th class="text-end">Montant (MAD)</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in transactions %}
                            <tr>
                                <td>{{ transaction.date.strftime('%d/%m/%Y') }}</td>
                                <td>{{ transaction.description }}</td>
                                <td>
                                    {% if transaction.transaction_type == 'encaissement' %}
                                    <span class="badge bg-success">Encaissement</span>
                                    {% else %}
                                    <span class="badge bg-danger">Décaissement</span>
                                    {% endif %}
                                </td>
                                <td class="text-end">{{ transaction.amount|round(2) }} MAD</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center">Aucune transaction trouvée</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
