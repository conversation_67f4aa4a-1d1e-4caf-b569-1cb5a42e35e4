#!/usr/bin/env python3
"""
Script simple et fiable pour démarrer l'application
"""

def main():
    try:
        print('🚀 Démarrage de Gestion des Pointages...')
        
        # Import de l'application
        from app import create_app
        print('✅ Application importée')
        
        # Création de l'application
        app = create_app()
        print('✅ Application créée')
        
        # Création des tables et utilisateur admin
        with app.app_context():
            from extensions import db
            from models import User
            
            # Créer les tables
            db.create_all()
            print('✅ Tables créées')
            
            # Créer utilisateur admin
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    is_admin=True,
                    can_manage_users=True,
                    can_manage_employees=True,
                    can_manage_tasks=True,
                    can_manage_finances=True
                )
                admin.set_password('admin')
                db.session.add(admin)
                db.session.commit()
                print('✅ Utilisateur admin créé')
            else:
                print('✅ Utilisateur admin existe')
        
        # Informations de connexion
        print('\n' + '='*50)
        print('🎉 SERVEUR PRÊT!')
        print('🌐 URL: http://127.0.0.1:5001')
        print('👤 Utilisateur: admin')
        print('🔑 Mot de passe: admin')
        print('⏹️  Appuyez sur Ctrl+C pour arrêter')
        print('='*50)
        
        # Démarrer le serveur
        app.run(debug=True, host='127.0.0.1', port=5001)
        
    except KeyboardInterrupt:
        print('\n🛑 Serveur arrêté par l\'utilisateur')
    except Exception as e:
        print(f'\n❌ Erreur: {e}')
        import traceback
        traceback.print_exc()
        input('\nAppuyez sur Entrée pour fermer...')

if __name__ == '__main__':
    main()
