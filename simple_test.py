#!/usr/bin/env python3
"""
Test simple des corrections
"""

def test_database():
    """Tester la base de données"""
    print("🗃️  Test de la base de données...")
    try:
        from app import create_app
        from models import User, Employee, UserActivity, DatabaseBackup
        
        app = create_app()
        with app.app_context():
            # Test des modèles
            user_count = User.query.count()
            employee_count = Employee.query.count()
            activity_count = UserActivity.query.count()
            backup_count = DatabaseBackup.query.count()
            
            print(f"✅ Utilisateurs: {user_count}")
            print(f"✅ Employés: {employee_count}")
            print(f"✅ Activités: {activity_count}")
            print(f"✅ Sauvegardes: {backup_count}")
            
            return True
    except Exception as e:
        print(f"❌ Erreur base de données: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_forms():
    """Tester les formulaires"""
    print("\n📝 Test des formulaires...")
    try:
        from forms import EmployeeForm
        
        # Créer une instance du formulaire
        form = EmployeeForm()
        
        # Vérifier le label du champ daily_rate
        if hasattr(form.daily_rate, 'label') and form.daily_rate.label.text == 'Salaire':
            print("✅ Champ 'Salaire' trouvé dans EmployeeForm")
        else:
            print(f"❌ Label actuel: {form.daily_rate.label.text}")
            
        return True
    except Exception as e:
        print(f"❌ Erreur formulaires: {e}")
        return False

def test_templates():
    """Tester les templates"""
    print("\n🎨 Test des templates...")
    try:
        # Test template daily_attendance
        with open('templates/attendance/daily_attendance.html', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'csrf_token' in content:
                print("✅ CSRF token trouvé dans daily_attendance.html")
            else:
                print("❌ CSRF token non trouvé dans daily_attendance.html")
        
        # Test template users
        with open('templates/user/users.html', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'Permissions' in content:
                print("✅ Colonne 'Permissions' trouvée dans users.html")
            else:
                print("❌ Colonne 'Permissions' non trouvée dans users.html")
        
        # Test template add_employee
        with open('templates/employee/add_employee.html', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'Salaire en MAD' in content:
                print("✅ Placeholder 'Salaire en MAD' trouvé dans add_employee.html")
            else:
                print("❌ Placeholder 'Salaire en MAD' non trouvé dans add_employee.html")
                
        return True
    except Exception as e:
        print(f"❌ Erreur templates: {e}")
        return False

def test_activity_logger():
    """Tester le logger d'activités"""
    print("\n📊 Test du logger d'activités...")
    try:
        from activity_logger import log_user_activity
        
        # Vérifier que la fonction accepte user_id
        import inspect
        sig = inspect.signature(log_user_activity)
        if 'user_id' in sig.parameters:
            print("✅ Paramètre 'user_id' trouvé dans log_user_activity")
        else:
            print("❌ Paramètre 'user_id' non trouvé dans log_user_activity")
            
        return True
    except Exception as e:
        print(f"❌ Erreur activity logger: {e}")
        return False

def main():
    """Fonction principale"""
    print("🧪 Test simple des corrections")
    print("=" * 40)
    
    tests = [
        test_database,
        test_forms,
        test_templates,
        test_activity_logger
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Erreur dans {test.__name__}: {e}")
    
    print("\n" + "=" * 40)
    print(f"🎯 Résultat: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 TOUS LES TESTS RÉUSSIS!")
        print("💡 Les corrections semblent fonctionner")
    else:
        print("⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print("💡 Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    main()
    input("\nAppuyez sur Entrée pour fermer...")
