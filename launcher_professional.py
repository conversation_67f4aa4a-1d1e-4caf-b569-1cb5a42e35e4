"""
Lanceur professionnel pour Gestion des Pointages
Sans fenêtre console - Interface graphique
"""

import os
import sys
import time
import threading
import webbrowser
import warnings
import tkinter as tk
from tkinter import messagebox, ttk
import socket

# Ignorer les avertissements
warnings.filterwarnings('ignore')

class GestionPointagesLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.app = None
        self.server_thread = None
        
    def setup_window(self):
        """Configuration de la fenêtre principale"""
        self.root.title("Gestion des Pointages - Serveur")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Icône si disponible
        try:
            icon_path = os.path.join("static", "Image", "App Gestion Des Pointages.png")
            if os.path.exists(icon_path):
                self.root.iconbitmap(default=icon_path)
        except:
            pass
        
        # Style
        style = ttk.Style()
        style.theme_use('clam')
        
        self.create_widgets()
        
    def center_window(self):
        """Centrer la fenêtre sur l'écran"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.root.winfo_screenheight() // 2) - (400 // 2)
        self.root.geometry(f"500x400+{x}+{y}")
        
    def create_widgets(self):
        """Créer les widgets de l'interface"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titre
        title_label = ttk.Label(
            main_frame, 
            text="Gestion des Pointages",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(0, 10))
        
        # Sous-titre
        subtitle_label = ttk.Label(
            main_frame,
            text="Système de Gestion des Présences",
            font=("Arial", 10)
        )
        subtitle_label.pack(pady=(0, 20))
        
        # Zone d'état
        self.status_frame = ttk.LabelFrame(main_frame, text="État du Serveur", padding="10")
        self.status_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.status_label = ttk.Label(
            self.status_frame,
            text="Serveur arrêté",
            font=("Arial", 10)
        )
        self.status_label.pack()
        
        # Barre de progression
        self.progress = ttk.Progressbar(
            self.status_frame,
            mode='indeterminate'
        )
        self.progress.pack(fill=tk.X, pady=(10, 0))
        
        # Informations de connexion
        self.info_frame = ttk.LabelFrame(main_frame, text="Informations de Connexion", padding="10")
        self.info_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.url_label = ttk.Label(
            self.info_frame,
            text="URL: http://localhost:5001",
            font=("Arial", 10, "bold")
        )
        self.url_label.pack()
        
        self.credentials_label = ttk.Label(
            self.info_frame,
            text="Utilisateur: admin | Mot de passe: admin",
            font=("Arial", 9)
        )
        self.credentials_label.pack(pady=(5, 0))
        
        # Boutons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.start_button = ttk.Button(
            button_frame,
            text="Démarrer le Serveur",
            command=self.start_server,
            style="Accent.TButton"
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(
            button_frame,
            text="Arrêter le Serveur",
            command=self.stop_server,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.browser_button = ttk.Button(
            button_frame,
            text="Ouvrir Navigateur",
            command=self.open_browser,
            state=tk.DISABLED
        )
        self.browser_button.pack(side=tk.LEFT)
        
        # Zone de log
        log_frame = ttk.LabelFrame(main_frame, text="Journal", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = tk.Text(
            log_frame,
            height=8,
            wrap=tk.WORD,
            font=("Consolas", 9)
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Scrollbar pour le log
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        # Protocole de fermeture
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def log_message(self, message):
        """Ajouter un message au journal"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def setup_paths(self):
        """Configuration des chemins"""
        if getattr(sys, 'frozen', False):
            application_path = os.path.dirname(sys.executable)
            bundle_dir = sys._MEIPASS
        else:
            application_path = os.path.dirname(os.path.abspath(__file__))
            bundle_dir = application_path
        
        os.chdir(application_path)
        sys.path.insert(0, application_path)
        sys.path.insert(0, bundle_dir)
        
        return application_path, bundle_path
        
    def create_directories(self):
        """Créer les répertoires nécessaires"""
        dirs = ['instance', 'logs', 'backups', 'uploads', 'static/uploads']
        for directory in dirs:
            try:
                os.makedirs(directory, exist_ok=True)
            except:
                pass
                
    def check_database(self):
        """Vérifier la base de données"""
        db_path = 'instance/app.db'
        
        if os.path.exists(db_path) and os.path.getsize(db_path) > 1000:
            self.log_message("Base de données trouvée")
            return True
        
        self.log_message("Création de la base de données...")
        return self.create_simple_database()
        
    def create_simple_database(self):
        """Créer une base de données simple"""
        try:
            import sqlite3
            from werkzeug.security import generate_password_hash
            
            db_path = 'instance/app.db'
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Table des utilisateurs
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(64) UNIQUE NOT NULL,
                    email VARCHAR(120) UNIQUE NOT NULL,
                    password_hash VARCHAR(128) NOT NULL,
                    role VARCHAR(20) DEFAULT 'user',
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_login DATETIME
                )
            ''')
            
            # Utilisateur par défaut
            admin_hash = generate_password_hash('admin')
            cursor.execute('''
                INSERT OR IGNORE INTO users (username, email, password_hash, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', ('admin', '<EMAIL>', admin_hash, 'admin', 1))
            
            conn.commit()
            conn.close()
            self.log_message("Base de données créée avec succès")
            return True
            
        except Exception as e:
            self.log_message(f"Erreur création base de données: {e}")
            return False
            
    def get_local_ip(self):
        """Obtenir l'adresse IP locale"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"
            
    def start_server(self):
        """Démarrer le serveur"""
        self.log_message("Démarrage du serveur...")
        self.progress.start()
        self.status_label.config(text="Démarrage en cours...")
        
        # Désactiver le bouton de démarrage
        self.start_button.config(state=tk.DISABLED)
        
        # Démarrer le serveur dans un thread séparé
        self.server_thread = threading.Thread(target=self._start_flask_server, daemon=True)
        self.server_thread.start()
        
    def _start_flask_server(self):
        """Démarrer le serveur Flask"""
        try:
            # Configuration des chemins
            self.setup_paths()
            self.create_directories()
            
            if not self.check_database():
                self.log_message("Erreur: Impossible de créer la base de données")
                self.reset_ui()
                return
            
            # Importer et créer l'application
            from app import create_app
            
            self.app = create_app()
            self.app.config.update({
                'DEBUG': False,
                'TESTING': False,
                'SECRET_KEY': 'gestion-pointages-2025-secure',
                'SQLALCHEMY_DATABASE_URI': 'sqlite:///instance/app.db',
                'SQLALCHEMY_TRACK_MODIFICATIONS': False,
                'WTF_CSRF_ENABLED': True,
            })
            
            # Mettre à jour l'interface
            self.root.after(0, self.server_started)
            
            # Démarrer le serveur
            self.app.run(
                host='0.0.0.0',  # Écouter sur toutes les interfaces
                port=5001,
                debug=False,
                use_reloader=False,
                threaded=True
            )
            
        except Exception as e:
            self.log_message(f"Erreur serveur: {e}")
            self.root.after(0, self.reset_ui)
            
    def server_started(self):
        """Serveur démarré avec succès"""
        self.progress.stop()
        self.status_label.config(text="Serveur en fonctionnement")
        self.stop_button.config(state=tk.NORMAL)
        self.browser_button.config(state=tk.NORMAL)
        
        # Afficher les informations de connexion
        local_ip = self.get_local_ip()
        self.url_label.config(text=f"URL Locale: http://localhost:5001\nURL Réseau: http://{local_ip}:5001")
        
        self.log_message("Serveur démarré avec succès")
        self.log_message(f"Accessible localement: http://localhost:5001")
        self.log_message(f"Accessible sur le réseau: http://{local_ip}:5001")
        self.log_message("Utilisateur: admin | Mot de passe: admin")
        
        # Ouvrir automatiquement le navigateur
        threading.Timer(2.0, self.open_browser).start()
        
    def stop_server(self):
        """Arrêter le serveur"""
        self.log_message("Arrêt du serveur...")
        # Note: Flask n'a pas de méthode stop() simple
        # Dans un environnement de production, on utiliserait un serveur WSGI
        self.reset_ui()
        
    def reset_ui(self):
        """Réinitialiser l'interface"""
        self.progress.stop()
        self.status_label.config(text="Serveur arrêté")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.browser_button.config(state=tk.DISABLED)
        self.url_label.config(text="URL: http://localhost:5001")
        
    def open_browser(self):
        """Ouvrir le navigateur"""
        try:
            webbrowser.open('http://localhost:5001')
            self.log_message("Navigateur ouvert")
        except Exception as e:
            self.log_message(f"Erreur ouverture navigateur: {e}")
            
    def on_closing(self):
        """Gérer la fermeture de l'application"""
        if messagebox.askokcancel("Quitter", "Voulez-vous vraiment quitter l'application?"):
            self.root.destroy()
            
    def run(self):
        """Lancer l'application"""
        self.log_message("Application Gestion des Pointages démarrée")
        self.root.mainloop()

def main():
    """Fonction principale"""
    app = GestionPointagesLauncher()
    app.run()

if __name__ == '__main__':
    main()
