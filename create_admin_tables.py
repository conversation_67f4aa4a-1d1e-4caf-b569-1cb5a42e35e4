#!/usr/bin/env python3
"""
Script pour créer les tables d'administration manquantes
"""

def create_admin_tables():
    """Créer les tables d'administration"""
    try:
        print("🔧 Création des tables d'administration...")
        
        from app import create_app
        from extensions import db
        from models import UserActivity, DatabaseBackup
        
        app = create_app()
        
        with app.app_context():
            # Créer toutes les tables
            db.create_all()
            print("✅ Tables créées avec succès")
            
            # Vérifier les tables créées
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            print("\n📋 Tables disponibles:")
            for table in sorted(tables):
                print(f"  - {table}")
            
            # Vérifier spécifiquement les tables d'administration
            admin_tables = ['user_activities', 'database_backups']
            for table in admin_tables:
                if table in tables:
                    print(f"✅ {table} créée")
                else:
                    print(f"❌ {table} manquante")
            
            print("\n🎉 Configuration terminée!")
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_admin_tables()
    if success:
        print("\n🚀 Vous pouvez maintenant accéder aux fonctionnalités d'administration")
    else:
        print("\n⚠️  Veuillez corriger les erreurs avant de continuer")
    
    input("\nAppuyez sur Entrée pour fermer...")
