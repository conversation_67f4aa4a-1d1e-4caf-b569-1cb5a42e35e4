#!/usr/bin/env python3
"""
Routes pour l'administration du système
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from extensions import db
import os
import json
import csv
import sqlite3
from datetime import datetime
import zipfile
import tempfile

# C<PERSON>er le blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

# استيراد النموذج من models.py بدلاً من إعادة تعريفه
try:
    from models import DatabaseBackup
except ImportError:
    # إذا لم يكن موجوداً في models.py، نعرفه هنا
    class DatabaseBackup(db.Model):
        __tablename__ = 'database_backups'

        id = db.Column(db.Integer, primary_key=True)
        filename = db.Column(db.String(255), nullable=False)
        file_path = db.Column(db.String(500), nullable=False)
        backup_type = db.Column(db.String(50), nullable=False)  # manual, automatic
        format = db.Column(db.String(10), nullable=False)  # json, csv, sql
        file_size_mb = db.Column(db.Float, nullable=False)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

@admin_bp.route('/database')
@login_required
def database_management():
    """Page de gestion de la base de données"""
    if not current_user.is_admin:
        flash('Accès non autorisé.', 'danger')
        return redirect(url_for('main.dashboard'))
    
    # Récupérer l'historique des sauvegardes
    backups = DatabaseBackup.query.order_by(DatabaseBackup.created_at.desc()).limit(10).all()
    
    return render_template('admin/database_management.html',
                          title='Gestion de la base de données',
                          backups=backups)

@admin_bp.route('/export_database', methods=['POST'])
@login_required
def export_database():
    """Exporter la base de données"""
    if not current_user.is_admin:
        flash('Accès non autorisé.', 'danger')
        return redirect(url_for('main.dashboard'))
    
    try:
        export_format = request.form.get('export_format')
        tables = request.form.getlist('tables')
        
        if not export_format:
            flash('Veuillez sélectionner un format d\'export.', 'danger')
            return redirect(url_for('admin.database_management'))
        
        # Créer le dossier de sauvegarde s'il n'existe pas
        backup_dir = os.path.join(current_app.instance_path, 'backups')
        os.makedirs(backup_dir, exist_ok=True)
        
        # Générer le nom de fichier
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'backup_{timestamp}.{export_format}'
        file_path = os.path.join(backup_dir, filename)
        
        if export_format == 'json':
            export_to_json(file_path, tables)
        elif export_format == 'csv':
            export_to_csv(file_path, tables)
        elif export_format == 'sql':
            export_to_sql(file_path, tables)
        
        # Enregistrer dans l'historique
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
        backup = DatabaseBackup(
            filename=filename,
            file_path=file_path,
            backup_type='manual',
            format=export_format,
            file_size_mb=round(file_size, 2),
            created_by=current_user.id
        )
        db.session.add(backup)
        db.session.commit()
        
        flash(f'Export réussi ! Fichier : {filename}', 'success')
        return send_file(file_path, as_attachment=True, download_name=filename)
        
    except Exception as e:
        flash(f'Erreur lors de l\'export : {str(e)}', 'danger')
        return redirect(url_for('admin.database_management'))

@admin_bp.route('/import_database', methods=['POST'])
@login_required
def import_database():
    """Importer des données dans la base de données"""
    if not current_user.is_admin:
        flash('Accès non autorisé.', 'danger')
        return redirect(url_for('main.dashboard'))
    
    try:
        import_file = request.files.get('import_file')
        import_mode = request.form.get('import_mode', 'append')
        create_backup = request.form.get('create_backup') == 'on'
        
        if not import_file or import_file.filename == '':
            flash('Veuillez sélectionner un fichier à importer.', 'danger')
            return redirect(url_for('admin.database_management'))
        
        # Créer une sauvegarde avant l'import si demandé
        if create_backup:
            create_automatic_backup()
        
        # Sauvegarder le fichier temporairement
        filename = secure_filename(import_file.filename)
        temp_path = os.path.join(tempfile.gettempdir(), filename)
        import_file.save(temp_path)
        
        # Déterminer le format et importer
        file_ext = filename.lower().split('.')[-1]
        
        if file_ext == 'json':
            import_from_json(temp_path, import_mode)
        elif file_ext == 'csv':
            import_from_csv(temp_path, import_mode)
        elif file_ext == 'sql':
            import_from_sql(temp_path, import_mode)
        else:
            flash('Format de fichier non supporté.', 'danger')
            return redirect(url_for('admin.database_management'))
        
        # Nettoyer le fichier temporaire
        os.remove(temp_path)
        
        flash('Import réussi !', 'success')
        
    except Exception as e:
        flash(f'Erreur lors de l\'import : {str(e)}', 'danger')
    
    return redirect(url_for('admin.database_management'))

@admin_bp.route('/download_backup/<int:backup_id>')
@login_required
def download_backup(backup_id):
    """Télécharger une sauvegarde"""
    if not current_user.is_admin:
        flash('Accès non autorisé.', 'danger')
        return redirect(url_for('main.dashboard'))
    
    backup = DatabaseBackup.query.get_or_404(backup_id)
    
    if os.path.exists(backup.file_path):
        return send_file(backup.file_path, as_attachment=True, download_name=backup.filename)
    else:
        flash('Fichier de sauvegarde introuvable.', 'danger')
        return redirect(url_for('admin.database_management'))

@admin_bp.route('/backup/delete/<int:backup_id>', methods=['POST'])
@login_required
def delete_backup(backup_id):
    """Supprimer une sauvegarde"""
    if not current_user.is_admin:
        return jsonify({'success': False, 'error': 'Accès non autorisé'})
    
    try:
        backup = DatabaseBackup.query.get_or_404(backup_id)
        
        # Supprimer le fichier physique
        if os.path.exists(backup.file_path):
            os.remove(backup.file_path)
        
        # Supprimer l'enregistrement
        db.session.delete(backup)
        db.session.commit()
        
        return jsonify({'success': True})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Fonctions utilitaires pour l'export/import

def export_to_json(file_path, tables):
    """Exporter vers JSON"""
    data = {}
    
    if 'all' in tables:
        # Exporter toutes les tables
        from models import User, Employee, SimpleTask, Attendance, Transaction
        
        data['users'] = [user.to_dict() for user in User.query.all()]
        data['employees'] = [emp.to_dict() for emp in Employee.query.all()]
        data['simple_tasks'] = [task.to_dict() for task in SimpleTask.query.all()]
        data['attendance'] = [att.to_dict() for att in Attendance.query.all()]
        data['financial_records'] = [trans.to_dict() for trans in Transaction.query.all()]
    else:
        # Exporter les tables sélectionnées
        if 'users' in tables:
            from models import User
            data['users'] = [user.to_dict() for user in User.query.all()]
        
        if 'employees' in tables:
            from models import Employee
            data['employees'] = [emp.to_dict() for emp in Employee.query.all()]
        
        if 'simple_tasks' in tables:
            from models import SimpleTask
            data['simple_tasks'] = [task.to_dict() for task in SimpleTask.query.all()]
        
        if 'attendance' in tables:
            from models import Attendance
            data['attendance'] = [att.to_dict() for att in Attendance.query.all()]
        
        if 'financial_records' in tables:
            from models import Transaction
            data['financial_records'] = [trans.to_dict() for trans in Transaction.query.all()]
    
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2, default=str)

def export_to_csv(file_path, tables):
    """Exporter vers CSV (fichier ZIP avec plusieurs CSV)"""
    import zipfile
    
    with zipfile.ZipFile(file_path, 'w') as zipf:
        if 'all' in tables or 'users' in tables:
            export_table_to_csv(zipf, 'users', 'User')
        
        if 'all' in tables or 'employees' in tables:
            export_table_to_csv(zipf, 'employees', 'Employee')
        
        if 'all' in tables or 'simple_tasks' in tables:
            export_table_to_csv(zipf, 'simple_tasks', 'SimpleTask')
        
        if 'all' in tables or 'attendance' in tables:
            export_table_to_csv(zipf, 'attendance', 'Attendance')
        
        if 'all' in tables or 'financial_records' in tables:
            export_table_to_csv(zipf, 'financial_records', 'Transaction')

def export_table_to_csv(zipf, table_name, model_name):
    """Exporter une table vers CSV"""
    from models import User, Employee, SimpleTask, Attendance, Transaction
    
    model_map = {
        'User': User,
        'Employee': Employee,
        'SimpleTask': SimpleTask,
        'Attendance': Attendance,
        'Transaction': Transaction
    }
    
    model = model_map.get(model_name)
    if not model:
        return
    
    # Créer un fichier CSV temporaire
    temp_csv = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8')
    
    try:
        records = model.query.all()
        if records:
            # Obtenir les colonnes
            first_record = records[0].to_dict()
            fieldnames = list(first_record.keys())
            
            writer = csv.DictWriter(temp_csv, fieldnames=fieldnames)
            writer.writeheader()
            
            for record in records:
                writer.writerow(record.to_dict())
        
        temp_csv.close()
        
        # Ajouter au ZIP
        zipf.write(temp_csv.name, f'{table_name}.csv')
        
    finally:
        # Nettoyer le fichier temporaire
        os.unlink(temp_csv.name)

def export_to_sql(file_path, tables):
    """Exporter vers SQL"""
    # Cette fonction nécessiterait une implémentation plus complexe
    # Pour l'instant, on utilise une approche simple
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write("-- Export SQL de la base de données\n")
        f.write(f"-- Généré le {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("-- Cette fonctionnalité sera implémentée dans une version future\n")

def import_from_json(file_path, import_mode):
    """Importer depuis JSON"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Implémentation de l'import JSON
    # Cette fonction nécessiterait une logique complexe pour gérer les relations
    pass

def import_from_csv(file_path, import_mode):
    """Importer depuis CSV"""
    # Implémentation de l'import CSV
    pass

def import_from_sql(file_path, import_mode):
    """Importer depuis SQL"""
    # Implémentation de l'import SQL
    pass

def create_automatic_backup():
    """Créer une sauvegarde automatique"""
    try:
        backup_dir = os.path.join(current_app.instance_path, 'backups')
        os.makedirs(backup_dir, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'auto_backup_{timestamp}.json'
        file_path = os.path.join(backup_dir, filename)

        export_to_json(file_path, ['all'])

        # Enregistrer dans l'historique
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
        backup = DatabaseBackup(
            filename=filename,
            file_path=file_path,
            backup_type='automatic',
            format='json',
            file_size_mb=round(file_size, 2),
            created_by=current_user.id
        )
        db.session.add(backup)
        db.session.commit()

        return True

    except Exception as e:
        print(f"Erreur lors de la sauvegarde automatique: {e}")
        return False

# Routes pour le suivi des activités utilisateurs

@admin_bp.route('/user_activities')
@login_required
def user_activities():
    """Page de suivi des activités utilisateurs"""
    if not current_user.is_admin:
        flash('Accès non autorisé.', 'danger')
        return redirect(url_for('main.dashboard'))

    from models import UserActivity, User
    from datetime import datetime, timedelta

    # Paramètres de filtrage
    page = request.args.get('page', 1, type=int)
    user_id = request.args.get('user_id', type=int)
    action = request.args.get('action')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')

    # Construire la requête
    query = UserActivity.query

    if user_id:
        query = query.filter(UserActivity.user_id == user_id)

    if action:
        query = query.filter(UserActivity.action == action)

    if date_from:
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
        query = query.filter(UserActivity.timestamp >= date_from_obj)

    if date_to:
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
        date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
        query = query.filter(UserActivity.timestamp <= date_to_obj)

    # Pagination
    activities = query.order_by(UserActivity.timestamp.desc()).paginate(
        page=page, per_page=50, error_out=False
    )

    # Statistiques
    today = datetime.now().date()
    week_start = today - timedelta(days=today.weekday())

    stats = {
        'today_count': UserActivity.query.filter(
            UserActivity.timestamp >= datetime.combine(today, datetime.min.time())
        ).count(),
        'week_count': UserActivity.query.filter(
            UserActivity.timestamp >= datetime.combine(week_start, datetime.min.time())
        ).count(),
        'active_users': db.session.query(UserActivity.user_id).distinct().count(),
        'total_count': UserActivity.query.count()
    }

    # Liste des utilisateurs pour le filtre
    users = User.query.all()

    return render_template('admin/user_activities.html',
                          title='Suivi des activités utilisateurs',
                          activities=activities.items,
                          pagination=activities,
                          stats=stats,
                          users=users)

@admin_bp.route('/activity_details/<int:activity_id>')
@login_required
def activity_details(activity_id):
    """Détails d'une activité"""
    if not current_user.is_admin:
        return jsonify({'success': False, 'error': 'Accès non autorisé'})

    from models import UserActivity

    activity = UserActivity.query.get_or_404(activity_id)

    html = f"""
    <div class="row">
        <div class="col-md-6">
            <h6>Informations générales</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>ID:</strong></td>
                    <td>{activity.id}</td>
                </tr>
                <tr>
                    <td><strong>Utilisateur:</strong></td>
                    <td>{activity.user.username if activity.user else 'Utilisateur supprimé'}</td>
                </tr>
                <tr>
                    <td><strong>Action:</strong></td>
                    <td><span class="badge bg-primary">{activity.action}</span></td>
                </tr>
                <tr>
                    <td><strong>Date/Heure:</strong></td>
                    <td>{activity.formatted_timestamp}</td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <h6>Informations techniques</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>Adresse IP:</strong></td>
                    <td>{activity.ip_address or 'Non disponible'}</td>
                </tr>
                <tr>
                    <td><strong>Navigateur:</strong></td>
                    <td><small>{activity.user_agent or 'Non disponible'}</small></td>
                </tr>
            </table>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-12">
            <h6>Description</h6>
            <div class="alert alert-light">
                {activity.description or 'Aucune description disponible'}
            </div>
        </div>
    </div>

    {f'<div class="row mt-3"><div class="col-12"><h6>Métadonnées</h6><pre class="bg-light p-3"><code>{activity.extra_data}</code></pre></div></div>' if activity.extra_data else ''}
    """

    return jsonify({'success': True, 'html': html})

@admin_bp.route('/export_activities')
@login_required
def export_activities():
    """Exporter les activités utilisateurs"""
    if not current_user.is_admin:
        flash('Accès non autorisé.', 'danger')
        return redirect(url_for('main.dashboard'))

    from models import UserActivity
    import csv
    import tempfile

    try:
        # Créer un fichier CSV temporaire
        temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8')

        # En-têtes CSV
        fieldnames = ['Date/Heure', 'Utilisateur', 'Action', 'Description', 'Adresse IP']
        writer = csv.DictWriter(temp_file, fieldnames=fieldnames)
        writer.writeheader()

        # Données
        activities = UserActivity.query.order_by(UserActivity.timestamp.desc()).all()
        for activity in activities:
            writer.writerow({
                'Date/Heure': activity.formatted_timestamp,
                'Utilisateur': activity.user.username if activity.user else 'Utilisateur supprimé',
                'Action': activity.action,
                'Description': activity.description or '',
                'Adresse IP': activity.ip_address or ''
            })

        temp_file.close()

        # Nom du fichier d'export
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'activites_utilisateurs_{timestamp}.csv'

        return send_file(temp_file.name, as_attachment=True, download_name=filename)

    except Exception as e:
        flash(f'Erreur lors de l\'export : {str(e)}', 'danger')
        return redirect(url_for('admin.user_activities'))

# Fonctions utilitaires pour les templates

def get_action_color(action):
    """Retourne la couleur Bootstrap pour une action"""
    colors = {
        'login': 'success',
        'logout': 'secondary',
        'create': 'primary',
        'update': 'warning',
        'delete': 'danger',
        'view': 'info'
    }
    return colors.get(action, 'secondary')

def get_action_label(action):
    """Retourne le libellé français pour une action"""
    labels = {
        'login': 'Connexion',
        'logout': 'Déconnexion',
        'create': 'Création',
        'update': 'Modification',
        'delete': 'Suppression',
        'view': 'Consultation'
    }
    return labels.get(action, action.title())

# Enregistrer les fonctions dans le contexte des templates
@admin_bp.app_template_global()
def get_action_color_global(action):
    return get_action_color(action)

@admin_bp.app_template_global()
def get_action_label_global(action):
    return get_action_label(action)
