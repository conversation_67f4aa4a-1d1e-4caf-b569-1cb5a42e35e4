"""
Script de construction pour créer l'exécutable
Système de Gestion des Pointages
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import zipfile

def install_requirements():
    """Installer les dépendances nécessaires"""
    print("Installation des dépendances...")
    
    requirements = [
        "pyinstaller==5.13.2",
        "auto-py-to-exe==2.40.0"
    ]
    
    for req in requirements:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", req])
            print(f"✓ {req} installé")
        except subprocess.CalledProcessError as e:
            print(f"✗ Erreur lors de l'installation de {req}: {e}")
            return False
    
    return True

def clean_build_dirs():
    """Nettoyer les répertoires de build"""
    print("Nettoyage des répertoires de build...")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ {dir_name} supprimé")

def create_icon():
    """Créer un icône pour l'application"""
    icon_path = Path("static/favicon.ico")
    if not icon_path.exists():
        print("⚠ Icône non trouvé, création d'un icône par défaut...")
        # Ici, vous pourriez créer un icône par défaut ou le copier
        return None
    return str(icon_path)

def build_executable():
    """Construire l'exécutable avec PyInstaller"""
    print("Construction de l'exécutable...")

    # Paramètres PyInstaller
    cmd = [
        "pyinstaller",
        "--onefile",
        "--console",  # Changé de --windowed à --console pour voir les messages
        "--name=GestionPointages",
        "--distpath=dist",
        "--workpath=build",
        "--specpath=.",
        "--clean",
        "--noconfirm",

        # Inclure les données
        "--add-data=templates;templates",
        "--add-data=static;static",
        "--add-data=instance;instance",

        # Modules cachés
        "--hidden-import=flask",
        "--hidden-import=flask_sqlalchemy",
        "--hidden-import=flask_login",
        "--hidden-import=flask_wtf",
        "--hidden-import=flask_babel",
        "--hidden-import=wtforms",
        "--hidden-import=sqlalchemy",
        "--hidden-import=babel",
        "--hidden-import=jinja2",
        "--hidden-import=werkzeug",
        "--hidden-import=markupsafe",
        "--hidden-import=itsdangerous",
        "--hidden-import=click",
        "--hidden-import=blinker",
        "--hidden-import=alembic",
        "--hidden-import=email_validator",
        "--hidden-import=python_dotenv",
        "--hidden-import=sqlite3",
        "--hidden-import=models",
        "--hidden-import=models_company",
        "--hidden-import=forms",
        "--hidden-import=routes",
        "--hidden-import=routes_admin",
        "--hidden-import=activity_logger",

        # Fichier principal
        "direct_start.py"
    ]
    
    # Ajouter l'icône s'il existe
    icon_path = create_icon()
    if icon_path:
        cmd.extend(["--icon", icon_path])
    
    # Ajouter les informations de version
    if os.path.exists("version_info.txt"):
        cmd.extend(["--version-file", "version_info.txt"])
    
    try:
        subprocess.check_call(cmd)
        print("✓ Exécutable créé avec succès")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Erreur lors de la construction: {e}")
        return False

def create_installer_files():
    """Créer les fichiers nécessaires pour l'installateur"""
    print("Création des fichiers d'installation...")
    
    # Créer le répertoire d'installation
    install_dir = Path("installer")
    install_dir.mkdir(exist_ok=True)
    
    # Copier l'exécutable
    exe_path = Path("dist/GestionPointages.exe")
    if exe_path.exists():
        shutil.copy2(exe_path, install_dir / "GestionPointages.exe")
        print("✓ Exécutable copié")
    
    # Créer un fichier README
    readme_content = """
# Système de Gestion des Pointages

## Installation

1. Copiez le fichier GestionPointages.exe dans un répertoire de votre choix
2. Lancez GestionPointages.exe
3. Configurez le mode de fonctionnement (Serveur ou Client)
4. Démarrez l'application

## Configuration Multi-Utilisateurs

### Mode Serveur (Ordinateur principal)
- Sélectionnez "Serveur" dans le lanceur
- L'application sera accessible depuis d'autres ordinateurs du réseau
- La base de données sera stockée localement

### Mode Client (Ordinateurs secondaires)
- Sélectionnez "Client" dans le lanceur
- Entrez l'adresse IP du serveur
- Utilisez la détection automatique si nécessaire

## Prérequis Système

- Windows 7, 8, 8.1, 10, ou 11 (32 ou 64 bits)
- Aucune installation supplémentaire requise
- Connexion réseau pour le mode multi-utilisateurs

## Support

Pour toute assistance, consultez la documentation ou contactez le support technique.

Version: 1.0.0
Date: 2025
"""
    
    with open(install_dir / "README.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    # Créer un script de démarrage rapide
    startup_script = """@echo off
echo Démarrage du Système de Gestion des Pointages...
start "" "GestionPointages.exe"
"""
    
    with open(install_dir / "Demarrer.bat", "w", encoding="utf-8") as f:
        f.write(startup_script)
    
    print("✓ Fichiers d'installation créés")

def create_setup_package():
    """Créer le package d'installation"""
    print("Création du package d'installation...")
    
    # Créer un fichier ZIP avec tous les composants
    zip_path = "GestionPointages_Setup_v1.0.0.zip"
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # Ajouter l'exécutable
        exe_path = "dist/GestionPointages.exe"
        if os.path.exists(exe_path):
            zipf.write(exe_path, "GestionPointages.exe")
        
        # Ajouter les fichiers d'installation
        install_dir = Path("installer")
        if install_dir.exists():
            for file_path in install_dir.rglob("*"):
                if file_path.is_file():
                    arcname = file_path.relative_to(install_dir)
                    zipf.write(file_path, arcname)
        
        # Ajouter la documentation
        if os.path.exists("README.md"):
            zipf.write("README.md", "Documentation.md")
    
    print(f"✓ Package d'installation créé: {zip_path}")
    return zip_path

def create_inno_setup_script():
    """Créer un script Inno Setup pour un installateur Windows professionnel"""
    
    inno_script = """
; Script Inno Setup pour Gestion des Pointages
; Compatible avec Windows 7, 8, 8.1, 10, 11 (32/64 bits)

[Setup]
AppName=Gestion des Pointages
AppVersion=1.0.0
AppPublisher=Gestion des Pointages
AppPublisherURL=https://www.gestion-pointages.com
AppSupportURL=https://www.gestion-pointages.com/support
AppUpdatesURL=https://www.gestion-pointages.com/updates
DefaultDirName={autopf}\\Gestion des Pointages
DefaultGroupName=Gestion des Pointages
AllowNoIcons=yes
LicenseFile=
OutputDir=setup
OutputBaseFilename=GestionPointages_Setup_v1.0.0
SetupIconFile=
Compression=lzma
SolidCompression=yes
WizardStyle=modern
ArchitecturesAllowed=x86 x64
ArchitecturesInstallIn64BitMode=x64
MinVersion=6.1
PrivilegesRequired=admin

[Languages]
Name: "french"; MessagesFile: "compiler:Languages\\French.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
Source: "dist\\GestionPointages.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "installer\\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\\Gestion des Pointages"; Filename: "{app}\\GestionPointages.exe"
Name: "{group}\\{cm:UninstallProgram,Gestion des Pointages}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\\Gestion des Pointages"; Filename: "{app}\\GestionPointages.exe"; Tasks: desktopicon
Name: "{userappdata}\\Microsoft\\Internet Explorer\\Quick Launch\\Gestion des Pointages"; Filename: "{app}\\GestionPointages.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\\GestionPointages.exe"; Description: "{cm:LaunchProgram,Gestion des Pointages}"; Flags: nowait postinstall skipifsilent

[Code]
function InitializeSetup(): Boolean;
begin
  Result := True;
  if not IsAdminLoggedOn then
  begin
    MsgBox('Ce programme nécessite des privilèges administrateur pour l''installation.', mbError, MB_OK);
    Result := False;
  end;
end;
"""
    
    with open("setup_script.iss", "w", encoding="utf-8") as f:
        f.write(inno_script)
    
    print("✓ Script Inno Setup créé: setup_script.iss")

def main():
    """Fonction principale de construction"""
    print("=== Construction du Système de Gestion des Pointages ===")
    print()
    
    # Étape 1: Installation des dépendances
    if not install_requirements():
        print("✗ Échec de l'installation des dépendances")
        return False
    
    # Étape 2: Nettoyage
    clean_build_dirs()
    
    # Étape 3: Construction de l'exécutable
    if not build_executable():
        print("✗ Échec de la construction de l'exécutable")
        return False
    
    # Étape 4: Création des fichiers d'installation
    create_installer_files()
    
    # Étape 5: Création du package
    package_path = create_setup_package()
    
    # Étape 6: Création du script Inno Setup
    create_inno_setup_script()
    
    print()
    print("=== Construction terminée avec succès ===")
    print(f"✓ Exécutable: dist/GestionPointages.exe")
    print(f"✓ Package: {package_path}")
    print(f"✓ Script Inno Setup: setup_script.iss")
    print()
    print("Instructions:")
    print("1. Testez l'exécutable dans dist/GestionPointages.exe")
    print("2. Utilisez le package ZIP pour une distribution simple")
    print("3. Utilisez Inno Setup avec setup_script.iss pour un installateur professionnel")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
