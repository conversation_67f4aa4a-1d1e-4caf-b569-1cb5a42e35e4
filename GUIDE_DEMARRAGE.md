# 🚀 دليل تشغيل البرنامج

## ✅ البرنامج جاهز للاستخدام الآن!

### 🎯 طرق التشغيل

#### 🥇 الطريقة الأسهل (مُوصى بها):
```
انقر نقراً مزدوجاً على ملف: START.bat
```

#### 🥈 أو استخدم الأمر:
```bash
python start.py
```

#### 🥉 أو الطريقة التقليدية:
```bash
python run_app.py
```

### 🌐 معلومات الوصول

- **🔗 الرابط**: http://127.0.0.1:5001
- **👤 اسم المستخدم**: `admin`
- **🔑 كلمة المرور**: `admin`

### 📋 الوظائف المتاحة

- ✅ تسجيل الدخول والخروج
- ✅ لوحة التحكم الرئيسية
- ✅ **إدارة المهام**
  - إضافة مهمة جديدة
  - تعديل المهام الموجودة
  - **حذف المهام** (مع نافذة تأكيد)
  - عرض قائمة جميع المهام
- ✅ إدارة الموظفين
- ✅ إدارة الحضور والغياب
- ✅ إدارة الشؤون المالية

### 🔧 حل المشاكل الشائعة

#### ❌ إذا لم يعمل البرنامج:
1. **تأكد من تثبيت Python**
2. **ثبت المكتبات المطلوبة**:
   ```bash
   pip install flask flask-sqlalchemy flask-login flask-wtf
   ```
3. **استخدم ملف START.bat للتشغيل التلقائي**

#### ❌ إذا ظهرت رسالة "Not Found":
1. تأكد من أن الخادم يعمل (انظر للرسائل في النافذة السوداء)
2. انتظر قليلاً حتى يكتمل تحميل البرنامج
3. جرب تحديث الصفحة (F5)
4. تأكد من الرابط: http://127.0.0.1:5001

#### ❌ إذا لم تعمل كلمة المرور:
- **اسم المستخدم**: admin (بأحرف صغيرة)
- **كلمة المرور**: admin (بأحرف صغيرة)

### 📁 ملفات التشغيل المتاحة

- **`START.bat`** - تشغيل تلقائي سهل (Windows)
- **`start.py`** - تشغيل بسيط وموثوق
- **`run_app.py`** - تشغيل تقليدي محسن
- **`app.py`** - تشغيل مباشر

### 🎉 حالة البرنامج الحالية

✅ **البرنامج في حالته الأصلية مع إصلاحات التشغيل**:
- ✅ يعمل بشكل صحيح ومستقر
- ✅ جميع الوظائف الأصلية متاحة
- ✅ قاعدة البيانات تُنشأ تلقائياً
- ✅ مستخدم admin يُنشأ تلقائياً
- ✅ واجهة المستخدم الأصلية محفوظة
- ✅ نظام حذف المهام الأصلي يعمل

### 🚨 ملاحظات مهمة

1. **لا تغلق النافذة السوداء** أثناء استخدام البرنامج
2. **للإيقاف**: اضغط Ctrl+C في النافذة السوداء
3. **للوصول من أجهزة أخرى**: استبدل 127.0.0.1 بعنوان IP للجهاز
4. **النسخ الاحتياطي**: انسخ مجلد `instance` لحفظ البيانات

---

## 🆘 للدعم

إذا واجهت أي مشكلة:
1. استخدم `START.bat` أولاً
2. تأكد من رسائل الخطأ في النافذة السوداء
3. جرب `python start.py` إذا لم يعمل الملف الأول

**البرنامج جاهز ويعمل بشكل مثالي! 🎉**
