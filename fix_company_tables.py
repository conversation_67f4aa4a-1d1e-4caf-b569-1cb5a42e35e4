#!/usr/bin/env python3
"""
Script pour corriger les tables de l'entreprise
"""
import sqlite3
import os

def fix_company_tables():
    """Corriger les tables de l'entreprise"""
    
    # Chemin vers la base de données
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'app.db')
    
    # Connexion à la base de données
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("🔧 Correction des tables de l'entreprise...")
        
        # Supprimer les tables existantes
        cursor.execute('DROP TABLE IF EXISTS database_backups')
        cursor.execute('DROP TABLE IF EXISTS auto_backup_settings')
        cursor.execute('DROP TABLE IF EXISTS company_info')
        
        print("✅ Anciennes tables supprimées")
        
        # Recréer les tables sans foreign keys
        
        # Table company_info
        cursor.execute('''
            CREATE TABLE company_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom_entreprise VARCHAR(200) NOT NULL,
                raison_sociale VARCHAR(200),
                secteur_activite VARCHAR(100),
                forme_juridique VARCHAR(50),
                adresse TEXT,
                ville VARCHAR(100),
                code_postal VARCHAR(20),
                pays VARCHAR(50) DEFAULT 'Maroc',
                telephone VARCHAR(20),
                fax VARCHAR(20),
                email VARCHAR(120),
                site_web VARCHAR(200),
                numero_registre_commerce VARCHAR(50),
                numero_ice VARCHAR(50),
                numero_if VARCHAR(50),
                numero_cnss VARCHAR(50),
                numero_patente VARCHAR(50),
                capital_social FLOAT,
                devise_capital VARCHAR(10) DEFAULT 'MAD',
                banque_principale VARCHAR(100),
                numero_compte_bancaire VARCHAR(50),
                rib VARCHAR(50),
                logo_path VARCHAR(200),
                signature_path VARCHAR(200),
                cachet_path VARCHAR(200),
                pied_de_page TEXT,
                mentions_legales TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Table database_backups (sans foreign key)
        cursor.execute('''
            CREATE TABLE database_backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom_fichier VARCHAR(200) NOT NULL,
                chemin_fichier VARCHAR(500) NOT NULL,
                taille_fichier BIGINT,
                type_sauvegarde VARCHAR(20) DEFAULT 'manuel',
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER
            )
        ''')
        
        # Table auto_backup_settings
        cursor.execute('''
            CREATE TABLE auto_backup_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                is_enabled BOOLEAN DEFAULT 0,
                frequence VARCHAR(20) DEFAULT 'daily',
                heure_execution TIME DEFAULT '02:00:00',
                jour_semaine INTEGER,
                jour_mois INTEGER,
                nombre_max_sauvegardes INTEGER DEFAULT 30,
                supprimer_anciennes BOOLEAN DEFAULT 1,
                dossier_sauvegarde VARCHAR(500),
                inclure_uploads BOOLEAN DEFAULT 1,
                compression BOOLEAN DEFAULT 1,
                email_notification BOOLEAN DEFAULT 0,
                email_destinataire VARCHAR(120),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_backup DATETIME
            )
        ''')
        
        # Insérer des paramètres par défaut
        cursor.execute('''
            INSERT INTO auto_backup_settings (id, is_enabled, frequence, heure_execution)
            VALUES (1, 0, 'daily', '02:00:00')
        ''')
        
        # Valider les changements
        conn.commit()
        print("✅ Tables corrigées avec succès!")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Erreur lors de la correction: {e}")
        raise
    
    finally:
        conn.close()

if __name__ == '__main__':
    print("🔧 Correction des Tables de l'Entreprise")
    print("=" * 50)
    
    fix_company_tables()
    
    print("=" * 50)
    print("✅ Correction terminée!")
    print("🚀 Vous pouvez maintenant redémarrer l'application")
    
    input("Appuyez sur Entrée pour continuer...")
