"""
مشغل محسن للملف التنفيذي
يحل مشاكل المسارات والاستيراد
"""

import os
import sys
import time
import threading
import webbrowser

def setup_paths():
    """إعداد المسارات للملف التنفيذي"""
    if getattr(sys, 'frozen', False):
        # نحن في ملف تنفيذي
        application_path = os.path.dirname(sys.executable)
        # مسار الملفات المؤقتة
        bundle_dir = sys._MEIPASS
    else:
        # نحن في بيئة التطوير
        application_path = os.path.dirname(os.path.abspath(__file__))
        bundle_dir = application_path
    
    # تغيير المجلد الحالي
    os.chdir(application_path)
    
    # إضافة المسارات إلى sys.path
    sys.path.insert(0, application_path)
    sys.path.insert(0, bundle_dir)
    
    return application_path, bundle_dir

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    dirs = ['instance', 'logs', 'backups', 'uploads', 'static/uploads']
    for directory in dirs:
        try:
            os.makedirs(directory, exist_ok=True)
        except:
            pass

def setup_database():
    """إعداد قاعدة البيانات"""
    db_path = 'instance/app.db'
    
    if not os.path.exists(db_path):
        print("انشاء قاعدة البيانات...")
        try:
            # تشغيل سكريبت إنشاء قاعدة البيانات
            import sqlite3
            from werkzeug.security import generate_password_hash
            
            # إنشاء قاعدة البيانات
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # إنشاء جدول المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(64) UNIQUE NOT NULL,
                    email VARCHAR(120) UNIQUE NOT NULL,
                    password_hash VARCHAR(128) NOT NULL,
                    role VARCHAR(20) DEFAULT 'user',
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_login DATETIME
                )
            ''')
            
            # إنشاء المستخدم الافتراضي
            admin_hash = generate_password_hash('admin')
            cursor.execute('''
                INSERT OR IGNORE INTO users (username, email, password_hash, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', ('admin', '<EMAIL>', admin_hash, 'admin', 1))
            
            conn.commit()
            conn.close()
            print("تم انشاء قاعدة البيانات")
            return True
            
        except Exception as e:
            print(f"خطأ في انشاء قاعدة البيانات: {e}")
            return False
    
    return True

def open_browser():
    """فتح المتصفح"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5001')
        print("تم فتح المتصفح")
    except Exception as e:
        print(f"لم يتم فتح المتصفح: {e}")
        print("افتح المتصفح يدوياً: http://localhost:5001")

def start_flask_app():
    """تشغيل تطبيق Flask"""
    try:
        # استيراد التطبيق
        from app import create_app
        
        print("تحميل التطبيق...")
        app = create_app()
        
        # إعدادات الإنتاج
        app.config.update(
            DEBUG=False,
            TESTING=False,
            SECRET_KEY='gestion-pointages-2025',
            SQLALCHEMY_DATABASE_URI='sqlite:///instance/app.db',
            SQLALCHEMY_TRACK_MODIFICATIONS=False,
            WTF_CSRF_ENABLED=True,
            WTF_CSRF_TIME_LIMIT=None
        )
        
        print("تم تحميل التطبيق")
        print("الخادم يعمل على: http://localhost:5001")
        print("المستخدم: admin")
        print("كلمة المرور: admin")
        print("للايقاف: اغلق هذه النافذة")
        print("-" * 50)
        
        # تشغيل الخادم
        app.run(
            host='127.0.0.1',
            port=5001,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except ImportError as e:
        print(f"خطأ في استيراد التطبيق: {e}")
        print("تأكد من وجود جميع الملفات المطلوبة")
        input("اضغط Enter للاغلاق...")
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للاغلاق...")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("نظام ادارة الحضور والانصراف")
    print("تشغيل من الملف التنفيذي")
    print("=" * 50)
    print()
    
    # إعداد المسارات
    print("اعداد المسارات...")
    app_path, bundle_path = setup_paths()
    print(f"مسار التطبيق: {app_path}")
    
    # إنشاء المجلدات
    print("انشاء المجلدات...")
    create_directories()
    
    # إعداد قاعدة البيانات
    print("اعداد قاعدة البيانات...")
    if not setup_database():
        print("فشل في اعداد قاعدة البيانات")
        input("اضغط Enter للاغلاق...")
        return
    
    print()
    print("بدء تشغيل الخادم...")
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    # تشغيل التطبيق
    start_flask_app()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\nتم ايقاف الخادم")
    except Exception as e:
        print(f"خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للاغلاق...")
