#!/usr/bin/env python3
"""
Module pour l'enregistrement des activités utilisateurs
"""

from flask import request, current_app
from flask_login import current_user
from extensions import db
from datetime import datetime
import json

def log_user_activity(action, description=None, metadata=None):
    """
    Enregistrer une activité utilisateur
    
    Args:
        action (str): Type d'action (login, logout, create, update, delete, view)
        description (str): Description détaillée de l'action
        metadata (dict): Données supplémentaires à enregistrer
    """
    try:
        from models import UserActivity
        
        # Obtenir les informations de la requête
        ip_address = get_client_ip()
        user_agent = request.headers.get('User-Agent', '')
        
        # Créer l'enregistrement d'activité
        activity = UserActivity(
            user_id=current_user.id if current_user.is_authenticated else None,
            action=action,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            metadata=json.dumps(metadata, ensure_ascii=False) if metadata else None,
            timestamp=datetime.utcnow()
        )
        
        db.session.add(activity)
        db.session.commit()
        
        return True
        
    except Exception as e:
        # En cas d'erreur, on ne veut pas faire planter l'application
        current_app.logger.error(f"Erreur lors de l'enregistrement de l'activité: {e}")
        return False

def get_client_ip():
    """Obtenir l'adresse IP du client"""
    # Vérifier les en-têtes de proxy
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

# Décorateur pour enregistrer automatiquement les activités
def log_activity(action, description_template=None):
    """
    Décorateur pour enregistrer automatiquement les activités
    
    Args:
        action (str): Type d'action
        description_template (str): Template de description avec placeholders
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Exécuter la fonction originale
            result = func(*args, **kwargs)
            
            # Enregistrer l'activité
            try:
                description = description_template
                if description_template and kwargs:
                    description = description_template.format(**kwargs)
                
                log_user_activity(action, description)
            except Exception as e:
                current_app.logger.error(f"Erreur lors de l'enregistrement automatique: {e}")
            
            return result
        
        wrapper.__name__ = func.__name__
        return wrapper
    return decorator

# Fonctions spécialisées pour différents types d'activités

def log_login(username=None):
    """Enregistrer une connexion"""
    user = username or (current_user.username if current_user.is_authenticated else 'Inconnu')
    log_user_activity(
        action='login',
        description=f'Connexion de l\'utilisateur {user}',
        metadata={'username': user}
    )

def log_logout(username=None):
    """Enregistrer une déconnexion"""
    user = username or (current_user.username if current_user.is_authenticated else 'Inconnu')
    log_user_activity(
        action='logout',
        description=f'Déconnexion de l\'utilisateur {user}',
        metadata={'username': user}
    )

def log_task_creation(task_title, task_id=None):
    """Enregistrer la création d'une tâche"""
    log_user_activity(
        action='create',
        description=f'Création de la tâche: {task_title}',
        metadata={
            'task_id': task_id,
            'task_title': task_title,
            'module': 'tasks'
        }
    )

def log_task_update(task_title, task_id=None):
    """Enregistrer la modification d'une tâche"""
    log_user_activity(
        action='update',
        description=f'Modification de la tâche: {task_title}',
        metadata={
            'task_id': task_id,
            'task_title': task_title,
            'module': 'tasks'
        }
    )

def log_task_deletion(task_title, task_id=None):
    """Enregistrer la suppression d'une tâche"""
    log_user_activity(
        action='delete',
        description=f'Suppression de la tâche: {task_title}',
        metadata={
            'task_id': task_id,
            'task_title': task_title,
            'module': 'tasks'
        }
    )

def log_employee_creation(employee_name, employee_id=None):
    """Enregistrer la création d'un employé"""
    log_user_activity(
        action='create',
        description=f'Création de l\'employé: {employee_name}',
        metadata={
            'employee_id': employee_id,
            'employee_name': employee_name,
            'module': 'employees'
        }
    )

def log_employee_update(employee_name, employee_id=None):
    """Enregistrer la modification d'un employé"""
    log_user_activity(
        action='update',
        description=f'Modification de l\'employé: {employee_name}',
        metadata={
            'employee_id': employee_id,
            'employee_name': employee_name,
            'module': 'employees'
        }
    )

def log_employee_deletion(employee_name, employee_id=None):
    """Enregistrer la suppression d'un employé"""
    log_user_activity(
        action='delete',
        description=f'Suppression de l\'employé: {employee_name}',
        metadata={
            'employee_id': employee_id,
            'employee_name': employee_name,
            'module': 'employees'
        }
    )

def log_user_creation(username, user_id=None):
    """Enregistrer la création d'un utilisateur"""
    log_user_activity(
        action='create',
        description=f'Création de l\'utilisateur: {username}',
        metadata={
            'user_id': user_id,
            'username': username,
            'module': 'users'
        }
    )

def log_user_update(username, user_id=None):
    """Enregistrer la modification d'un utilisateur"""
    log_user_activity(
        action='update',
        description=f'Modification de l\'utilisateur: {username}',
        metadata={
            'user_id': user_id,
            'username': username,
            'module': 'users'
        }
    )

def log_user_deletion(username, user_id=None):
    """Enregistrer la suppression d'un utilisateur"""
    log_user_activity(
        action='delete',
        description=f'Suppression de l\'utilisateur: {username}',
        metadata={
            'user_id': user_id,
            'username': username,
            'module': 'users'
        }
    )

def log_database_export(format_type, tables):
    """Enregistrer un export de base de données"""
    log_user_activity(
        action='view',
        description=f'Export de la base de données en format {format_type}',
        metadata={
            'format': format_type,
            'tables': tables,
            'module': 'database'
        }
    )

def log_database_import(format_type, filename):
    """Enregistrer un import de base de données"""
    log_user_activity(
        action='update',
        description=f'Import de données depuis le fichier {filename}',
        metadata={
            'format': format_type,
            'filename': filename,
            'module': 'database'
        }
    )

def log_page_view(page_name, page_url=None):
    """Enregistrer la consultation d'une page"""
    log_user_activity(
        action='view',
        description=f'Consultation de la page: {page_name}',
        metadata={
            'page_name': page_name,
            'page_url': page_url or request.url,
            'module': 'navigation'
        }
    )

def log_financial_transaction(transaction_type, amount, description=None):
    """Enregistrer une transaction financière"""
    log_user_activity(
        action='create',
        description=f'Enregistrement d\'une transaction {transaction_type}: {amount}',
        metadata={
            'transaction_type': transaction_type,
            'amount': str(amount),
            'description': description,
            'module': 'finance'
        }
    )

def log_attendance_record(employee_name, date, status):
    """Enregistrer un pointage"""
    log_user_activity(
        action='create',
        description=f'Enregistrement de présence pour {employee_name}: {status}',
        metadata={
            'employee_name': employee_name,
            'date': str(date),
            'status': status,
            'module': 'attendance'
        }
    )
