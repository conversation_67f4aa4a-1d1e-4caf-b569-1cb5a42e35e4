{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <!-- En-tête avec boutons d'action -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-plus text-primary me-2"></i>
            Ajouter une Tâche
        </h2>
        <div class="btn-group">
            <a href="{{ url_for('task.tasks') }}" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-arrow-left me-1"></i> Retour
            </a>
            <button type="submit" form="task-form" class="btn btn-primary btn-sm">
                <i class="fas fa-save me-1"></i> Enregistrer
            </button>
        </div>
    </div>

    <!-- Formulaire dans le style du tableau -->
    <div class="task-group mb-4">
        <div class="group-header">
            <div class="d-flex align-items-center">
                <div class="group-indicator bg-primary"></div>
                <h5 class="mb-0 me-3">Nouvelle Tâche</h5>
                <span class="badge bg-primary rounded-pill">Formulaire</span>
            </div>
        </div>
        
        <div class="group-content">
            <form method="POST" enctype="multipart/form-data" id="task-form">
                {{ form.hidden_tag() }}
                
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="20%">Champ</th>
                                <th width="80%">Valeur</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Titre -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-heading me-2 text-primary"></i>
                                    {{ form.title.label.text }}
                                </td>
                                <td>
                                    {{ form.title(class="form-control form-control-sm") }}
                                    {% if form.title.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.title.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Description -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-align-left me-2 text-info"></i>
                                    {{ form.description.label.text }}
                                </td>
                                <td>
                                    {{ form.description(class="form-control form-control-sm", rows="3") }}
                                    {% if form.description.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.description.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Statut -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-flag me-2 text-warning"></i>
                                    {{ form.status.label.text }}
                                </td>
                                <td>
                                    {{ form.status(class="form-select form-select-sm") }}
                                    {% if form.status.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.status.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Priorité -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                                    {{ form.priority.label.text }}
                                </td>
                                <td>
                                    {{ form.priority(class="form-select form-select-sm") }}
                                    {% if form.priority.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.priority.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Date d'échéance -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-calendar me-2 text-success"></i>
                                    {{ form.due_date.label.text }}
                                </td>
                                <td>
                                    {{ form.due_date(class="form-control form-control-sm", type="date") }}
                                    {% if form.due_date.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.due_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Catégorie -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-tag me-2 text-secondary"></i>
                                    {{ form.category.label.text }}
                                </td>
                                <td>
                                    {{ form.category(class="form-control form-control-sm") }}
                                    {% if form.category.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.category.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Assigné à -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-user me-2 text-info"></i>
                                    Assigné à
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-secondary text-white me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <select class="form-select form-select-sm" name="assigned_to">
                                            <option value="">Non assigné</option>
                                            {% if employees %}
                                                {% for employee in employees %}
                                                <option value="{{ employee.id }}">{{ employee.first_name }} {{ employee.last_name }}</option>
                                                {% endfor %}
                                            {% endif %}
                                        </select>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- Pièce jointe -->
                            {% if form.attachment %}
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-paperclip me-2 text-warning"></i>
                                    {{ form.attachment.label.text }}
                                </td>
                                <td>
                                    {{ form.attachment(class="form-control form-control-sm", type="file") }}
                                    {% if form.attachment.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.attachment.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="text-muted">Formats acceptés: PDF, DOC, DOCX, JPG, PNG (Max: 5MB)</small>
                                </td>
                            </tr>
                            {% endif %}
                            
                            <!-- Actions de communication -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-share me-2 text-primary"></i>
                                    Communication
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-success" onclick="sendWhatsApp()">
                                            <i class="fab fa-whatsapp me-1"></i> WhatsApp
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" onclick="sendEmail()">
                                            <i class="fas fa-envelope me-1"></i> Email
                                        </button>
                                        <button type="button" class="btn btn-outline-info" onclick="generatePDF()">
                                            <i class="fas fa-file-pdf me-1"></i> PDF
                                        </button>
                                    </div>
                                    <small class="text-muted d-block mt-1">Envoyer la tâche après création</small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>

    <!-- Aperçu de la tâche -->
    <div class="task-group mb-4">
        <div class="group-header">
            <div class="d-flex align-items-center">
                <div class="group-indicator bg-info"></div>
                <h5 class="mb-0 me-3">Aperçu de la Tâche</h5>
                <span class="badge bg-info rounded-pill">Prévisualisation</span>
            </div>
        </div>
        
        <div class="group-content">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="5%"></th>
                            <th width="25%">Élément</th>
                            <th width="15%">Personne</th>
                            <th width="15%">Statut</th>
                            <th width="15%">Période</th>
                            <th width="15%">Échéances</th>
                            <th width="10%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr id="preview-row">
                            <td>
                                <input type="checkbox" class="form-check-input" disabled>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-grip-vertical text-muted me-2"></i>
                                    <div>
                                        <div class="fw-medium" id="preview-title">Titre de la tâche...</div>
                                        <small class="text-muted" id="preview-description">Description...</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-secondary text-white me-2">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <span class="text-muted" id="preview-assigned">Non assigné</span>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge status-nouveau" id="preview-status">Nouveau</span>
                            </td>
                            <td>
                                <span class="period-badge" id="preview-period">-</span>
                            </td>
                            <td>
                                <div class="deadline-indicator" id="preview-deadline">
                                    <div class="deadline-bar"></div>
                                </div>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" disabled>
                                        Actions
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block styles %}
<style>
.task-group {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.group-header {
    background: #f8f9fa;
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
}

.group-indicator {
    width: 4px;
    height: 20px;
    border-radius: 2px;
    margin-right: 12px;
}

.group-content {
    background: white;
}

.avatar-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-nouveau {
    background: #e3f2fd;
    color: #1976d2;
}

.period-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.deadline-indicator {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.deadline-bar {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #ff9800, #f44336);
    width: 60%;
    border-radius: 4px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 13px;
    padding: 12px 8px;
}

.table td {
    padding: 12px 8px;
    vertical-align: middle;
    border-top: 1px solid #f1f3f4;
}

.form-control-sm, .form-select-sm {
    border-radius: 6px;
}

.btn-group .btn {
    border-radius: 6px;
    margin-left: 4px;
}

.btn-group .btn:first-child {
    margin-left: 0;
}
</style>
{% endblock %}

{% block scripts %}
<script>
// Aperçu en temps réel
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.querySelector('input[name="title"]');
    const descriptionInput = document.querySelector('textarea[name="description"]');
    const statusSelect = document.querySelector('select[name="status"]');
    const dueDateInput = document.querySelector('input[name="due_date"]');
    
    function updatePreview() {
        // Titre
        const title = titleInput.value || 'Titre de la tâche...';
        document.getElementById('preview-title').textContent = title;
        
        // Description
        const description = descriptionInput.value || 'Description...';
        document.getElementById('preview-description').textContent = 
            description.length > 50 ? description.substring(0, 50) + '...' : description;
        
        // Statut
        const status = statusSelect.value || 'nouveau';
        const statusElement = document.getElementById('preview-status');
        statusElement.className = `status-badge status-${status}`;
        statusElement.textContent = getStatusText(status);
        
        // Date
        if (dueDateInput.value) {
            const date = new Date(dueDateInput.value);
            const options = { day: 'numeric', month: 'short' };
            document.getElementById('preview-period').textContent = 
                date.toLocaleDateString('fr-FR', options);
        }
    }
    
    function getStatusText(status) {
        const statusMap = {
            'nouveau': 'Nouveau',
            'en_cours': 'En cours',
            'termine': 'Terminé',
            'annule': 'Bloqué'
        };
        return statusMap[status] || 'Nouveau';
    }
    
    // Écouter les changements
    titleInput.addEventListener('input', updatePreview);
    descriptionInput.addEventListener('input', updatePreview);
    statusSelect.addEventListener('change', updatePreview);
    dueDateInput.addEventListener('change', updatePreview);
    
    // Mise à jour initiale
    updatePreview();
});

function sendWhatsApp() {
    const title = document.querySelector('input[name="title"]').value;
    if (!title) {
        alert('Veuillez d\'abord saisir un titre pour la tâche');
        return;
    }
    
    const message = `Nouvelle tâche: ${title}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function sendEmail() {
    const title = document.querySelector('input[name="title"]').value;
    const description = document.querySelector('textarea[name="description"]').value;
    
    if (!title) {
        alert('Veuillez d\'abord saisir un titre pour la tâche');
        return;
    }
    
    const subject = `Nouvelle tâche: ${title}`;
    const body = `Bonjour,\n\nUne nouvelle tâche vous a été assignée:\n\nTitre: ${title}\nDescription: ${description}\n\nCordialement`;
    
    const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoUrl;
}

function generatePDF() {
    alert('Fonctionnalité PDF en cours de développement');
}
</script>
{% endblock %}
