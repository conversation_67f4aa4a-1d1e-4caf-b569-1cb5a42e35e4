from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>gin<PERSON>ana<PERSON>
from flask_migrate import Migrate
from flask_babel import Babel

# Initialize extensions
db = SQLAlchemy()
login_manager = LoginManager()
migrate = Migrate()
babel = Babel()

# Configure login manager
login_manager.login_view = 'auth.login'
login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'

@login_manager.user_loader
def load_user(id):
    from models import User
    return User.query.get(int(id))
