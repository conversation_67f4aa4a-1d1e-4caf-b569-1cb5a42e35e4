{% extends "base.html" %}

{% block title %}Tableau de bord - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3"><i class="fas fa-tachometer-alt me-2"></i>Tableau de bord</h1>
        <p class="text-muted">Bienvenue dans le système de gestion des pointages. Voici un aperçu des informations importantes.</p>
    </div>
</div>

<!-- Statistiques -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card dashboard-card card-employees">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">Employés</h5>
                        <h2 class="mb-0">{{ employee_count }}</h2>
                    </div>
                    <div class="col-4 text-end">
                        <div class="card-icon text-primary">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
                <p class="card-text mt-3">
                    <a href="{{ url_for('employee.employees') }}" class="text-decoration-none">Voir tous les employés <i class="fas fa-arrow-right ms-1"></i></a>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card dashboard-card card-present">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">Présents aujourd'hui</h5>
                        <h2 class="mb-0">{{ present_today }}</h2>
                    </div>
                    <div class="col-4 text-end">
                        <div class="card-icon text-success">
                            <i class="fas fa-user-check"></i>
                        </div>
                    </div>
                </div>
                <p class="card-text mt-3">
                    <a href="{{ url_for('attendance.attendance_calendar') }}" class="text-decoration-none">Voir les présences <i class="fas fa-arrow-right ms-1"></i></a>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card dashboard-card card-absent">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">Absents aujourd'hui</h5>
                        <h2 class="mb-0">{{ absent_today }}</h2>
                    </div>
                    <div class="col-4 text-end">
                        <div class="card-icon text-danger">
                            <i class="fas fa-user-times"></i>
                        </div>
                    </div>
                </div>
                <p class="card-text mt-3">
                    <a href="{{ url_for('attendance.attendance_calendar') }}" class="text-decoration-none">Voir les absences <i class="fas fa-arrow-right ms-1"></i></a>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card dashboard-card card-tasks">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">Tâches aujourd'hui</h5>
                        <h2 class="mb-0">{{ completed_tasks }}/{{ total_tasks }}</h2>
                    </div>
                    <div class="col-4 text-end">
                        <div class="card-icon text-warning">
                            <i class="fas fa-tasks"></i>
                        </div>
                    </div>
                </div>
                <p class="card-text mt-3">
                    <a href="{{ url_for('task.tasks') }}" class="text-decoration-none">Voir toutes les tâches <i class="fas fa-arrow-right ms-1"></i></a>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Finances -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card dashboard-card card-income">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">Encaissements</h5>
                        <h2 class="mb-0">{{ income|round(2) }} MAD</h2>
                    </div>
                    <div class="col-4 text-end">
                        <div class="card-icon text-success">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                </div>
                <p class="card-text mt-3">
                    <a href="{{ url_for('finance.income') }}" class="text-decoration-none">Voir les encaissements <i class="fas fa-arrow-right ms-1"></i></a>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card dashboard-card card-expenses">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">Décaissements</h5>
                        <h2 class="mb-0">{{ expenses|round(2) }} MAD</h2>
                    </div>
                    <div class="col-4 text-end">
                        <div class="card-icon text-danger">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                    </div>
                </div>
                <p class="card-text mt-3">
                    <a href="{{ url_for('finance.expenses') }}" class="text-decoration-none">Voir les décaissements <i class="fas fa-arrow-right ms-1"></i></a>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card dashboard-card card-balance">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">Solde</h5>
                        <h2 class="mb-0">{{ balance|round(2) }} MAD</h2>
                    </div>
                    <div class="col-4 text-end">
                        <div class="card-icon text-info">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                    </div>
                </div>
                <p class="card-text mt-3">
                    <a href="{{ url_for('finance.reports') }}" class="text-decoration-none">Voir les rapports <i class="fas fa-arrow-right ms-1"></i></a>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Transactions récentes -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>Transactions récentes</h5>
                <a href="{{ url_for('finance.reports') }}" class="btn btn-sm btn-primary">Voir tout</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Description</th>
                                <th>Type</th>
                                <th class="text-end">Montant (MAD)</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in recent_transactions %}
                            <tr>
                                <td>{{ transaction.date.strftime('%d/%m/%Y') }}</td>
                                <td>{{ transaction.description }}</td>
                                <td>
                                    {% if transaction.transaction_type == 'encaissement' %}
                                    <span class="badge bg-success">Encaissement</span>
                                    {% else %}
                                    <span class="badge bg-danger">Décaissement</span>
                                    {% endif %}
                                </td>
                                <td class="text-end">{{ transaction.amount|round(2) }} MAD</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center">Aucune transaction récente</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
