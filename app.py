from flask import Flask
import os
from dotenv import load_dotenv
import locale
from config import Config
from extensions import db, login_manager, migrate, babel

# Configuration de la langue française et de la devise marocaine
try:
    locale.setlocale(locale.LC_ALL, 'fr_FR.UTF-8')  # Français
except:
    try:
        locale.setlocale(locale.LC_ALL, 'fr_FR')  # Essai sans UTF-8
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'fr_MA.UTF-8')  # Français - Maroc
        except:
            locale.setlocale(locale.LC_ALL, '')  # Utiliser les paramètres par défaut

# Chargement des variables d'environnement
load_dotenv()

def create_app():
    # Création de l'application Flask
    app = Flask(__name__)
    app.config.from_object(Config)

    # Création du dossier d'uploads s'il n'existe pas
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

    # Initialisation des extensions
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)
    babel.init_app(app)

    # Configuration de l'authentification
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'

    # Configuration de Babel
    app.config['BABEL_DEFAULT_LOCALE'] = 'fr'

    # Configuration CSRF améliorée
    app.config['WTF_CSRF_ENABLED'] = True
    app.config['WTF_CSRF_TIME_LIMIT'] = None
    app.config['WTF_CSRF_SSL_STRICT'] = False

    # Ajouter des fonctions utilitaires aux templates
    @app.template_global()
    def moment():
        """Fonction moment pour les templates"""
        from datetime import datetime
        return datetime.now()

    @app.template_global()
    def has_company_blueprint():
        """Vérifier si le blueprint company est disponible"""
        return 'company' in app.blueprints

    @app.template_global()
    def get_action_color(action):
        """Retourne la couleur Bootstrap pour une action"""
        colors = {
            'login': 'success',
            'logout': 'secondary',
            'create': 'primary',
            'update': 'warning',
            'delete': 'danger',
            'view': 'info'
        }
        return colors.get(action, 'secondary')

    @app.template_global()
    def get_action_label(action):
        """Retourne le libellé français pour une action"""
        labels = {
            'login': 'Connexion',
            'logout': 'Déconnexion',
            'create': 'Création',
            'update': 'Modification',
            'delete': 'Suppression',
            'view': 'Consultation'
        }
        return labels.get(action, action.title())

    # Enregistrement des blueprints
    with app.app_context():
        from routes import auth_bp, user_bp, employee_bp, task_bp, attendance_bp, finance_bp, main_bp
        app.register_blueprint(auth_bp)
        app.register_blueprint(user_bp)
        app.register_blueprint(employee_bp)
        app.register_blueprint(task_bp)
        app.register_blueprint(attendance_bp)
        app.register_blueprint(finance_bp)
        app.register_blueprint(main_bp)

        # Importer admin_bp
        try:
            from routes_admin import admin_bp
            app.register_blueprint(admin_bp)
        except Exception as e:
            print(f"⚠️  Admin blueprint non chargé: {e}")

        # Importer company_bp seulement si les tables existent
        try:
            from routes_company import company_bp
            app.register_blueprint(company_bp)
        except Exception as e:
            print(f"⚠️  Company blueprint non chargé: {e}")
            print("💡 Exécutez 'python run_company_migration.py' pour créer les tables manquantes")

    return app

def create_tables(app):
    with app.app_context():
        db.create_all()

        # Création d'un utilisateur administrateur par défaut s'il n'existe pas
        from models import User, SimpleTask
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True,
                can_manage_users=True,
                can_manage_employees=True,
                can_manage_tasks=True,
                can_manage_finances=True,
                can_access_registration_files=True,
                can_access_technical_files=True,
                can_access_reimbursement_files=True,
                can_access_organization_files=True,
                can_access_trainer_files=True,
                can_access_trainer_schedule=True
            )
            admin.set_password('admin')
            db.session.add(admin)
            db.session.commit()
            print('Utilisateur administrateur par défaut créé: admin / admin')

# Création de l'application
app = create_app()

if __name__ == '__main__':
    # Création de la base de données et de l'utilisateur par défaut
    create_tables(app)
    app.run(debug=True, host='0.0.0.0', port=5001)
