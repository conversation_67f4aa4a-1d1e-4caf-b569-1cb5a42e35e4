{% extends "base.html" %}

{% block title %}Gestion de la base de données - Gestion des Pointages{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête de la page -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="mb-3">
                <i class="fas fa-database me-2"></i>
                Gestion de la base de données
            </h1>
            <p class="text-muted">Importez et exportez les données de votre système</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#helpModal">
                    <i class="fas fa-question-circle me-1"></i>
                    Aide
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Section Export -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-download me-2"></i>
                        Exporter les données
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Téléchargez une sauvegarde complète de votre base de données.</p>
                    
                    <form method="POST" action="{{ url_for('admin.export_database') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <div class="mb-3">
                            <label class="form-label">Format d'export</label>
                            <select class="form-select" name="export_format" required>
                                <option value="">Sélectionner un format...</option>
                                <option value="json">JSON (Recommandé)</option>
                                <option value="csv">CSV (Tables séparées)</option>
                                <option value="sql">SQL (Dump complet)</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Tables à exporter</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tables" value="all" id="export_all" checked>
                                <label class="form-check-label" for="export_all">
                                    <strong>Toutes les tables</strong>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tables" value="users" id="export_users">
                                <label class="form-check-label" for="export_users">
                                    Utilisateurs
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tables" value="employees" id="export_employees">
                                <label class="form-check-label" for="export_employees">
                                    Employés
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tables" value="simple_tasks" id="export_tasks">
                                <label class="form-check-label" for="export_tasks">
                                    Tâches
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tables" value="attendance" id="export_attendance">
                                <label class="form-check-label" for="export_attendance">
                                    Présences
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tables" value="financial_records" id="export_financial">
                                <label class="form-check-label" for="export_financial">
                                    Enregistrements financiers
                                </label>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-download me-2"></i>
                                Exporter maintenant
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Section Import -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-upload me-2"></i>
                        Importer les données
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Attention :</strong> L'importation peut écraser les données existantes.
                    </div>
                    
                    <form method="POST" action="{{ url_for('admin.import_database') }}" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <div class="mb-3">
                            <label for="import_file" class="form-label">Fichier à importer</label>
                            <input type="file" class="form-control" name="import_file" id="import_file" 
                                   accept=".json,.csv,.sql" required>
                            <div class="form-text">
                                Formats acceptés : JSON, CSV, SQL (max 50MB)
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Mode d'importation</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="import_mode" value="append" id="mode_append" checked>
                                <label class="form-check-label" for="mode_append">
                                    <strong>Ajouter</strong> aux données existantes
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="import_mode" value="replace" id="mode_replace">
                                <label class="form-check-label" for="mode_replace">
                                    <strong>Remplacer</strong> les données existantes
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="create_backup" id="create_backup" checked>
                                <label class="form-check-label" for="create_backup">
                                    Créer une sauvegarde avant l'importation
                                </label>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" onclick="return confirm('Êtes-vous sûr de vouloir importer ces données ?')">
                                <i class="fas fa-upload me-2"></i>
                                Importer maintenant
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Historique des sauvegardes -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Historique des sauvegardes
                    </h5>
                </div>
                <div class="card-body">
                    {% if backups %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date de création</th>
                                    <th>Type</th>
                                    <th>Taille</th>
                                    <th>Format</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for backup in backups %}
                                <tr>
                                    <td>{{ backup.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ backup.backup_type }}</span>
                                    </td>
                                    <td>{{ backup.file_size_mb }} MB</td>
                                    <td>{{ backup.format.upper() }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('admin.download_backup', backup_id=backup.id) }}" 
                                               class="btn btn-outline-success">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteBackup({{ backup.id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-database fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Aucune sauvegarde disponible</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'aide -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-question-circle me-2"></i>
                    Aide - Gestion de la base de données
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Export des données</h6>
                <ul>
                    <li><strong>JSON :</strong> Format recommandé, préserve la structure des données</li>
                    <li><strong>CSV :</strong> Format tableur, une table par fichier</li>
                    <li><strong>SQL :</strong> Dump complet de la base de données</li>
                </ul>

                <h6 class="mt-4">Import des données</h6>
                <ul>
                    <li><strong>Ajouter :</strong> Conserve les données existantes et ajoute les nouvelles</li>
                    <li><strong>Remplacer :</strong> Supprime les données existantes avant l'import</li>
                </ul>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    Il est recommandé de créer une sauvegarde avant toute importation.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function deleteBackup(backupId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette sauvegarde ?')) {
        fetch(`/admin/backup/delete/${backupId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la suppression');
        });
    }
}

// Gestion des checkboxes d'export
document.getElementById('export_all').addEventListener('change', function() {
    const otherCheckboxes = document.querySelectorAll('input[name="tables"]:not(#export_all)');
    otherCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
        checkbox.disabled = this.checked;
    });
});

document.querySelectorAll('input[name="tables"]:not(#export_all)').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        if (this.checked) {
            document.getElementById('export_all').checked = false;
        }
    });
});
</script>
{% endblock %}
