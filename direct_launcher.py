"""
Lanceur direct pour Gestion des Pointages
Lance directement l'application web sans interface supplémentaire
"""

import os
import sys
import time
import threading
import webbrowser
import warnings

# Ignorer les avertissements
warnings.filterwarnings('ignore')

def setup_paths():
    """Configuration des chemins"""
    if getattr(sys, 'frozen', False):
        application_path = os.path.dirname(sys.executable)
        bundle_dir = sys._MEIPASS
    else:
        application_path = os.path.dirname(os.path.abspath(__file__))
        bundle_dir = application_path
    
    os.chdir(application_path)
    sys.path.insert(0, application_path)
    sys.path.insert(0, bundle_dir)
    
    return application_path, bundle_path

def create_directories():
    """Créer les répertoires nécessaires"""
    dirs = ['instance', 'logs', 'backups', 'uploads', 'static/uploads']
    for directory in dirs:
        try:
            os.makedirs(directory, exist_ok=True)
        except:
            pass

def check_database():
    """Vérifier et créer la base de données si nécessaire"""
    db_path = 'instance/app.db'
    
    if os.path.exists(db_path) and os.path.getsize(db_path) > 1000:
        return True
    
    # Créer une base de données simple
    try:
        import sqlite3
        from werkzeug.security import generate_password_hash
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Table des utilisateurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(64) UNIQUE NOT NULL,
                email VARCHAR(120) UNIQUE NOT NULL,
                password_hash VARCHAR(128) NOT NULL,
                role VARCHAR(20) DEFAULT 'user',
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME
            )
        ''')
        
        # Utilisateur par défaut
        admin_hash = generate_password_hash('admin')
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, email, password_hash, role, is_active)
            VALUES (?, ?, ?, ?, ?)
        ''', ('admin', '<EMAIL>', admin_hash, 'admin', 1))
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        return False

def open_browser():
    """Ouvrir le navigateur après un délai"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5001')
    except:
        pass

def main():
    """Fonction principale - Lance directement l'application Flask"""
    try:
        # Configuration des chemins
        setup_paths()
        create_directories()
        
        # Vérifier la base de données
        if not check_database():
            # Essayer d'exécuter le script de correction
            try:
                import subprocess
                subprocess.run([sys.executable, 'fix_database_final.py'], 
                             capture_output=True, text=True)
            except:
                pass
        
        # Ouvrir le navigateur dans un thread séparé
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # Importer et lancer l'application Flask directement
        from app import create_app
        
        app = create_app()
        app.config.update({
            'DEBUG': False,
            'TESTING': False,
            'SECRET_KEY': 'gestion-pointages-2025-secure',
            'SQLALCHEMY_DATABASE_URI': 'sqlite:///instance/app.db',
            'SQLALCHEMY_TRACK_MODIFICATIONS': False,
            'WTF_CSRF_ENABLED': True,
        })
        
        # Lancer le serveur Flask directement
        app.run(
            host='0.0.0.0',  # Accessible sur le réseau local
            port=5001,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except Exception as e:
        # En cas d'erreur, essayer de lancer avec start.py
        try:
            from start import main as start_main
            start_main()
        except:
            pass

if __name__ == '__main__':
    main()
