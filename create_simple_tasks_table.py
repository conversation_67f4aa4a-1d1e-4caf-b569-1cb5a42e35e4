#!/usr/bin/env python3
"""
Script pour créer la table simple_tasks dans la base de données
"""

import os
import sys
import sqlite3
from datetime import datetime

def create_simple_tasks_table():
    """Crée la table simple_tasks dans la base de données"""
    
    # Chemin vers la base de données
    db_path = os.path.join('instance', 'app.db')
    
    if not os.path.exists(db_path):
        print(f"Erreur: Le fichier de base de données {db_path} n'existe pas.")
        print("Assurez-vous que l'application Flask a été initialisée au moins une fois.")
        return False
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vérifier si la table existe déjà
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='simple_tasks'
        """)
        
        if cursor.fetchone():
            print("La table 'simple_tasks' existe déjà.")
            conn.close()
            return True
        
        # Créer la table simple_tasks
        cursor.execute("""
            CREATE TABLE simple_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title VARCHAR(128) NOT NULL,
                description TEXT,
                status VARCHAR(20) DEFAULT 'new',
                priority VARCHAR(20) DEFAULT 'normal',
                due_date DATE,
                category VARCHAR(50),
                tags TEXT,
                attachment_path VARCHAR(255),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Créer un trigger pour mettre à jour updated_at automatiquement
        cursor.execute("""
            CREATE TRIGGER update_simple_tasks_updated_at 
            AFTER UPDATE ON simple_tasks
            BEGIN
                UPDATE simple_tasks SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        """)
        
        # Insérer quelques tâches d'exemple
        sample_tasks = [
            (
                "Configurer le système de gestion des tâches",
                "Mettre en place le nouveau système simple de gestion des tâches avec toutes les fonctionnalités de base.",
                "completed",
                "high",
                "2024-01-15",
                "Développement",
                "système, configuration, tâches"
            ),
            (
                "Tester les fonctionnalités de base",
                "Vérifier que toutes les fonctionnalités fonctionnent correctement : ajout, modification, suppression, affichage.",
                "in_progress",
                "normal",
                "2024-01-20",
                "Tests",
                "tests, fonctionnalités, validation"
            ),
            (
                "Documenter le système",
                "Créer la documentation utilisateur pour le nouveau système de gestion des tâches.",
                "new",
                "low",
                "2024-01-25",
                "Documentation",
                "documentation, utilisateur, guide"
            ),
            (
                "Formation des utilisateurs",
                "Organiser une session de formation pour les utilisateurs du système.",
                "new",
                "normal",
                "2024-01-30",
                "Formation",
                "formation, utilisateurs, session"
            )
        ]
        
        cursor.executemany("""
            INSERT INTO simple_tasks (title, description, status, priority, due_date, category, tags)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, sample_tasks)
        
        # Valider les changements
        conn.commit()
        
        print("✅ Table 'simple_tasks' créée avec succès!")
        print(f"✅ {len(sample_tasks)} tâches d'exemple ajoutées.")
        
        # Afficher les tâches créées
        cursor.execute("SELECT id, title, status, priority FROM simple_tasks")
        tasks = cursor.fetchall()
        
        print("\n📋 Tâches créées:")
        for task in tasks:
            print(f"  - #{task[0]}: {task[1]} (Statut: {task[2]}, Priorité: {task[3]})")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Erreur SQLite: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Création de la table simple_tasks...")
    print("=" * 50)
    
    if create_simple_tasks_table():
        print("\n✅ Migration terminée avec succès!")
        print("\nVous pouvez maintenant utiliser le nouveau système de gestion des tâches.")
        print("Redémarrez l'application Flask pour voir les changements.")
    else:
        print("\n❌ Échec de la migration.")
        print("Vérifiez les erreurs ci-dessus et réessayez.")
        sys.exit(1)

if __name__ == "__main__":
    main()
