@echo off
echo 🚀 Demarrage rapide de Gestion des Pointages
echo ================================================

echo 🔍 Verification de Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python non trouve
    pause
    exit /b 1
)

echo ✅ Python trouve

echo 🔍 Verification des modules...
python -c "import flask; print('✅ Flask')" 2>nul || echo ❌ Flask manquant
python -c "import flask_sqlalchemy; print('✅ Flask-SQLAlchemy')" 2>nul || echo ❌ Flask-SQLAlchemy manquant
python -c "import flask_login; print('✅ Flask-Login')" 2>nul || echo ❌ Flask-Login manquant

echo.
echo 🚀 Demarrage du serveur...
echo 💡 Le navigateur s'ouvrira automatiquement
echo 👤 Utilisateur: admin
echo 🔑 Mot de passe: admin
echo.
echo ⚠️  Appuyez sur Ctrl+C pour arreter
echo ================================================

python start_server.py

pause
