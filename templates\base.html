<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gestion des Pointages{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Font Awesome Brands (pour WhatsApp) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/brands.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    {% block styles %}{% endblock %}
</head>
<body>
    {% if current_user.is_authenticated %}
    <div class="app-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h6 class="sidebar-brand">
                    <i class="fas fa-clipboard-check me-2"></i>
                    Gestion des Pointages
                </h6>
            </div>

            <nav class="sidebar-nav">
                <ul class="nav flex-column">
                    <!-- Tableau de bord -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'main.dashboard' %}active{% endif %}" href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            <span>Tableau de bord</span>
                        </a>
                    </li>

                    <!-- Gestion des employés -->
                    {% if current_user.can_manage_employees or current_user.is_admin %}
                    <li class="nav-item">
                        <a class="nav-link {% if 'employee' in request.endpoint %}active{% endif %}" href="{{ url_for('employee.employees') }}">
                            <i class="fas fa-users me-2"></i>
                            <span>Employés</span>
                        </a>
                    </li>
                    {% endif %}

                    <!-- Gestion des tâches -->
                    {% if current_user.can_manage_tasks or current_user.is_admin %}
                    <li class="nav-item">
                        <a class="nav-link {% if 'task' in request.endpoint %}active{% endif %}" href="{{ url_for('task.tasks') }}">
                            <i class="fas fa-tasks me-2"></i>
                            <span>Tâches</span>
                        </a>
                    </li>
                    {% endif %}

                    <!-- Présences -->
                    <li class="nav-item">
                        <a class="nav-link {% if 'attendance' in request.endpoint %}active{% endif %}" href="{{ url_for('attendance.attendance_calendar') }}">
                            <i class="fas fa-calendar-check me-2"></i>
                            <span>Présences</span>
                        </a>
                    </li>

                    <!-- Finances -->
                    {% if current_user.can_manage_finances or current_user.is_admin %}
                    <li class="nav-item">
                        <a class="nav-link {% if 'finance' in request.endpoint %}active{% endif %}" href="{{ url_for('finance.income') }}">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            <span>Finances</span>
                        </a>
                    </li>
                    {% endif %}

                    <!-- Gestion des utilisateurs -->
                    {% if current_user.is_admin or current_user.can_manage_users %}
                    <li class="nav-item">
                        <a class="nav-link {% if 'user' in request.endpoint %}active{% endif %}" href="{{ url_for('user.users') }}">
                            <i class="fas fa-user-cog me-2"></i>
                            <span>Utilisateurs</span>
                        </a>
                    </li>
                    {% endif %}

                    <!-- Administration -->
                    {% if current_user.is_admin %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if 'admin' in request.endpoint or 'company' in request.endpoint %}active{% endif %}"
                           href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-cogs me-2"></i>
                            <span>Administration</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('admin.database_management') }}">
                                    <i class="fas fa-database me-2"></i>
                                    Base de données
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('admin.user_activities') }}">
                                    <i class="fas fa-user-clock me-2"></i>
                                    Activités utilisateurs
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('company.company_info') }}">
                                    <i class="fas fa-building me-2"></i>
                                    Informations entreprise
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
            </nav>

            <!-- User Menu -->
            <div class="sidebar-footer">
                <div class="user-menu">
                    <div class="user-info">
                        <i class="fas fa-user-circle me-2"></i>
                        <span class="username">{{ current_user.username }}</span>
                    </div>
                    <div class="user-actions">
                        <a href="#" class="user-action" title="Profil">
                            <i class="fas fa-user"></i>
                        </a>
                        <a href="#" class="user-action" title="Paramètres">
                            <i class="fas fa-cog"></i>
                        </a>
                        <a href="{{ url_for('auth.logout') }}" class="user-action text-danger" title="Déconnexion">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
    {% endif %}
            <!-- Flash Messages -->
            <div class="content-messages">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>

            <!-- Page Content -->
            <div class="page-content">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2023 Gestion des Pointages. Tous droits réservés.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>Version 1.0.0</p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
