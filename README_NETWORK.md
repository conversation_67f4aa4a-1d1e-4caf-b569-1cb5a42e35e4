# Gestion des Pointages - Version Réseau

## Aperçu Général

Système complet de gestion des pointages conçu pour fonctionner en environnement réseau local avec support multi-utilisateurs. Le programme supporte toutes les versions de Windows de 7 à 11 pour les systèmes 32 et 64 bits.

### Fonctionnalités Principales

- ✅ **Support réseau local:** Fonctionnement multi-utilisateurs sur réseau local
- ✅ **Base de données centralisée:** Stockage de toutes les données sur le serveur principal
- ✅ **Interface française:** Interface utilisateur entièrement en français
- ✅ **Gestion des employés:** Ajout, modification et suppression des données employés
- ✅ **Suivi des pointages:** Enregistrement des heures d'arrivée et de départ
- ✅ **Rapports:** Rapports complets journaliers, hebdomadaires et mensuels
- ✅ **Impression:** Modèles d'impression professionnels avec logo de l'entreprise
- ✅ **Sauvegarde:** Sauvegarde automatique des données
- ✅ **Sécurité:** Système d'utilisateurs multi-niveaux
- ✅ **Devise marocaine:** Support du Dirham marocain (MAD)

## متطلبات النظام

### الحد الأدنى
- **نظام التشغيل:** Windows 7 SP1 أو أحدث
- **المعالج:** Intel/AMD 1 GHz
- **الذاكرة:** 2 GB RAM
- **مساحة القرص:** 500 MB
- **الشبكة:** اتصال شبكة محلية (LAN)

### الموصى به
- **نظام التشغيل:** Windows 10/11
- **المعالج:** Intel/AMD 2 GHz أو أسرع
- **الذاكرة:** 4 GB RAM أو أكثر
- **مساحة القرص:** 1 GB
- **الشبكة:** شبكة محلية سريعة (100 Mbps+)

## التثبيت السريع

### للمطورين

1. **استنساخ المشروع:**
   ```bash
   git clone https://github.com/your-repo/gestion-pointages.git
   cd gestion-pointages
   ```

2. **تثبيت المتطلبات:**
   ```bash
   pip install -r requirements.txt
   ```

3. **تشغيل التطبيق:**
   ```bash
   python app.py
   ```

### للمستخدمين النهائيين

1. **تحميل برنامج التثبيت:**
   - قم بتحميل `GestionPointages_Setup_v1.0.0.exe`

2. **تشغيل المثبت:**
   - انقر بزر الماوس الأيمن واختر "تشغيل كمسؤول"
   - اتبع تعليمات المثبت

3. **اختيار نوع التثبيت:**
   - **خادم:** للكمبيوتر الرئيسي
   - **عميل:** للأجهزة الأخرى

## بناء الملف التنفيذي

### الطريقة السريعة
```bash
# تشغيل سكريبت البناء
BUILD_NETWORK_EXE.bat
```

### الطريقة اليدوية
```bash
# بناء الملف التنفيذي
python build_network_exe.py

# إنشاء برنامج التثبيت (يتطلب Inno Setup)
# افتح setup_network_installer.iss في Inno Setup واضغط F9
```

## الهيكل التقني

### التقنيات المستخدمة
- **Backend:** Flask (Python)
- **Database:** SQLite مع دعم الشبكة
- **Frontend:** Bootstrap 4 + Jinja2
- **Packaging:** PyInstaller
- **Installer:** Inno Setup

### هيكل المشروع
```
gestion-pointages/
├── app.py                          # التطبيق الرئيسي
├── config.py                       # إعدادات التطبيق
├── config_network.py               # إعدادات الشبكة
├── models.py                       # نماذج قاعدة البيانات
├── routes.py                       # مسارات التطبيق
├── forms.py                        # نماذج الويب
├── extensions.py                   # إضافات Flask
├── templates/                      # قوالب HTML
├── static/                         # الملفات الثابتة
├── instance/                       # قاعدة البيانات
├── migrations/                     # ترحيل قاعدة البيانات
├── build_network_exe.py           # سكريبت البناء
├── GestionPointages_Network.spec  # إعدادات PyInstaller
├── setup_network_installer.iss    # إعدادات Inno Setup
└── requirements.txt                # المتطلبات
```

## الاستخدام

### تسجيل الدخول
- **المدير الافتراضي:** admin / admin123
- **كلمة المرور:** يجب تغييرها عند أول تسجيل دخول

### إدارة الموظفين
1. انتقل إلى قسم "الموظفين"
2. انقر "إضافة موظف جديد"
3. املأ البيانات المطلوبة
4. احفظ البيانات

### تسجيل الحضور
1. انتقل إلى قسم "الحضور"
2. اختر الموظف
3. انقر "تسجيل الحضور" أو "تسجيل الانصراف"

### التقارير
1. انتقل إلى قسم "التقارير"
2. اختر نوع التقرير والفترة
3. انقر "إنشاء التقرير"
4. يمكنك طباعة أو تصدير التقرير

## إعداد الشبكة

### الخادم الرئيسي
1. ثبت البرنامج كخادم
2. شغل البرنامج من "Gestion des Pointages - Serveur"
3. شارك عنوان IP مع المستخدمين الآخرين

### أجهزة العملاء
1. ثبت البرنامج كعميل
2. أدخل عنوان IP الخادم
3. شغل البرنامج من "Gestion des Pointages - Client"

## استكشاف الأخطاء

### مشاكل الشبكة
- تحقق من عنوان IP الخادم
- تأكد من عمل الخادم
- فحص إعدادات الحماية (Firewall)

### مشاكل قاعدة البيانات
- تحقق من مساحة القرص
- تأكد من صلاحيات الكتابة
- استخدم النسخة الاحتياطية

## الملفات المهمة

### ملفات التكوين
- `config_network.py` - إعدادات الشبكة
- `network_config.json` - تكوين الشبكة المحلية
- `version_info.json` - معلومات الإصدار

### ملفات البناء
- `build_network_exe.py` - سكريبت بناء الملف التنفيذي
- `GestionPointages_Network.spec` - إعدادات PyInstaller
- `setup_network_installer.iss` - إعدادات Inno Setup
- `BUILD_NETWORK_EXE.bat` - سكريبت بناء سريع

### ملفات التشغيل
- `start_server.bat` - تشغيل الخادم
- `start_client.bat` - تشغيل العميل

## التطوير

### إعداد بيئة التطوير
```bash
# إنشاء بيئة افتراضية
python -m venv venv
venv\Scripts\activate

# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل التطبيق في وضع التطوير
python app.py
```

### اختبار الشبكة
```bash
# اختبار الخادم
python -c "from config_network import network_config; print(network_config.get_local_ip())"

# اختبار الاتصال
python -c "from config_network import network_config; print(network_config.test_server_connection('*************'))"
```

## الدعم

- **التوثيق:** راجع ملف `دليل_التثبيت_والتشغيل.md`
- **البريد الإلكتروني:** <EMAIL>
- **المشاكل:** استخدم GitHub Issues

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## الإصدارات

### الإصدار 1.0.0
- الإصدار الأولي للشبكة المحلية
- دعم متعدد المستخدمين
- واجهة عربية كاملة
- نظام تقارير شامل
- دعم Windows 7-11 (32/64 bit)
