# Guide d'Installation et d'Utilisation
## Système de Gestion des Pointages v1.0.0

### 📋 Table des Matières
1. [Prérequis Système](#prérequis-système)
2. [Construction de l'Exécutable](#construction-de-lexécutable)
3. [Installation](#installation)
4. [Configuration Multi-Utilisateurs](#configuration-multi-utilisateurs)
5. [Utilisation](#utilisation)
6. [Dépannage](#dépannage)

---

## 🖥️ Prérequis Système

### Systèmes d'Exploitation Supportés
- ✅ Windows 7 (32/64 bits)
- ✅ Windows 8 (32/64 bits)
- ✅ Windows 8.1 (32/64 bits)
- ✅ Windows 10 (32/64 bits)
- ✅ Windows 11 (64 bits)

### Configuration Minimale
- **RAM**: 2 GB minimum, 4 GB recommandé
- **Espace disque**: 500 MB minimum
- **Réseau**: Connexion réseau local (LAN) pour le mode multi-utilisateurs
- **Privilèges**: Droits administrateur pour l'installation

### Aucune Installation Supplémentaire Requise
- ❌ Pas besoin de Python
- ❌ Pas besoin de base de données externe
- ❌ Pas besoin de serveur web
- ✅ Application autonome complète

---

## 🔨 Construction de l'Exécutable

### Étape 1: Préparation de l'Environnement
```bash
# Installer les dépendances de construction
pip install pyinstaller==5.13.2 auto-py-to-exe==2.40.0

# Ou utiliser le script automatique
python build_exe.py
```

### Étape 2: Construction Automatique
```bash
# Lancer le script de construction
python build_exe.py
```

Le script effectuera automatiquement:
- ✅ Installation des dépendances
- ✅ Nettoyage des répertoires
- ✅ Construction de l'exécutable
- ✅ Création du package d'installation
- ✅ Génération du script Inno Setup

### Étape 3: Résultats de la Construction
Après construction, vous obtiendrez:
- `dist/GestionPointages.exe` - Exécutable principal
- `GestionPointages_Setup_v1.0.0.zip` - Package d'installation
- `setup_script.iss` - Script pour Inno Setup

---

## 📦 Installation

### Option 1: Installation Simple (ZIP)
1. **Télécharger** le package `GestionPointages_Setup_v1.0.0.zip`
2. **Extraire** le contenu dans un dossier de votre choix
3. **Lancer** `GestionPointages.exe`

### Option 2: Installation Professionnelle (Inno Setup)
1. **Installer** Inno Setup Compiler
2. **Ouvrir** le fichier `setup_script.iss`
3. **Compiler** pour créer l'installateur Windows
4. **Exécuter** l'installateur généré

### Option 3: Installation Manuelle
1. **Copier** `GestionPointages.exe` dans `C:\Program Files\Gestion des Pointages\`
2. **Créer** un raccourci sur le bureau
3. **Lancer** l'application

---

## 🌐 Configuration Multi-Utilisateurs

### Architecture Réseau
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SERVEUR       │    │    CLIENT 1     │    │    CLIENT 2     │
│  (Principal)    │◄──►│  (Secondaire)   │    │  (Secondaire)   │
│                 │    │                 │    │                 │
│ • Base données  │    │ • Interface     │    │ • Interface     │
│ • Interface     │    │ • Connexion     │    │ • Connexion     │
│ • Port 5001     │    │   réseau        │    │   réseau        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Configuration du Serveur (Ordinateur Principal)

#### 1. Lancement Initial
- Démarrer `GestionPointages.exe`
- Sélectionner **"Serveur (Ordinateur principal)"**
- Configurer le port (défaut: 5001)
- Cliquer **"Démarrer l'Application"**

#### 2. Configuration Réseau
- L'application sera accessible via: `http://[IP_SERVEUR]:5001`
- Exemple: `http://*************:5001`
- Noter l'adresse IP affichée dans les informations système

#### 3. Partage de Base de Données
- La base de données est automatiquement partagée
- Emplacement: `instance/app.db`
- Sauvegarde automatique activée

### Configuration des Clients (Ordinateurs Secondaires)

#### 1. Lancement Initial
- Démarrer `GestionPointages.exe`
- Sélectionner **"Client (Ordinateur secondaire)"**
- Entrer l'adresse IP du serveur
- Cliquer **"Tester la connexion"**

#### 2. Détection Automatique
- Cliquer **"Détecter automatiquement"**
- Le système scannera le réseau local
- Sélection automatique du serveur trouvé

#### 3. Connexion
- Cliquer **"Démarrer l'Application"**
- L'application s'ouvrira dans le navigateur
- Connexion automatique au serveur

---

## 🚀 Utilisation

### Premier Démarrage

#### 1. Lancement de l'Application
```
GestionPointages.exe
├── Interface de Configuration
├── Sélection du Mode (Serveur/Client)
├── Configuration Réseau
└── Démarrage de l'Application Web
```

#### 2. Accès Web
- **URL Locale**: `http://localhost:5001`
- **URL Réseau**: `http://[IP_SERVEUR]:5001`
- **Navigateur**: Ouverture automatique

#### 3. Connexion Initiale
- **Utilisateur**: `admin`
- **Mot de passe**: `admin`
- **Langue**: Français (par défaut)

### Fonctionnalités Principales

#### 📊 Gestion des Pointages
- Enregistrement des présences/absences
- Calendrier de présence
- Rapports de pointage
- Notes et commentaires

#### 👥 Gestion des Employés
- Ajout/modification/suppression
- Informations personnelles
- Postes et salaires
- Historique des activités

#### 📋 Gestion des Tâches
- Création et assignation
- Suivi des statuts
- Priorités et échéances
- Communication (WhatsApp/Email)

#### 💰 Gestion Financière
- Revenus et dépenses
- Conversion en MAD
- Rapports financiers
- Historique des transactions

#### ⚙️ Administration
- Gestion des utilisateurs
- Permissions et rôles
- Sauvegarde/restauration
- Configuration système

---

## 🔧 Dépannage

### Problèmes Courants

#### ❌ "L'application ne démarre pas"
**Solutions:**
1. Vérifier les droits administrateur
2. Désactiver temporairement l'antivirus
3. Vérifier que le port 5001 n'est pas utilisé
4. Relancer en tant qu'administrateur

#### ❌ "Impossible de se connecter au serveur"
**Solutions:**
1. Vérifier l'adresse IP du serveur
2. Tester la connectivité réseau: `ping [IP_SERVEUR]`
3. Vérifier le pare-feu Windows
4. S'assurer que le serveur est démarré

#### ❌ "Erreur de base de données"
**Solutions:**
1. Vérifier les permissions du dossier `instance/`
2. Restaurer depuis une sauvegarde
3. Recréer la base de données
4. Vérifier l'espace disque disponible

#### ❌ "Page non trouvée dans le navigateur"
**Solutions:**
1. Vérifier l'URL: `http://localhost:5001`
2. Attendre le démarrage complet (30 secondes)
3. Rafraîchir la page (F5)
4. Essayer un autre navigateur

### Configuration Réseau Avancée

#### Pare-feu Windows
```cmd
# Autoriser le port 5001
netsh advfirewall firewall add rule name="Gestion Pointages" dir=in action=allow protocol=TCP localport=5001
```

#### Test de Connectivité
```cmd
# Tester la connexion au serveur
telnet [IP_SERVEUR] 5001

# Scanner les ports ouverts
netstat -an | findstr :5001
```

### Logs et Diagnostic

#### Fichiers de Log
- `logs/app.log` - Logs de l'application
- `logs/error.log` - Logs d'erreurs
- `logs/access.log` - Logs d'accès

#### Mode Debug
1. Modifier `config_network.py`
2. Activer `enable_logging: true`
3. Définir `log_level: "DEBUG"`
4. Redémarrer l'application

---

## 📞 Support Technique

### Informations Système
- **Version**: 1.0.0
- **Date de Release**: 2025
- **Compatibilité**: Windows 7-11
- **Architecture**: 32/64 bits

### Contact
- **Documentation**: Consultez ce guide
- **Logs**: Vérifiez les fichiers de log
- **Communauté**: Forums de support
- **Support Technique**: Contact direct

---

## 📝 Notes Importantes

### Sécurité
- ⚠️ Changez le mot de passe admin par défaut
- ⚠️ Configurez des sauvegardes régulières
- ⚠️ Limitez l'accès réseau si nécessaire
- ⚠️ Mettez à jour régulièrement

### Performance
- 💡 Un seul serveur par réseau local
- 💡 Maximum 50 connexions simultanées
- 💡 Sauvegarde automatique toutes les heures
- 💡 Nettoyage des logs ancien

### Maintenance
- 🔄 Sauvegarde quotidienne recommandée
- 🔄 Redémarrage hebdomadaire du serveur
- 🔄 Vérification des logs régulière
- 🔄 Test de connectivité périodique

---

**🎉 Félicitations! Votre Système de Gestion des Pointages est maintenant prêt à l'emploi!**
