"""
Configuration réseau pour l'environnement multi-utilisateurs
Système de Gestion des Pointages
"""

import os
import socket
import json
from pathlib import Path

class NetworkConfig:
    """Configuration pour l'environnement réseau multi-utilisateurs"""
    
    def __init__(self):
        self.config_file = Path("network_config.json")
        self.default_config = {
            "server_mode": False,
            "server_host": "0.0.0.0",
            "server_port": 5001,
            "client_server_ip": "*************",
            "database_path": "instance/app.db",
            "shared_database_path": None,
            "auto_detect_server": True,
            "backup_interval": 3600,  # 1 heure en secondes
            "max_connections": 50,
            "connection_timeout": 30,
            "language": "fr",
            "company_name": "Gestion des Pointages",
            "enable_logging": True,
            "log_level": "INFO"
        }
        self.config = self.load_config()
    
    def load_config(self):
        """Charger la configuration depuis le fichier"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # Fusionner avec la configuration par défaut
                merged_config = self.default_config.copy()
                merged_config.update(config)
                return merged_config
            except Exception as e:
                print(f"Erreur lors du chargement de la configuration: {e}")
                return self.default_config.copy()
        else:
            return self.default_config.copy()
    
    def save_config(self):
        """Sauvegarder la configuration dans le fichier"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Erreur lors de la sauvegarde de la configuration: {e}")
            return False
    
    def get_local_ip(self):
        """Obtenir l'adresse IP locale"""
        try:
            # Créer une connexion socket pour obtenir l'IP locale
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            return "127.0.0.1"
    
    def is_server_mode(self):
        """Vérifier si l'application fonctionne en mode serveur"""
        return self.config.get("server_mode", False)
    
    def set_server_mode(self, enabled=True):
        """Activer/désactiver le mode serveur"""
        self.config["server_mode"] = enabled
        if enabled:
            self.config["server_host"] = "0.0.0.0"
        else:
            self.config["server_host"] = "127.0.0.1"
        self.save_config()
    
    def get_server_config(self):
        """Obtenir la configuration du serveur"""
        return {
            "host": self.config.get("server_host", "0.0.0.0"),
            "port": self.config.get("server_port", 5001),
            "debug": False,
            "threaded": True
        }
    
    def get_database_config(self):
        """Obtenir la configuration de la base de données"""
        if self.is_server_mode():
            # Mode serveur - utiliser la base de données locale
            db_path = self.config.get("database_path", "instance/app.db")
        else:
            # Mode client - utiliser la base de données partagée si configurée
            shared_path = self.config.get("shared_database_path")
            if shared_path and os.path.exists(shared_path):
                db_path = shared_path
            else:
                db_path = self.config.get("database_path", "instance/app.db")
        
        # Créer le répertoire instance s'il n'existe pas
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        return f"sqlite:///{db_path}"
    
    def detect_server(self):
        """Détecter automatiquement le serveur sur le réseau"""
        if not self.config.get("auto_detect_server", True):
            return None
        
        # Scanner les adresses IP communes du réseau local
        local_ip = self.get_local_ip()
        network_base = ".".join(local_ip.split(".")[:-1]) + "."
        port = self.config.get("server_port", 5001)
        
        for i in range(1, 255):
            ip = network_base + str(i)
            if ip == local_ip:
                continue
            
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((ip, port))
                sock.close()
                
                if result == 0:
                    # Serveur trouvé
                    self.config["client_server_ip"] = ip
                    self.save_config()
                    return ip
            except Exception:
                continue
        
        return None
    
    def test_server_connection(self, ip=None):
        """Tester la connexion au serveur"""
        if ip is None:
            ip = self.config.get("client_server_ip", "127.0.0.1")
        
        port = self.config.get("server_port", 5001)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.config.get("connection_timeout", 30))
            result = sock.connect_ex((ip, port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def get_client_database_url(self):
        """Obtenir l'URL de la base de données pour le client"""
        server_ip = self.config.get("client_server_ip", "127.0.0.1")
        
        # Pour SQLite, nous utilisons un chemin réseau partagé
        # En production, cela pourrait être un serveur de base de données dédié
        shared_path = f"\\\\{server_ip}\\GestionPointages\\instance\\app.db"
        
        if os.path.exists(shared_path):
            return f"sqlite:///{shared_path}"
        else:
            # Fallback vers la base de données locale
            return self.get_database_config()
    
    def setup_network_share(self):
        """Configurer le partage réseau (Windows)"""
        if not self.is_server_mode():
            return False
        
        try:
            import subprocess
            
            # Créer un partage réseau pour la base de données
            share_name = "GestionPointages"
            share_path = os.path.abspath("instance")
            
            # Commande pour créer le partage
            cmd = f'net share {share_name}="{share_path}" /grant:everyone,full'
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"Partage réseau créé: \\\\{self.get_local_ip()}\\{share_name}")
                return True
            else:
                print(f"Erreur lors de la création du partage: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"Erreur lors de la configuration du partage réseau: {e}")
            return False

    def create_startup_scripts(self):
        """Créer les scripts de démarrage pour serveur et client"""
        # Script pour le serveur
        server_script = f"""@echo off
title Serveur - {self.config.get('company_name', 'Gestion des Pointages')}
color 0A
echo ===============================================
echo    {self.config.get('company_name', 'Gestion des Pointages')} - SERVEUR
echo ===============================================
echo.
echo Démarrage du serveur...
echo IP du serveur: {self.get_local_ip()}
echo Port: {self.config.get('server_port', 5001)}
echo.
echo IMPORTANT:
echo - Le serveur est maintenant accessible aux autres ordinateurs
echo - Partagez cette adresse IP avec les autres utilisateurs
echo - Pour arrêter le serveur, fermez cette fenêtre
echo.
echo ===============================================
echo Serveur en cours d'exécution...
echo ===============================================
"""

        # Script pour le client
        client_script = f"""@echo off
title Client - {self.config.get('company_name', 'Gestion des Pointages')}
color 0B
echo ===============================================
echo    {self.config.get('company_name', 'Gestion des Pointages')} - CLIENT
echo ===============================================
echo.
echo Connexion au serveur...
echo Serveur: {self.config.get('client_server_ip', '*************')}:{self.config.get('server_port', 5001)}
echo.
echo Vérification de la connexion...
ping -n 1 {self.config.get('client_server_ip', '*************')} >nul 2>&1
if errorlevel 1 (
    echo.
    echo ERREUR: Impossible de se connecter au serveur!
    echo.
    echo Vérifications à effectuer:
    echo 1. Le serveur est-il démarré?
    echo 2. L'adresse IP est-elle correcte?
    echo 3. Le pare-feu bloque-t-il la connexion?
    echo.
    pause
    exit /b 1
)
echo Connexion établie avec succès!
echo.
echo ===============================================
echo Client connecté au serveur
echo ===============================================
"""

        # Sauvegarder les scripts
        server_path = Path("start_server.bat")
        client_path = Path("start_client.bat")

        try:
            with open(server_path, 'w', encoding='cp1252') as f:
                f.write(server_script)
            with open(client_path, 'w', encoding='cp1252') as f:
                f.write(client_script)
            return True
        except Exception as e:
            print(f"Erreur lors de la création des scripts: {e}")
            return False

    def get_network_info(self):
        """Obtenir les informations réseau complètes"""
        return {
            "local_ip": self.get_local_ip(),
            "server_mode": self.is_server_mode(),
            "server_config": self.get_server_config(),
            "database_config": self.get_database_config(),
            "company_name": self.config.get('company_name', 'Gestion des Pointages'),
            "language": self.config.get('language', 'fr')
        }

# Instance globale de configuration
network_config = NetworkConfig()
