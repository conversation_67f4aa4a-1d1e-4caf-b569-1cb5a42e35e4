"""
Application simple - Gestion des Pointages
Version simplifiée qui fonctionne directement
"""

import os
import sys
import time
import threading
import webbrowser
import sqlite3
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, session
from werkzeug.security import generate_password_hash, check_password_hash

# Configuration de base
if getattr(sys, 'frozen', False):
    template_folder = os.path.join(sys._MEIPASS, 'templates')
    static_folder = os.path.join(sys._MEIPASS, 'static')
else:
    template_folder = 'templates'
    static_folder = 'static'

app = Flask(__name__, template_folder=template_folder, static_folder=static_folder)
app.secret_key = 'gestion-pointages-secret-key-2025'

# Configuration de la base de données
DATABASE = 'simple_app.db'

def init_db():
    """Initialiser la base de données"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    # Table des utilisateurs
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            email TEXT,
            role TEXT DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Table des employés
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            cin TEXT,
            position TEXT,
            salary REAL,
            hire_date DATE,
            phone TEXT,
            email TEXT,
            address TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Table des tâches
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            status TEXT DEFAULT 'new',
            priority TEXT DEFAULT 'medium',
            assigned_to INTEGER,
            due_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (assigned_to) REFERENCES employees (id)
        )
    ''')
    
    # Créer l'utilisateur admin par défaut
    cursor.execute('SELECT * FROM users WHERE username = ?', ('admin',))
    if not cursor.fetchone():
        admin_hash = generate_password_hash('admin')
        cursor.execute('''
            INSERT INTO users (username, password_hash, email, role)
            VALUES (?, ?, ?, ?)
        ''', ('admin', admin_hash, '<EMAIL>', 'admin'))
    
    conn.commit()
    conn.close()
    print("✓ Base de données initialisée")

def get_db_connection():
    """Obtenir une connexion à la base de données"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

# Routes principales
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('dashboard.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = get_db_connection()
        user = conn.execute('SELECT * FROM users WHERE username = ?', (username,)).fetchone()
        conn.close()
        
        if user and check_password_hash(user['password_hash'], password):
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['role'] = user['role']
            return redirect(url_for('index'))
        else:
            flash('Nom d\'utilisateur ou mot de passe incorrect')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

@app.route('/employees')
def employees():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    conn = get_db_connection()
    employees = conn.execute('SELECT * FROM employees ORDER BY created_at DESC').fetchall()
    conn.close()
    
    return render_template('employees.html', employees=employees)

@app.route('/add_employee', methods=['GET', 'POST'])
def add_employee():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        conn = get_db_connection()
        conn.execute('''
            INSERT INTO employees (first_name, last_name, cin, position, salary, hire_date, phone, email, address)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            request.form['first_name'],
            request.form['last_name'],
            request.form['cin'],
            request.form['position'],
            float(request.form['salary']) if request.form['salary'] else None,
            request.form['hire_date'] if request.form['hire_date'] else None,
            request.form['phone'],
            request.form['email'],
            request.form['address']
        ))
        conn.commit()
        conn.close()
        
        flash('Employé ajouté avec succès')
        return redirect(url_for('employees'))
    
    return render_template('add_employee.html')

@app.route('/tasks')
def tasks():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    conn = get_db_connection()
    tasks = conn.execute('''
        SELECT t.*, e.first_name, e.last_name 
        FROM tasks t 
        LEFT JOIN employees e ON t.assigned_to = e.id 
        ORDER BY t.created_at DESC
    ''').fetchall()
    conn.close()
    
    return render_template('tasks.html', tasks=tasks)

@app.route('/add_task', methods=['GET', 'POST'])
def add_task():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        conn = get_db_connection()
        conn.execute('''
            INSERT INTO tasks (title, description, status, priority, assigned_to, due_date)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            request.form['title'],
            request.form['description'],
            request.form['status'],
            request.form['priority'],
            int(request.form['assigned_to']) if request.form['assigned_to'] else None,
            request.form['due_date'] if request.form['due_date'] else None
        ))
        conn.commit()
        conn.close()
        
        flash('Tâche ajoutée avec succès')
        return redirect(url_for('tasks'))
    
    # Récupérer la liste des employés
    conn = get_db_connection()
    employees = conn.execute('SELECT * FROM employees ORDER BY first_name').fetchall()
    conn.close()
    
    return render_template('add_task.html', employees=employees)

def open_browser():
    """Ouvrir le navigateur après un délai"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:5000')
        print("✓ Navigateur ouvert")
    except:
        print("⚠ Ouvrez manuellement: http://localhost:5000")

def main():
    """Fonction principale"""
    print("=" * 50)
    print("🏢 GESTION DES POINTAGES")
    print("=" * 50)
    print()
    
    # Initialiser la base de données
    print("🗄️ Initialisation de la base de données...")
    init_db()
    
    print("🚀 Démarrage du serveur...")
    print("📍 URL: http://localhost:5000")
    print("👤 Connexion: admin / admin")
    print("🔄 Le navigateur va s'ouvrir...")
    print("-" * 50)
    
    # Ouvrir le navigateur
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    # Démarrer l'application
    app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)

if __name__ == '__main__':
    main()
