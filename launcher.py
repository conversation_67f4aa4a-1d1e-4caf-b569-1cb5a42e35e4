"""
Lanceur principal pour le Système de Gestion des Pointages
Support multi-utilisateurs avec interface de configuration
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import webbrowser
import time
import subprocess
from pathlib import Path

# Ajouter le répertoire du projet au path
if getattr(sys, 'frozen', False):
    # Mode exécutable
    application_path = os.path.dirname(sys.executable)
else:
    # Mode développement
    application_path = os.path.dirname(os.path.abspath(__file__))

sys.path.insert(0, application_path)

try:
    from config_network import network_config
except ImportError:
    print("Erreur: Impossible d'importer la configuration réseau")
    sys.exit(1)

class GestionPointagesLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Gestion des Pointages - Lanceur")
        self.root.geometry("600x500")
        self.root.resizable(<PERSON>als<PERSON>, False)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Variables
        self.server_process = None
        self.is_running = False
        
        # Interface
        self.create_interface()
        
        # Charger la configuration
        self.load_current_config()
    
    def center_window(self):
        """Centrer la fenêtre sur l'écran"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (500 // 2)
        self.root.geometry(f"600x500+{x}+{y}")
    
    def create_interface(self):
        """Créer l'interface utilisateur"""
        # Titre
        title_frame = ttk.Frame(self.root)
        title_frame.pack(pady=20)
        
        title_label = ttk.Label(
            title_frame, 
            text="Système de Gestion des Pointages",
            font=("Arial", 16, "bold")
        )
        title_label.pack()
        
        subtitle_label = ttk.Label(
            title_frame,
            text="Configuration Multi-Utilisateurs",
            font=("Arial", 10)
        )
        subtitle_label.pack()
        
        # Configuration
        config_frame = ttk.LabelFrame(self.root, text="Configuration", padding=10)
        config_frame.pack(fill="x", padx=20, pady=10)
        
        # Mode de fonctionnement
        mode_frame = ttk.Frame(config_frame)
        mode_frame.pack(fill="x", pady=5)
        
        ttk.Label(mode_frame, text="Mode de fonctionnement:").pack(anchor="w")
        
        self.mode_var = tk.StringVar(value="client")
        
        ttk.Radiobutton(
            mode_frame, 
            text="Serveur (Ordinateur principal)", 
            variable=self.mode_var, 
            value="server",
            command=self.on_mode_change
        ).pack(anchor="w", padx=20)
        
        ttk.Radiobutton(
            mode_frame, 
            text="Client (Ordinateur secondaire)", 
            variable=self.mode_var, 
            value="client",
            command=self.on_mode_change
        ).pack(anchor="w", padx=20)
        
        # Configuration serveur
        self.server_frame = ttk.LabelFrame(config_frame, text="Configuration Serveur", padding=5)
        self.server_frame.pack(fill="x", pady=5)
        
        ttk.Label(self.server_frame, text="Port:").grid(row=0, column=0, sticky="w", padx=5)
        self.port_var = tk.StringVar(value="5001")
        ttk.Entry(self.server_frame, textvariable=self.port_var, width=10).grid(row=0, column=1, padx=5)
        
        # Configuration client
        self.client_frame = ttk.LabelFrame(config_frame, text="Configuration Client", padding=5)
        self.client_frame.pack(fill="x", pady=5)
        
        ttk.Label(self.client_frame, text="Adresse IP du serveur:").grid(row=0, column=0, sticky="w", padx=5)
        self.server_ip_var = tk.StringVar(value="*************")
        ttk.Entry(self.client_frame, textvariable=self.server_ip_var, width=15).grid(row=0, column=1, padx=5)
        
        ttk.Button(
            self.client_frame, 
            text="Détecter automatiquement", 
            command=self.auto_detect_server
        ).grid(row=0, column=2, padx=5)
        
        ttk.Button(
            self.client_frame, 
            text="Tester la connexion", 
            command=self.test_connection
        ).grid(row=1, column=1, pady=5)
        
        # Informations système
        info_frame = ttk.LabelFrame(self.root, text="Informations Système", padding=10)
        info_frame.pack(fill="x", padx=20, pady=10)
        
        self.info_text = tk.Text(info_frame, height=6, width=70)
        self.info_text.pack()
        
        # Boutons d'action
        action_frame = ttk.Frame(self.root)
        action_frame.pack(pady=20)
        
        self.start_button = ttk.Button(
            action_frame, 
            text="Démarrer l'Application", 
            command=self.start_application,
            style="Accent.TButton"
        )
        self.start_button.pack(side="left", padx=10)
        
        self.stop_button = ttk.Button(
            action_frame, 
            text="Arrêter l'Application", 
            command=self.stop_application,
            state="disabled"
        )
        self.stop_button.pack(side="left", padx=10)
        
        ttk.Button(
            action_frame, 
            text="Ouvrir dans le navigateur", 
            command=self.open_browser
        ).pack(side="left", padx=10)
        
        # Statut
        self.status_var = tk.StringVar(value="Prêt")
        status_label = ttk.Label(self.root, textvariable=self.status_var)
        status_label.pack(pady=10)
        
        # Mise à jour initiale de l'interface
        self.on_mode_change()
        self.update_info()
    
    def on_mode_change(self):
        """Gérer le changement de mode"""
        is_server = self.mode_var.get() == "server"
        
        # Afficher/masquer les frames appropriés
        if is_server:
            self.server_frame.pack(fill="x", pady=5)
            self.client_frame.pack_forget()
        else:
            self.server_frame.pack_forget()
            self.client_frame.pack(fill="x", pady=5)
    
    def load_current_config(self):
        """Charger la configuration actuelle"""
        if network_config.is_server_mode():
            self.mode_var.set("server")
        else:
            self.mode_var.set("client")
        
        self.port_var.set(str(network_config.config.get("server_port", 5001)))
        self.server_ip_var.set(network_config.config.get("client_server_ip", "*************"))
        
        self.on_mode_change()
    
    def save_config(self):
        """Sauvegarder la configuration"""
        is_server = self.mode_var.get() == "server"
        
        network_config.set_server_mode(is_server)
        network_config.config["server_port"] = int(self.port_var.get())
        network_config.config["client_server_ip"] = self.server_ip_var.get()
        
        network_config.save_config()
    
    def auto_detect_server(self):
        """Détecter automatiquement le serveur"""
        self.status_var.set("Détection du serveur en cours...")
        self.root.update()
        
        def detect():
            server_ip = network_config.detect_server()
            if server_ip:
                self.server_ip_var.set(server_ip)
                self.status_var.set(f"Serveur détecté: {server_ip}")
                messagebox.showinfo("Succès", f"Serveur trouvé à l'adresse: {server_ip}")
            else:
                self.status_var.set("Aucun serveur détecté")
                messagebox.showwarning("Échec", "Aucun serveur trouvé sur le réseau local")
        
        threading.Thread(target=detect, daemon=True).start()
    
    def test_connection(self):
        """Tester la connexion au serveur"""
        server_ip = self.server_ip_var.get()
        
        if network_config.test_server_connection(server_ip):
            messagebox.showinfo("Succès", f"Connexion réussie au serveur {server_ip}")
            self.status_var.set(f"Connexion OK: {server_ip}")
        else:
            messagebox.showerror("Échec", f"Impossible de se connecter au serveur {server_ip}")
            self.status_var.set(f"Connexion échouée: {server_ip}")
    
    def start_application(self):
        """Démarrer l'application"""
        try:
            # Sauvegarder la configuration
            self.save_config()
            
            # Démarrer l'application en arrière-plan
            def run_app():
                try:
                    # Importer et démarrer l'application Flask
                    from app import create_app
                    app = create_app()
                    
                    config = network_config.get_server_config()
                    app.run(
                        host=config["host"],
                        port=config["port"],
                        debug=config["debug"],
                        threaded=config["threaded"],
                        use_reloader=False
                    )
                except Exception as e:
                    print(f"Erreur lors du démarrage de l'application: {e}")
            
            # Démarrer dans un thread séparé
            self.server_thread = threading.Thread(target=run_app, daemon=True)
            self.server_thread.start()
            
            # Attendre un peu pour que le serveur démarre
            time.sleep(2)
            
            self.is_running = True
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
            self.status_var.set("Application démarrée")
            
            # Ouvrir automatiquement le navigateur
            self.open_browser()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible de démarrer l'application:\n{str(e)}")
    
    def stop_application(self):
        """Arrêter l'application"""
        self.is_running = False
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.status_var.set("Application arrêtée")
        
        messagebox.showinfo("Information", "L'application a été arrêtée")
    
    def open_browser(self):
        """Ouvrir l'application dans le navigateur"""
        if self.mode_var.get() == "server":
            url = f"http://localhost:{self.port_var.get()}"
        else:
            url = f"http://{self.server_ip_var.get()}:{network_config.config.get('server_port', 5001)}"
        
        webbrowser.open(url)
    
    def update_info(self):
        """Mettre à jour les informations système"""
        info = []
        info.append(f"Adresse IP locale: {network_config.get_local_ip()}")
        info.append(f"Mode actuel: {'Serveur' if network_config.is_server_mode() else 'Client'}")
        info.append(f"Port configuré: {network_config.config.get('server_port', 5001)}")
        info.append(f"Base de données: {network_config.get_database_config()}")
        info.append(f"Répertoire de travail: {os.getcwd()}")
        
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, "\n".join(info))
    
    def run(self):
        """Lancer l'interface"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
    
    def on_closing(self):
        """Gérer la fermeture de l'application"""
        if self.is_running:
            if messagebox.askokcancel("Quitter", "L'application est en cours d'exécution. Voulez-vous vraiment quitter?"):
                self.stop_application()
                self.root.destroy()
        else:
            self.root.destroy()

if __name__ == "__main__":
    launcher = GestionPointagesLauncher()
    launcher.run()
