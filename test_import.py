#!/usr/bin/env python3
"""
Test script to check imports
"""
import sys
import traceback

print("🔍 Testing imports...")
print(f"Python version: {sys.version}")
print("-" * 50)

try:
    print("1. Testing Flask...")
    import flask
    print(f"   ✅ Flask {flask.__version__} imported successfully")
except Exception as e:
    print(f"   ❌ Flask import failed: {e}")
    traceback.print_exc()

try:
    print("2. Testing config...")
    import config
    print("   ✅ Config imported successfully")
except Exception as e:
    print(f"   ❌ Config import failed: {e}")
    traceback.print_exc()

try:
    print("3. Testing extensions...")
    import extensions
    print("   ✅ Extensions imported successfully")
except Exception as e:
    print(f"   ❌ Extensions import failed: {e}")
    traceback.print_exc()

try:
    print("4. Testing models...")
    import models
    print("   ✅ Models imported successfully")
except Exception as e:
    print(f"   ❌ Models import failed: {e}")
    traceback.print_exc()

try:
    print("5. Testing routes...")
    import routes
    print("   ✅ Routes imported successfully")
except Exception as e:
    print(f"   ❌ Routes import failed: {e}")
    traceback.print_exc()

try:
    print("6. Testing app...")
    import app
    print("   ✅ App imported successfully")
except Exception as e:
    print(f"   ❌ App import failed: {e}")
    traceback.print_exc()

print("-" * 50)
print("🏁 Import test completed")
