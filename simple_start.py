"""
تشغيل البرنامج بطريقة بسيطة
"""

import os
import sys
import time
import threading
import webbrowser

def setup_dirs():
    """إنشاء المجلدات المطلوبة"""
    dirs = ['instance', 'logs', 'backups', 'uploads']
    for d in dirs:
        os.makedirs(d, exist_ok=True)

def fix_db():
    """إصلاح قاعدة البيانات"""
    if not os.path.exists('instance/app.db'):
        print("انشاء قاعدة البيانات...")
        try:
            import subprocess
            result = subprocess.run([sys.executable, 'fix_database_final.py'])
            return result.returncode == 0
        except:
            return False
    return True

def open_web():
    """فتح المتصفح"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5001')
    except:
        pass

def run_app():
    """تشغيل التطبيق"""
    try:
        from app import create_app
        app = create_app()
        app.config['DEBUG'] = False
        
        # فتح المتصفح
        t = threading.Thread(target=open_web, daemon=True)
        t.start()
        
        print("الخادم يعمل على: http://localhost:5001")
        print("المستخدم: admin")
        print("كلمة المرور: admin")
        print("للايقاف: Ctrl+C")
        
        app.run(host='127.0.0.1', port=5001, debug=False, use_reloader=False)
        
    except Exception as e:
        print(f"خطأ: {e}")
        input("اضغط Enter...")

def main():
    print("نظام ادارة الحضور والانصراف")
    print("=" * 40)
    
    setup_dirs()
    
    if not fix_db():
        print("خطأ في قاعدة البيانات")
        input("اضغط Enter...")
        return
    
    run_app()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\nتم الايقاف")
    except Exception as e:
        print(f"خطأ: {e}")
        input("اضغط Enter...")
