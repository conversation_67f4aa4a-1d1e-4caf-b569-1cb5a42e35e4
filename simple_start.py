#!/usr/bin/env python3
"""
Script simple pour démarrer le serveur
"""

import sys
import os

# Ajouter le répertoire actuel au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🚀 Démarrage simple du serveur...")
print("=" * 40)

try:
    # Import de l'application
    print("📦 Import de l'application...")
    from app import create_app
    
    # Création de l'application
    print("🏗️  Création de l'application...")
    app = create_app()
    
    # Informations
    print("✅ Application créée avec succès!")
    print()
    print("🌐 URL: http://127.0.0.1:5001")
    print("👤 Utilisateur: admin")
    print("🔑 Mot de passe: admin")
    print()
    print("💡 Appuyez sur Ctrl+C pour arrêter")
    print("=" * 40)
    
    # Démarrage du serveur
    app.run(
        host='127.0.0.1',
        port=5001,
        debug=True
    )
    
except ImportError as e:
    print(f"❌ Erreur d'import: {e}")
    print("💡 Vérifiez que tous les modules sont installés")
    print("💡 Essayez: pip install flask flask-sqlalchemy flask-login flask-wtf")
    
except Exception as e:
    print(f"❌ Erreur: {e}")
    import traceback
    traceback.print_exc()
    
finally:
    print("\n🛑 Serveur arrêté")
    input("Appuyez sur Entrée pour fermer...")
