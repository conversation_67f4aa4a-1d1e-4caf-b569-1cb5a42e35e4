{% extends "base.html" %}

{% block title %}Modifier un utilisateur - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3"><i class="fas fa-user-edit me-2"></i>Modifier un utilisateur</h1>
        <p class="text-muted">Modifiez les informations et les permissions de l'utilisateur.</p>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-edit me-2"></i>Formulaire de modification d'utilisateur</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('user.edit_user', id=user.id) }}">
                    {{ form.hidden_tag() }}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="username" class="form-label">{{ form.username.label }}</label>
                            {{ form.username(class="form-control", placeholder="Entrez le nom d'utilisateur") }}
                            {% for error in form.username.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">{{ form.email.label }}</label>
                            {{ form.email(class="form-control", placeholder="Entrez l'email") }}
                            {% for error in form.email.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <h5 class="mt-4 mb-3">Permissions</h5>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-check mb-2">
                                {{ form.is_admin(class="form-check-input") }}
                                <label class="form-check-label" for="is_admin">{{ form.is_admin.label }}</label>
                            </div>
                            <div class="form-check mb-2">
                                {{ form.can_manage_users(class="form-check-input") }}
                                <label class="form-check-label" for="can_manage_users">{{ form.can_manage_users.label }}</label>
                            </div>
                            <div class="form-check mb-2">
                                {{ form.can_manage_employees(class="form-check-input") }}
                                <label class="form-check-label" for="can_manage_employees">{{ form.can_manage_employees.label }}</label>
                            </div>
                            <div class="form-check mb-2">
                                {{ form.can_manage_tasks(class="form-check-input") }}
                                <label class="form-check-label" for="can_manage_tasks">{{ form.can_manage_tasks.label }}</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-2">
                                {{ form.can_manage_finances(class="form-check-input") }}
                                <label class="form-check-label" for="can_manage_finances">{{ form.can_manage_finances.label }}</label>
                            </div>
                            <div class="form-check mb-2">
                                {{ form.can_access_registration_files(class="form-check-input") }}
                                <label class="form-check-label" for="can_access_registration_files">{{ form.can_access_registration_files.label }}</label>
                            </div>
                            <div class="form-check mb-2">
                                {{ form.can_access_technical_files(class="form-check-input") }}
                                <label class="form-check-label" for="can_access_technical_files">{{ form.can_access_technical_files.label }}</label>
                            </div>
                            <div class="form-check mb-2">
                                {{ form.can_access_reimbursement_files(class="form-check-input") }}
                                <label class="form-check-label" for="can_access_reimbursement_files">{{ form.can_access_reimbursement_files.label }}</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-2">
                                {{ form.can_access_organization_files(class="form-check-input") }}
                                <label class="form-check-label" for="can_access_organization_files">{{ form.can_access_organization_files.label }}</label>
                            </div>
                            <div class="form-check mb-2">
                                {{ form.can_access_trainer_files(class="form-check-input") }}
                                <label class="form-check-label" for="can_access_trainer_files">{{ form.can_access_trainer_files.label }}</label>
                            </div>
                            <div class="form-check mb-2">
                                {{ form.can_access_trainer_schedule(class="form-check-input") }}
                                <label class="form-check-label" for="can_access_trainer_schedule">{{ form.can_access_trainer_schedule.label }}</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{{ url_for('user.users') }}" class="btn btn-secondary me-md-2">Annuler</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
