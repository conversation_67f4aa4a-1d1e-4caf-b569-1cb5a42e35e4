#!/usr/bin/env python3
"""
Script pour vérifier et corriger les statuts des tâches
"""
import sqlite3
import os

def check_and_fix_task_status():
    """Vérifier et corriger les statuts des tâches"""
    
    # Chemin vers la base de données
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'app.db')
    
    if not os.path.exists(db_path):
        print("❌ Base de données non trouvée!")
        return
    
    # Connexion à la base de données
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Vérifier les statuts existants
        cursor.execute('SELECT DISTINCT status FROM simple_tasks')
        statuses = cursor.fetchall()
        
        print("🔍 Statuts actuels dans la base de données:")
        for status in statuses:
            cursor.execute('SELECT COUNT(*) FROM simple_tasks WHERE status = ?', (status[0],))
            count = cursor.fetchone()[0]
            print(f"   - {status[0]}: {count} tâche(s)")
        
        # Corriger les statuts incorrects
        corrections = {
            'in_progress': 'en_cours',
            'completed': 'termine',
            'cancelled': 'annule',
            'new': 'nouveau'
        }
        
        print("\n🔧 Correction des statuts...")
        for old_status, new_status in corrections.items():
            cursor.execute('UPDATE simple_tasks SET status = ? WHERE status = ?', (new_status, old_status))
            if cursor.rowcount > 0:
                print(f"   ✅ {cursor.rowcount} tâche(s) corrigée(s): {old_status} → {new_status}")
        
        # Valider les changements
        conn.commit()
        
        # Vérifier les statuts après correction
        print("\n📊 Statuts après correction:")
        cursor.execute('SELECT DISTINCT status FROM simple_tasks')
        statuses = cursor.fetchall()
        
        for status in statuses:
            cursor.execute('SELECT COUNT(*) FROM simple_tasks WHERE status = ?', (status[0],))
            count = cursor.fetchone()[0]
            print(f"   - {status[0]}: {count} tâche(s)")
        
        print("\n✅ Correction terminée!")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Erreur: {e}")
        raise
    
    finally:
        conn.close()

if __name__ == '__main__':
    print("🔧 Vérification et Correction des Statuts des Tâches")
    print("=" * 60)
    
    check_and_fix_task_status()
    
    print("=" * 60)
    print("🚀 Vous pouvez maintenant actualiser la page des tâches")
    
    input("Appuyez sur Entrée pour continuer...")
