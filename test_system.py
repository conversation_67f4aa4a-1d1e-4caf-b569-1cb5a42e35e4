#!/usr/bin/env python3
"""
Script de test pour vérifier le système
"""

import os
import sys
import sqlite3
from werkzeug.security import generate_password_hash, check_password_hash

def test_database():
    """Test de la base de données"""
    print("🔍 Test de la base de données...")
    
    db_path = os.path.join('instance', 'app.db')
    
    if not os.path.exists(db_path):
        print("❌ Base de données non trouvée")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test des tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✅ Tables trouvées: {[table[0] for table in tables]}")
        
        # Test des utilisateurs
        cursor.execute("SELECT id, username, email FROM users")
        users = cursor.fetchall()
        print(f"✅ Utilisateurs: {len(users)} trouvés")
        
        # Test des tâches
        cursor.execute("SELECT COUNT(*) FROM simple_tasks")
        task_count = cursor.fetchone()[0]
        print(f"✅ Tâches: {task_count} trouvées")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur base de données: {e}")
        return False

def fix_admin_password():
    """Réparer le mot de passe admin"""
    print("🔧 Réparation du mot de passe admin...")
    
    db_path = os.path.join('instance', 'app.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Réinitialiser le mot de passe admin
        password_hash = generate_password_hash('admin')
        cursor.execute("UPDATE users SET password_hash = ? WHERE username = 'admin'", (password_hash,))
        conn.commit()
        
        # Vérifier
        cursor.execute("SELECT password_hash FROM users WHERE username = 'admin'")
        stored_hash = cursor.fetchone()[0]
        
        if check_password_hash(stored_hash, 'admin'):
            print("✅ Mot de passe admin réparé: admin/admin")
            conn.close()
            return True
        else:
            print("❌ Échec de la réparation")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_imports():
    """Test des imports"""
    print("🔍 Test des imports...")
    
    try:
        from app import create_app
        print("✅ App importée")
        
        from models import User, SimpleTask, Employee
        print("✅ Modèles importés")
        
        from forms import LoginForm, SimpleTaskForm
        print("✅ Formulaires importés")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'import: {e}")
        return False

def create_test_data():
    """Créer des données de test"""
    print("🔧 Création de données de test...")
    
    try:
        from app import create_app
        from models import User, SimpleTask, Employee
        from extensions import db
        
        app = create_app()
        
        with app.app_context():
            # Créer un employé de test
            employee = Employee.query.first()
            if not employee:
                employee = Employee(
                    first_name="Test",
                    last_name="Employee",
                    position="Testeur",
                    cin="TEST123",
                    daily_rate=100.0
                )
                db.session.add(employee)
                db.session.commit()
                print("✅ Employé de test créé")
            
            # Créer une tâche de test
            task_count = SimpleTask.query.count()
            if task_count == 0:
                task = SimpleTask(
                    title="Tâche de test",
                    description="Ceci est une tâche de test pour vérifier le système",
                    status="new",
                    priority="normal"
                )
                db.session.add(task)
                db.session.commit()
                print("✅ Tâche de test créée")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur création données: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Test du système Gestion des Pointages")
    print("=" * 50)
    
    # Tests
    tests = [
        ("Base de données", test_database),
        ("Imports", test_imports),
        ("Mot de passe admin", fix_admin_password),
        ("Données de test", create_test_data)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        result = test_func()
        results.append((test_name, result))
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS:")
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"  {test_name}: {status}")
    
    # Instructions
    print("\n💡 INSTRUCTIONS:")
    print("1. Utilisateur: admin")
    print("2. Mot de passe: admin")
    print("3. URL: http://127.0.0.1:5001")
    print("4. Pour démarrer: python run_app.py")
    
    if all(result for _, result in results):
        print("\n🎉 Tous les tests sont réussis!")
    else:
        print("\n⚠️  Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
