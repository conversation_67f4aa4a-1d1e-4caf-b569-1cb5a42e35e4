<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Réinitialiser le mot de passe - Gestion des Pointages</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="login-container">
                    <!-- Logo -->
                    <div class="login-logo">
                        <i class="fas fa-clipboard-check fa-3x text-primary mb-3"></i>
                        <h4 class="text-center mb-4">Gestion des Pointages</h4>
                    </div>

                    <!-- Formulaire de réinitialisation -->
                    <div class="card login-card">
                        <div class="card-header text-center bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-lock-open me-2"></i>
                                Nouveau mot de passe
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Messages flash -->
                            {% with messages = get_flashed_messages(with_categories=true) %}
                                {% if messages %}
                                    {% for category, message in messages %}
                                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                            {{ message }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            {% endwith %}

                            <p class="text-muted text-center mb-4">
                                <i class="fas fa-info-circle me-1"></i>
                                Choisissez un nouveau mot de passe sécurisé
                            </p>

                            <form method="POST">
                                {{ form.hidden_tag() }}
                                
                                <div class="mb-3">
                                    {{ form.password.label(class="form-label") }}
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        {{ form.password(class="form-control", placeholder="Minimum 6 caractères") }}
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                            <i class="fas fa-eye" id="password-eye"></i>
                                        </button>
                                    </div>
                                    {% if form.password.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.password.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.password2.label(class="form-label") }}
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        {{ form.password2(class="form-control", placeholder="Confirmez le mot de passe") }}
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password2')">
                                            <i class="fas fa-eye" id="password2-eye"></i>
                                        </button>
                                    </div>
                                    {% if form.password2.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.password2.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Indicateur de force du mot de passe -->
                                <div class="mb-3">
                                    <small class="text-muted">Force du mot de passe:</small>
                                    <div class="progress" style="height: 5px;">
                                        <div class="progress-bar" id="password-strength" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small id="password-strength-text" class="text-muted"></small>
                                </div>

                                <div class="d-grid mb-3">
                                    {{ form.submit(class="btn btn-success") }}
                                </div>
                            </form>

                            <div class="text-center">
                                <a href="{{ url_for('auth.login') }}" class="text-decoration-none">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Retour à la connexion
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Conseils de sécurité -->
                    <div class="card mt-3">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-shield-alt text-success me-2"></i>
                                Conseils pour un mot de passe sécurisé
                            </h6>
                            <ul class="list-unstyled mb-0">
                                <li><small><i class="fas fa-check text-success me-1"></i> Au moins 8 caractères</small></li>
                                <li><small><i class="fas fa-check text-success me-1"></i> Mélange de lettres et chiffres</small></li>
                                <li><small><i class="fas fa-check text-success me-1"></i> Caractères spéciaux (!@#$%)</small></li>
                                <li><small><i class="fas fa-check text-success me-1"></i> Évitez les mots courants</small></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Fonction pour afficher/masquer le mot de passe
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const eye = document.getElementById(fieldId + '-eye');
            
            if (field.type === 'password') {
                field.type = 'text';
                eye.classList.remove('fa-eye');
                eye.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                eye.classList.remove('fa-eye-slash');
                eye.classList.add('fa-eye');
            }
        }

        // Vérification de la force du mot de passe
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('password-strength');
            const strengthText = document.getElementById('password-strength-text');
            
            let strength = 0;
            let text = '';
            let color = '';
            
            if (password.length >= 6) strength += 20;
            if (password.length >= 8) strength += 20;
            if (/[a-z]/.test(password)) strength += 20;
            if (/[A-Z]/.test(password)) strength += 20;
            if (/[0-9]/.test(password)) strength += 10;
            if (/[^A-Za-z0-9]/.test(password)) strength += 10;
            
            if (strength < 30) {
                text = 'Faible';
                color = 'bg-danger';
            } else if (strength < 60) {
                text = 'Moyen';
                color = 'bg-warning';
            } else if (strength < 90) {
                text = 'Bon';
                color = 'bg-info';
            } else {
                text = 'Excellent';
                color = 'bg-success';
            }
            
            strengthBar.style.width = strength + '%';
            strengthBar.className = 'progress-bar ' + color;
            strengthText.textContent = text;
        });

        // Auto-hide des alertes après 5 secondes
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    if (alert.parentNode) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                }, 5000);
            });
        });
    </script>
</body>
</html>
