#!/usr/bin/env python3
"""
Script de test pour le système de gestion des tâches simplifié
"""

import os
import sys
from datetime import datetime, date

def test_imports():
    """Test des imports"""
    try:
        print("🔍 Test des imports...")
        
        # Test import de l'app
        from app import create_app
        print("✅ App importée avec succès")
        
        # Test import des modèles
        from models import SimpleTask
        print("✅ SimpleTask importé avec succès")
        
        # Test import des formulaires
        from forms import SimpleTaskForm
        print("✅ SimpleTaskForm importé avec succès")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'import: {e}")
        return False

def test_database():
    """Test de la base de données"""
    try:
        print("\n🔍 Test de la base de données...")
        
        from app import create_app
        from models import SimpleTask
        from extensions import db
        
        app = create_app()
        
        with app.app_context():
            # Test de connexion à la base de données
            tasks = SimpleTask.query.all()
            print(f"✅ Connexion à la base de données réussie")
            print(f"✅ {len(tasks)} tâches trouvées dans la base")
            
            # Afficher quelques tâches
            if tasks:
                print("\n📋 Tâches existantes:")
                for task in tasks[:3]:  # Afficher les 3 premières
                    print(f"  - #{task.id}: {task.title} (Statut: {task.status_display})")
            
            return True
            
    except Exception as e:
        print(f"❌ Erreur de base de données: {e}")
        return False

def test_forms():
    """Test des formulaires"""
    try:
        print("\n🔍 Test des formulaires...")

        from app import create_app
        from forms import SimpleTaskForm

        app = create_app()

        with app.app_context():
            # Créer un formulaire vide
            form = SimpleTaskForm()
            print("✅ Formulaire créé avec succès")
        
        # Vérifier les champs
        expected_fields = ['title', 'description', 'status', 'priority', 'due_date', 'category', 'tags', 'attachment']
        for field_name in expected_fields:
            if hasattr(form, field_name):
                print(f"✅ Champ '{field_name}' présent")
            else:
                print(f"❌ Champ '{field_name}' manquant")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur de formulaire: {e}")
        return False

def test_routes():
    """Test des routes"""
    try:
        print("\n🔍 Test des routes...")
        
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # Test de la route principale des tâches (sans authentification)
            response = client.get('/tasks')
            print(f"✅ Route /tasks accessible (Status: {response.status_code})")
            
            # Test de la route d'ajout de tâche
            response = client.get('/task/add')
            print(f"✅ Route /task/add accessible (Status: {response.status_code})")
            
            return True
            
    except Exception as e:
        print(f"❌ Erreur de routes: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 Test du système de gestion des tâches simplifié")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Base de données", test_database),
        ("Formulaires", test_forms),
        ("Routes", test_routes)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📝 Test: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name}: RÉUSSI")
        else:
            print(f"❌ {test_name}: ÉCHEC")
    
    print("\n" + "=" * 60)
    print(f"📊 Résultats: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests sont passés! Le système est prêt.")
        print("\nVous pouvez maintenant démarrer l'application avec:")
        print("python app.py")
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
        sys.exit(1)

if __name__ == "__main__":
    main()
