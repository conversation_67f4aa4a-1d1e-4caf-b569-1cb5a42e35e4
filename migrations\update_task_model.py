"""
Script de migration pour mettre à jour le modèle de tâches
"""
import sqlite3
import os
import sys

# Ajouter le répertoire parent au chemin de recherche des modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from config import Config
except ImportError:
    # Si le module config n'est pas trouvé, définir un chemin par défaut
    class Config:
        UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'static/uploads')

def run_migration():
    """Exécute la migration pour mettre à jour le modèle de tâches"""
    # Obtenir le chemin de la base de données
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'instance', 'app.db')

    print(f"Connexion à la base de données: {db_path}")

    # Vérifier si le fichier existe
    if not os.path.exists(db_path):
        print(f"Erreur: Le fichier de base de données {db_path} n'existe pas.")
        return False

    try:
        # Connexion à la base de données
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Liste des colonnes à ajouter
        columns_to_add = [
            ("priority", "INTEGER DEFAULT 0"),
            ("status", "VARCHAR(20) DEFAULT 'pending'"),
            ("due_date", "DATE"),
            ("category", "VARCHAR(50)"),
            ("tags", "TEXT"),
            ("color", "VARCHAR(20) DEFAULT '#3498db'")
        ]

        # Vérifier et ajouter chaque colonne
        for column_name, column_type in columns_to_add:
            # Vérifier si la colonne existe déjà
            cursor.execute(f"PRAGMA table_info(tasks)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            if column_name in column_names:
                print(f"La colonne {column_name} existe déjà dans la table tasks.")
            else:
                # Ajouter la colonne
                cursor.execute(f"ALTER TABLE tasks ADD COLUMN {column_name} {column_type}")
                print(f"La colonne {column_name} a été ajoutée à la table tasks.")

        # Mettre à jour la table employee_tasks
        employee_task_columns = [
            ("start_date", "DATE"),
            ("end_date", "DATE"),
            ("progress", "INTEGER DEFAULT 0"),
            ("priority", "INTEGER DEFAULT 0"),
            ("status", "VARCHAR(20) DEFAULT 'pending'")
        ]

        for column_name, column_type in employee_task_columns:
            # Vérifier si la colonne existe déjà
            cursor.execute(f"PRAGMA table_info(employee_tasks)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            if column_name in column_names:
                print(f"La colonne {column_name} existe déjà dans la table employee_tasks.")
            else:
                # Ajouter la colonne
                cursor.execute(f"ALTER TABLE employee_tasks ADD COLUMN {column_name} {column_type}")
                print(f"La colonne {column_name} a été ajoutée à la table employee_tasks.")

        # Valider les changements
        conn.commit()
        print("Migration réussie: Les modèles de tâches ont été mis à jour.")

        # Fermer la connexion
        conn.close()
        return True

    except sqlite3.Error as e:
        print(f"Erreur SQLite: {e}")
        return False
    except Exception as e:
        print(f"Erreur: {e}")
        return False

if __name__ == "__main__":
    run_migration()
