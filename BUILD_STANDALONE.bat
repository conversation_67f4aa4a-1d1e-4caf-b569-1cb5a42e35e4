@echo off
echo ========================================
echo   Construction Application Autonome
echo ========================================
echo.

echo Installation de PyInstaller...
pip install pyinstaller==5.13.2

echo.
echo Nettoyage des repertoires precedents...
if exist "dist" rmdir /s /q dist
if exist "build" rmdir /s /q build
if exist "__pycache__" rmdir /s /q __pycache__

echo.
echo Construction de l'executable autonome...
pyinstaller --onefile --console --name=GestionPointages ^
  --add-data="templates;templates" ^
  --add-data="static;static" ^
  --add-data="instance;instance" ^
  --hidden-import=flask ^
  --hidden-import=flask_sqlalchemy ^
  --hidden-import=flask_login ^
  --hidden-import=flask_wtf ^
  --hidden-import=flask_babel ^
  --hidden-import=wtforms ^
  --hidden-import=sqlalchemy ^
  --hidden-import=babel ^
  --hidden-import=jinja2 ^
  --hidden-import=werkzeug ^
  --hidden-import=markupsafe ^
  --hidden-import=itsdangerous ^
  --hidden-import=click ^
  --hidden-import=blinker ^
  --hidden-import=alembic ^
  --hidden-import=email_validator ^
  --hidden-import=sqlite3 ^
  --hidden-import=models ^
  --hidden-import=forms ^
  --hidden-import=routes ^
  --hidden-import=routes_admin ^
  standalone_app.py

echo.
echo Construction terminee!
echo.
echo Fichier genere:
echo - dist\GestionPointages.exe
echo.
echo Pour tester:
echo 1. Allez dans le dossier dist
echo 2. Executez GestionPointages.exe
echo 3. Le navigateur s'ouvrira automatiquement
echo.

pause
