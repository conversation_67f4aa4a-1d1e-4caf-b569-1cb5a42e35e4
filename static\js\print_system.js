/**
 * Système de Gestion des Pointages - Système d'Impression Professionnel
 * Gestion des impressions de haute qualité avec design moderne
 */

class ProfessionalPrintSystem {
    constructor() {
        this.companyInfo = null;
        this.templates = {};
        this.loadCompanyInfo();
        this.initializeTemplates();
    }

    /**
     * Charger les informations de l'entreprise
     */
    async loadCompanyInfo() {
        try {
            const response = await fetch('/api/company-info');
            const data = await response.json();
            this.companyInfo = data.company;
        } catch (error) {
            console.error('Erreur lors du chargement des informations de l\'entreprise:', error);
            // Utiliser des informations par défaut
            this.companyInfo = {
                name: 'GESTION DES POINTAGES',
                address: 'Adresse de l\'entreprise',
                phone: '+212 XXX XXX XXX',
                email: '<EMAIL>',
                website: 'www.entreprise.ma',
                logo_path: null,
                footer_text: 'Document généré automatiquement par le système de gestion'
            };
        }
    }

    /**
     * Initialiser les templates professionnels
     */
    initializeTemplates() {
        this.templates = {
            task: this.getTaskTemplate(),
            employee: this.getEmployeeTemplate(),
            attendance: this.getAttendanceTemplate(),
            financial: this.getFinancialTemplate()
        };
    }

    /**
     * Générer l'en-tête avec logo de l'entreprise
     */
    generateHeader() {
        const logoSection = this.companyInfo.logo_path ? 
            `<img src="${this.companyInfo.logo_path}" alt="Logo" style="max-height: 80px; max-width: 200px;">` :
            `<div class="logo-placeholder">🏢</div>`;

        return `
            <div class="print-header">
                <div class="company-logo">
                    ${logoSection}
                </div>
                <div class="company-info">
                    <h1>${this.companyInfo.name}</h1>
                    <p>${this.companyInfo.address}</p>
                    <p>Tél: ${this.companyInfo.phone} | Email: ${this.companyInfo.email}</p>
                    <p>Site web: ${this.companyInfo.website}</p>
                </div>
            </div>
            <hr class="header-separator">
        `;
    }

    /**
     * Générer le pied de page
     */
    generateFooter() {
        const currentDate = new Date().toLocaleDateString('fr-FR');
        const currentTime = new Date().toLocaleTimeString('fr-FR');

        return `
            <hr class="footer-separator">
            <div class="print-footer">
                <div class="footer-content">
                    <p><strong>${this.companyInfo.footer_text}</strong></p>
                    <div class="footer-details">
                        <div class="footer-left">
                            <p>${this.companyInfo.name}</p>
                            <p>${this.companyInfo.address}</p>
                        </div>
                        <div class="footer-center">
                            <p>Tél: ${this.companyInfo.phone}</p>
                            <p>Email: ${this.companyInfo.email}</p>
                        </div>
                        <div class="footer-right">
                            <p>Document généré le:</p>
                            <p>${currentDate} à ${currentTime}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Générer les styles CSS pour l'impression
     */
    generatePrintStyles() {
        return `
            <style>
                @media print {
                    body { 
                        font-family: Arial, sans-serif; 
                        margin: 0; 
                        padding: 20px;
                        font-size: 12px;
                        line-height: 1.4;
                    }
                    
                    .print-header {
                        display: flex;
                        align-items: center;
                        margin-bottom: 20px;
                        padding-bottom: 15px;
                    }
                    
                    .company-logo {
                        margin-right: 20px;
                        flex-shrink: 0;
                    }
                    
                    .logo-placeholder {
                        font-size: 48px;
                        color: #666;
                        width: 80px;
                        height: 80px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border: 2px solid #ddd;
                        border-radius: 8px;
                    }
                    
                    .company-info h1 {
                        margin: 0 0 10px 0;
                        font-size: 24px;
                        color: #333;
                        font-weight: bold;
                    }
                    
                    .company-info p {
                        margin: 5px 0;
                        color: #666;
                        font-size: 11px;
                    }
                    
                    .header-separator, .footer-separator {
                        border: none;
                        border-top: 2px solid #333;
                        margin: 15px 0;
                    }
                    
                    .print-content {
                        min-height: 400px;
                        margin: 20px 0;
                    }
                    
                    .print-footer {
                        margin-top: 30px;
                        padding-top: 15px;
                        page-break-inside: avoid;
                    }
                    
                    .footer-content {
                        text-align: center;
                    }
                    
                    .footer-details {
                        display: flex;
                        justify-content: space-between;
                        margin-top: 15px;
                        font-size: 10px;
                    }
                    
                    .footer-left, .footer-center, .footer-right {
                        flex: 1;
                    }
                    
                    .footer-left {
                        text-align: left;
                    }
                    
                    .footer-center {
                        text-align: center;
                    }
                    
                    .footer-right {
                        text-align: right;
                    }
                    
                    .footer-details p {
                        margin: 2px 0;
                        color: #666;
                    }
                    
                    /* Masquer les éléments non imprimables */
                    .no-print, .btn, .navbar, .sidebar, .modal {
                        display: none !important;
                    }
                    
                    /* Styles pour les tableaux */
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 15px 0;
                    }
                    
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: left;
                    }
                    
                    th {
                        background-color: #f5f5f5;
                        font-weight: bold;
                    }
                    
                    /* Éviter les coupures de page */
                    .page-break-avoid {
                        page-break-inside: avoid;
                    }
                }
                
                @page {
                    margin: 2cm;
                    size: A4;
                }
            </style>
        `;
    }

    /**
     * Créer un document d'impression complet
     */
    createPrintDocument(title, content) {
        return `
            <!DOCTYPE html>
            <html lang="fr">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${title}</title>
                ${this.generatePrintStyles()}
            </head>
            <body>
                ${this.generateHeader()}
                <div class="print-content">
                    <h2 style="text-align: center; margin: 20px 0; color: #333;">${title}</h2>
                    ${content}
                </div>
                ${this.generateFooter()}
            </body>
            </html>
        `;
    }

    /**
     * Imprimer un document
     */
    printDocument(title, content) {
        const printWindow = window.open('', '_blank');
        const documentHTML = this.createPrintDocument(title, content);
        
        printWindow.document.write(documentHTML);
        printWindow.document.close();
        
        // Attendre que les images se chargent avant d'imprimer
        printWindow.onload = function() {
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);
        };
    }

    /**
     * Générer un PDF (nécessite une bibliothèque PDF)
     */
    async generatePDF(title, content) {
        // Cette fonction peut être étendue avec jsPDF ou une autre bibliothèque
        console.log('Génération PDF:', title);
        // Pour l'instant, utiliser l'impression
        this.printDocument(title, content);
    }

    /**
     * Créer une fiche de tâche pour impression
     */
    createTaskSheet(taskData) {
        const content = `
            <div class="task-sheet">
                <table class="task-info-table">
                    <tr>
                        <th style="width: 30%;">Titre de la tâche</th>
                        <td>${taskData.title || 'Non défini'}</td>
                    </tr>
                    <tr>
                        <th>Description</th>
                        <td style="white-space: pre-wrap;">${taskData.description || 'Aucune description'}</td>
                    </tr>
                    <tr>
                        <th>Statut</th>
                        <td>${taskData.status || 'Non défini'}</td>
                    </tr>
                    <tr>
                        <th>Priorité</th>
                        <td>${taskData.priority || 'Normale'}</td>
                    </tr>
                    <tr>
                        <th>Assigné à</th>
                        <td>${taskData.assignedTo || 'Non assigné'}</td>
                    </tr>
                    <tr>
                        <th>Date d'échéance</th>
                        <td>${taskData.dueDate || 'Non définie'}</td>
                    </tr>
                    <tr>
                        <th>Catégorie</th>
                        <td>${taskData.category || 'Non définie'}</td>
                    </tr>
                    <tr>
                        <th>Tags</th>
                        <td>${taskData.tags || 'Aucun'}</td>
                    </tr>
                </table>
                
                <div class="task-notes" style="margin-top: 30px;">
                    <h3>Notes et Commentaires:</h3>
                    <div style="border: 1px solid #ddd; padding: 15px; min-height: 100px; background-color: #f9f9f9;">
                        ${taskData.notes || 'Aucune note disponible'}
                    </div>
                </div>
            </div>
        `;
        
        return this.createPrintDocument('FICHE DE TÂCHE', content);
    }

    /**
     * Créer une fiche d'employé pour impression
     */
    createEmployeeSheet(employeeData) {
        const content = `
            <div class="employee-sheet">
                <table class="employee-info-table">
                    <tr>
                        <th style="width: 30%;">Nom complet</th>
                        <td>${employeeData.firstName} ${employeeData.lastName}</td>
                    </tr>
                    <tr>
                        <th>CIN</th>
                        <td>${employeeData.cin || 'Non renseigné'}</td>
                    </tr>
                    <tr>
                        <th>Poste</th>
                        <td>${employeeData.position || 'Non défini'}</td>
                    </tr>
                    <tr>
                        <th>Salaire</th>
                        <td>${employeeData.salary || 'Non renseigné'} MAD</td>
                    </tr>
                    <tr>
                        <th>Date d'embauche</th>
                        <td>${employeeData.hireDate || 'Non renseignée'}</td>
                    </tr>
                    <tr>
                        <th>Téléphone</th>
                        <td>${employeeData.phone || 'Non renseigné'}</td>
                    </tr>
                    <tr>
                        <th>Email</th>
                        <td>${employeeData.email || 'Non renseigné'}</td>
                    </tr>
                    <tr>
                        <th>Adresse</th>
                        <td>${employeeData.address || 'Non renseignée'}</td>
                    </tr>
                </table>
            </div>
        `;
        
        return this.createPrintDocument('FICHE EMPLOYÉ', content);
    }
}

    /**
     * Template professionnel pour les tâches
     */
    getTaskTemplate() {
        return `
        <!DOCTYPE html>
        <html lang="fr">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{DOCUMENT_TITLE}}</title>
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                    line-height: 1.6;
                    color: #1a1a1a;
                    background: #ffffff;
                    font-size: 14px;
                }

                .document-container {
                    max-width: 210mm;
                    margin: 0 auto;
                    padding: 20mm;
                    min-height: 297mm;
                    position: relative;
                    background: white;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                }

                /* Header Section */
                .document-header {
                    display: flex;
                    align-items: flex-start;
                    justify-content: space-between;
                    padding-bottom: 30px;
                    margin-bottom: 40px;
                    border-bottom: 3px solid #2563eb;
                    position: relative;
                }

                .company-branding {
                    display: flex;
                    align-items: center;
                    gap: 20px;
                }

                .company-logo {
                    width: 80px;
                    height: 80px;
                    background: linear-gradient(135deg, #2563eb, #1d4ed8);
                    border-radius: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 32px;
                    font-weight: 700;
                    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
                }

                .company-info {
                    flex: 1;
                }

                .company-name {
                    font-size: 28px;
                    font-weight: 700;
                    color: #1a1a1a;
                    margin-bottom: 8px;
                    letter-spacing: -0.5px;
                }

                .company-details {
                    color: #6b7280;
                    font-size: 13px;
                    line-height: 1.5;
                }

                .document-meta {
                    text-align: right;
                    color: #6b7280;
                    font-size: 12px;
                }

                .document-type {
                    background: linear-gradient(135deg, #f59e0b, #d97706);
                    color: white;
                    padding: 8px 16px;
                    border-radius: 8px;
                    font-weight: 600;
                    font-size: 11px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    margin-bottom: 8px;
                    display: inline-block;
                }

                /* Document Title */
                .document-title {
                    text-align: center;
                    margin-bottom: 40px;
                }

                .main-title {
                    font-size: 32px;
                    font-weight: 700;
                    color: #1a1a1a;
                    margin-bottom: 8px;
                    letter-spacing: -0.5px;
                }

                .subtitle {
                    color: #6b7280;
                    font-size: 16px;
                    font-weight: 400;
                }

                /* Content Sections */
                .content-section {
                    margin-bottom: 35px;
                }

                .section-title {
                    font-size: 18px;
                    font-weight: 600;
                    color: #1a1a1a;
                    margin-bottom: 20px;
                    padding-bottom: 8px;
                    border-bottom: 2px solid #e5e7eb;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .section-icon {
                    width: 24px;
                    height: 24px;
                    background: linear-gradient(135deg, #2563eb, #1d4ed8);
                    border-radius: 6px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 12px;
                }

                /* Information Grid */
                .info-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 25px;
                    margin-bottom: 30px;
                }

                .info-card {
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 12px;
                    padding: 20px;
                    transition: all 0.2s ease;
                }

                .info-card:hover {
                    border-color: #2563eb;
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.1);
                }

                .info-label {
                    font-size: 12px;
                    font-weight: 600;
                    color: #6b7280;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    margin-bottom: 6px;
                }

                .info-value {
                    font-size: 16px;
                    font-weight: 500;
                    color: #1a1a1a;
                    word-wrap: break-word;
                }

                /* Status and Priority Badges */
                .status-badge, .priority-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 6px;
                    padding: 6px 12px;
                    border-radius: 8px;
                    font-size: 12px;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                .status-new { background: #dbeafe; color: #1d4ed8; }
                .status-progress { background: #fef3c7; color: #d97706; }
                .status-completed { background: #d1fae5; color: #059669; }
                .status-cancelled { background: #fee2e2; color: #dc2626; }

                .priority-low { background: #d1fae5; color: #059669; }
                .priority-medium { background: #fef3c7; color: #d97706; }
                .priority-high { background: #fed7aa; color: #ea580c; }
                .priority-urgent { background: #fee2e2; color: #dc2626; }

                /* Description Box */
                .description-box {
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 12px;
                    padding: 24px;
                    margin: 20px 0;
                    min-height: 120px;
                }

                .description-text {
                    font-size: 14px;
                    line-height: 1.7;
                    color: #374151;
                    white-space: pre-wrap;
                }

                /* Notes Section */
                .notes-section {
                    background: #fffbeb;
                    border: 1px solid #fbbf24;
                    border-radius: 12px;
                    padding: 24px;
                    margin-top: 30px;
                }

                .notes-title {
                    font-size: 16px;
                    font-weight: 600;
                    color: #92400e;
                    margin-bottom: 12px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .notes-content {
                    min-height: 80px;
                    border: 1px dashed #fbbf24;
                    border-radius: 8px;
                    padding: 16px;
                    background: white;
                    color: #6b7280;
                    font-style: italic;
                }

                /* Footer */
                .document-footer {
                    position: absolute;
                    bottom: 20mm;
                    left: 20mm;
                    right: 20mm;
                    border-top: 2px solid #e5e7eb;
                    padding-top: 20px;
                    display: grid;
                    grid-template-columns: 1fr auto 1fr;
                    gap: 20px;
                    align-items: center;
                    font-size: 11px;
                    color: #6b7280;
                }

                .footer-left, .footer-right {
                    line-height: 1.4;
                }

                .footer-center {
                    text-align: center;
                    font-weight: 600;
                    color: #374151;
                }

                .footer-right {
                    text-align: right;
                }

                /* Print Styles */
                @media print {
                    body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
                    .document-container { box-shadow: none; margin: 0; padding: 15mm; }
                    @page { margin: 0; size: A4; }
                }

                /* Responsive */
                @media (max-width: 768px) {
                    .document-container { padding: 15mm; }
                    .info-grid { grid-template-columns: 1fr; gap: 15px; }
                    .document-header { flex-direction: column; gap: 20px; }
                    .company-branding { align-items: center; }
                    .document-meta { text-align: left; }
                }
            </style>
        </head>
        <body>
            <div class="document-container">
                <!-- Header -->
                <div class="document-header">
                    <div class="company-branding">
                        <div class="company-logo">
                            {{COMPANY_LOGO}}
                        </div>
                        <div class="company-info">
                            <div class="company-name">{{COMPANY_NAME}}</div>
                            <div class="company-details">
                                {{COMPANY_ADDRESS}}<br>
                                Tél: {{COMPANY_PHONE}} | Email: {{COMPANY_EMAIL}}<br>
                                Site web: {{COMPANY_WEBSITE}}
                            </div>
                        </div>
                    </div>
                    <div class="document-meta">
                        <div class="document-type">Fiche de Tâche</div>
                        <div>{{CURRENT_DATE}}</div>
                        <div>{{CURRENT_TIME}}</div>
                    </div>
                </div>

                <!-- Title -->
                <div class="document-title">
                    <h1 class="main-title">FICHE DE TÂCHE</h1>
                    <p class="subtitle">Détails et informations de suivi</p>
                </div>

                <!-- Task Information -->
                <div class="content-section">
                    <h2 class="section-title">
                        <span class="section-icon">📋</span>
                        Informations Générales
                    </h2>

                    <div class="info-grid">
                        <div class="info-card">
                            <div class="info-label">Titre de la tâche</div>
                            <div class="info-value">{{TASK_TITLE}}</div>
                        </div>

                        <div class="info-card">
                            <div class="info-label">Statut</div>
                            <div class="info-value">
                                <span class="status-badge {{STATUS_CLASS}}">{{TASK_STATUS}}</span>
                            </div>
                        </div>

                        <div class="info-card">
                            <div class="info-label">Priorité</div>
                            <div class="info-value">
                                <span class="priority-badge {{PRIORITY_CLASS}}">{{TASK_PRIORITY}}</span>
                            </div>
                        </div>

                        <div class="info-card">
                            <div class="info-label">Date d'échéance</div>
                            <div class="info-value">{{TASK_DUE_DATE}}</div>
                        </div>

                        <div class="info-card">
                            <div class="info-label">Assigné à</div>
                            <div class="info-value">{{TASK_ASSIGNEE}}</div>
                        </div>

                        <div class="info-card">
                            <div class="info-label">Catégorie</div>
                            <div class="info-value">{{TASK_CATEGORY}}</div>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="content-section">
                    <h2 class="section-title">
                        <span class="section-icon">📝</span>
                        Description Détaillée
                    </h2>

                    <div class="description-box">
                        <div class="description-text">{{TASK_DESCRIPTION}}</div>
                    </div>
                </div>

                <!-- Notes Section -->
                <div class="notes-section">
                    <div class="notes-title">
                        💡 Notes et Commentaires
                    </div>
                    <div class="notes-content">
                        Espace réservé pour les notes, commentaires et observations...
                    </div>
                </div>

                <!-- Footer -->
                <div class="document-footer">
                    <div class="footer-left">
                        <strong>{{COMPANY_NAME}}</strong><br>
                        {{COMPANY_ADDRESS}}<br>
                        Tél: {{COMPANY_PHONE}}
                    </div>
                    <div class="footer-center">
                        {{FOOTER_TEXT}}
                    </div>
                    <div class="footer-right">
                        Document généré le:<br>
                        <strong>{{CURRENT_DATE}} à {{CURRENT_TIME}}</strong>
                    </div>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    /**
     * Créer une fiche de tâche professionnelle
     */
    createProfessionalTaskDocument(taskData) {
        let template = this.templates.task;

        // Remplacer les variables du template
        const replacements = {
            '{{DOCUMENT_TITLE}}': `Fiche de Tâche - ${taskData.title}`,
            '{{COMPANY_LOGO}}': this.companyInfo.logo_path ?
                `<img src="${this.companyInfo.logo_path}" alt="Logo" style="width:100%;height:100%;object-fit:contain;">` :
                '🏢',
            '{{COMPANY_NAME}}': this.companyInfo.name,
            '{{COMPANY_ADDRESS}}': this.companyInfo.address,
            '{{COMPANY_PHONE}}': this.companyInfo.phone,
            '{{COMPANY_EMAIL}}': this.companyInfo.email,
            '{{COMPANY_WEBSITE}}': this.companyInfo.website,
            '{{CURRENT_DATE}}': new Date().toLocaleDateString('fr-FR'),
            '{{CURRENT_TIME}}': new Date().toLocaleTimeString('fr-FR'),
            '{{TASK_TITLE}}': taskData.title || 'Non défini',
            '{{TASK_STATUS}}': taskData.status || 'Non défini',
            '{{STATUS_CLASS}}': this.getStatusClass(taskData.status),
            '{{TASK_PRIORITY}}': taskData.priority || 'Moyenne',
            '{{PRIORITY_CLASS}}': this.getPriorityClass(taskData.priority),
            '{{TASK_DUE_DATE}}': taskData.dueDate || 'Non définie',
            '{{TASK_ASSIGNEE}}': taskData.assignedTo || 'Non assigné',
            '{{TASK_CATEGORY}}': taskData.category || 'Général',
            '{{TASK_DESCRIPTION}}': taskData.description || 'Aucune description disponible',
            '{{FOOTER_TEXT}}': this.companyInfo.footer_text
        };

        // Appliquer les remplacements
        for (const [placeholder, value] of Object.entries(replacements)) {
            template = template.replace(new RegExp(placeholder, 'g'), value);
        }

        return template;
    }

    /**
     * Obtenir la classe CSS pour le statut
     */
    getStatusClass(status) {
        const classes = {
            'new': 'status-new',
            'pending': 'status-new',
            'in_progress': 'status-progress',
            'completed': 'status-completed',
            'cancelled': 'status-cancelled'
        };
        return classes[status] || 'status-new';
    }

    /**
     * Obtenir la classe CSS pour la priorité
     */
    getPriorityClass(priority) {
        const classes = {
            'low': 'priority-low',
            'medium': 'priority-medium',
            'high': 'priority-high',
            'urgent': 'priority-urgent'
        };
        return classes[priority] || 'priority-medium';
    }

    /**
     * Imprimer un document professionnel
     */
    printProfessionalDocument(documentType, data) {
        let documentHTML;

        switch (documentType) {
            case 'task':
                documentHTML = this.createProfessionalTaskDocument(data);
                break;
            default:
                console.error('Type de document non supporté:', documentType);
                return;
        }

        const printWindow = window.open('', '_blank');
        printWindow.document.write(documentHTML);
        printWindow.document.close();

        // Attendre le chargement complet avant d'imprimer
        printWindow.onload = function() {
            setTimeout(() => {
                printWindow.print();
                // Ne pas fermer automatiquement pour permettre la prévisualisation
                // printWindow.close();
            }, 1000);
        };
    }
}

// Instance globale du système d'impression professionnel
const professionalPrintSystem = new ProfessionalPrintSystem();
