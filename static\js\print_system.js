/**
 * Système de Gestion des Pointages - Système d'Impression Unifié
 * Gestion des impressions avec logo et pied de page de l'entreprise
 */

class PrintSystem {
    constructor() {
        this.companyInfo = null;
        this.loadCompanyInfo();
    }

    /**
     * Charger les informations de l'entreprise
     */
    async loadCompanyInfo() {
        try {
            const response = await fetch('/api/company-info');
            const data = await response.json();
            this.companyInfo = data.company;
        } catch (error) {
            console.error('Erreur lors du chargement des informations de l\'entreprise:', error);
            // Utiliser des informations par défaut
            this.companyInfo = {
                name: 'GESTION DES POINTAGES',
                address: 'Adresse de l\'entreprise',
                phone: '+212 XXX XXX XXX',
                email: '<EMAIL>',
                website: 'www.entreprise.ma',
                logo_path: null,
                footer_text: 'Document généré automatiquement par le système de gestion'
            };
        }
    }

    /**
     * Générer l'en-tête avec logo de l'entreprise
     */
    generateHeader() {
        const logoSection = this.companyInfo.logo_path ? 
            `<img src="${this.companyInfo.logo_path}" alt="Logo" style="max-height: 80px; max-width: 200px;">` :
            `<div class="logo-placeholder">🏢</div>`;

        return `
            <div class="print-header">
                <div class="company-logo">
                    ${logoSection}
                </div>
                <div class="company-info">
                    <h1>${this.companyInfo.name}</h1>
                    <p>${this.companyInfo.address}</p>
                    <p>Tél: ${this.companyInfo.phone} | Email: ${this.companyInfo.email}</p>
                    <p>Site web: ${this.companyInfo.website}</p>
                </div>
            </div>
            <hr class="header-separator">
        `;
    }

    /**
     * Générer le pied de page
     */
    generateFooter() {
        const currentDate = new Date().toLocaleDateString('fr-FR');
        const currentTime = new Date().toLocaleTimeString('fr-FR');

        return `
            <hr class="footer-separator">
            <div class="print-footer">
                <div class="footer-content">
                    <p><strong>${this.companyInfo.footer_text}</strong></p>
                    <div class="footer-details">
                        <div class="footer-left">
                            <p>${this.companyInfo.name}</p>
                            <p>${this.companyInfo.address}</p>
                        </div>
                        <div class="footer-center">
                            <p>Tél: ${this.companyInfo.phone}</p>
                            <p>Email: ${this.companyInfo.email}</p>
                        </div>
                        <div class="footer-right">
                            <p>Document généré le:</p>
                            <p>${currentDate} à ${currentTime}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Générer les styles CSS pour l'impression
     */
    generatePrintStyles() {
        return `
            <style>
                @media print {
                    body { 
                        font-family: Arial, sans-serif; 
                        margin: 0; 
                        padding: 20px;
                        font-size: 12px;
                        line-height: 1.4;
                    }
                    
                    .print-header {
                        display: flex;
                        align-items: center;
                        margin-bottom: 20px;
                        padding-bottom: 15px;
                    }
                    
                    .company-logo {
                        margin-right: 20px;
                        flex-shrink: 0;
                    }
                    
                    .logo-placeholder {
                        font-size: 48px;
                        color: #666;
                        width: 80px;
                        height: 80px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border: 2px solid #ddd;
                        border-radius: 8px;
                    }
                    
                    .company-info h1 {
                        margin: 0 0 10px 0;
                        font-size: 24px;
                        color: #333;
                        font-weight: bold;
                    }
                    
                    .company-info p {
                        margin: 5px 0;
                        color: #666;
                        font-size: 11px;
                    }
                    
                    .header-separator, .footer-separator {
                        border: none;
                        border-top: 2px solid #333;
                        margin: 15px 0;
                    }
                    
                    .print-content {
                        min-height: 400px;
                        margin: 20px 0;
                    }
                    
                    .print-footer {
                        margin-top: 30px;
                        padding-top: 15px;
                        page-break-inside: avoid;
                    }
                    
                    .footer-content {
                        text-align: center;
                    }
                    
                    .footer-details {
                        display: flex;
                        justify-content: space-between;
                        margin-top: 15px;
                        font-size: 10px;
                    }
                    
                    .footer-left, .footer-center, .footer-right {
                        flex: 1;
                    }
                    
                    .footer-left {
                        text-align: left;
                    }
                    
                    .footer-center {
                        text-align: center;
                    }
                    
                    .footer-right {
                        text-align: right;
                    }
                    
                    .footer-details p {
                        margin: 2px 0;
                        color: #666;
                    }
                    
                    /* Masquer les éléments non imprimables */
                    .no-print, .btn, .navbar, .sidebar, .modal {
                        display: none !important;
                    }
                    
                    /* Styles pour les tableaux */
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 15px 0;
                    }
                    
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: left;
                    }
                    
                    th {
                        background-color: #f5f5f5;
                        font-weight: bold;
                    }
                    
                    /* Éviter les coupures de page */
                    .page-break-avoid {
                        page-break-inside: avoid;
                    }
                }
                
                @page {
                    margin: 2cm;
                    size: A4;
                }
            </style>
        `;
    }

    /**
     * Créer un document d'impression complet
     */
    createPrintDocument(title, content) {
        return `
            <!DOCTYPE html>
            <html lang="fr">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${title}</title>
                ${this.generatePrintStyles()}
            </head>
            <body>
                ${this.generateHeader()}
                <div class="print-content">
                    <h2 style="text-align: center; margin: 20px 0; color: #333;">${title}</h2>
                    ${content}
                </div>
                ${this.generateFooter()}
            </body>
            </html>
        `;
    }

    /**
     * Imprimer un document
     */
    printDocument(title, content) {
        const printWindow = window.open('', '_blank');
        const documentHTML = this.createPrintDocument(title, content);
        
        printWindow.document.write(documentHTML);
        printWindow.document.close();
        
        // Attendre que les images se chargent avant d'imprimer
        printWindow.onload = function() {
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);
        };
    }

    /**
     * Générer un PDF (nécessite une bibliothèque PDF)
     */
    async generatePDF(title, content) {
        // Cette fonction peut être étendue avec jsPDF ou une autre bibliothèque
        console.log('Génération PDF:', title);
        // Pour l'instant, utiliser l'impression
        this.printDocument(title, content);
    }

    /**
     * Créer une fiche de tâche pour impression
     */
    createTaskSheet(taskData) {
        const content = `
            <div class="task-sheet">
                <table class="task-info-table">
                    <tr>
                        <th style="width: 30%;">Titre de la tâche</th>
                        <td>${taskData.title || 'Non défini'}</td>
                    </tr>
                    <tr>
                        <th>Description</th>
                        <td style="white-space: pre-wrap;">${taskData.description || 'Aucune description'}</td>
                    </tr>
                    <tr>
                        <th>Statut</th>
                        <td>${taskData.status || 'Non défini'}</td>
                    </tr>
                    <tr>
                        <th>Priorité</th>
                        <td>${taskData.priority || 'Normale'}</td>
                    </tr>
                    <tr>
                        <th>Assigné à</th>
                        <td>${taskData.assignedTo || 'Non assigné'}</td>
                    </tr>
                    <tr>
                        <th>Date d'échéance</th>
                        <td>${taskData.dueDate || 'Non définie'}</td>
                    </tr>
                    <tr>
                        <th>Catégorie</th>
                        <td>${taskData.category || 'Non définie'}</td>
                    </tr>
                    <tr>
                        <th>Tags</th>
                        <td>${taskData.tags || 'Aucun'}</td>
                    </tr>
                </table>
                
                <div class="task-notes" style="margin-top: 30px;">
                    <h3>Notes et Commentaires:</h3>
                    <div style="border: 1px solid #ddd; padding: 15px; min-height: 100px; background-color: #f9f9f9;">
                        ${taskData.notes || 'Aucune note disponible'}
                    </div>
                </div>
            </div>
        `;
        
        return this.createPrintDocument('FICHE DE TÂCHE', content);
    }

    /**
     * Créer une fiche d'employé pour impression
     */
    createEmployeeSheet(employeeData) {
        const content = `
            <div class="employee-sheet">
                <table class="employee-info-table">
                    <tr>
                        <th style="width: 30%;">Nom complet</th>
                        <td>${employeeData.firstName} ${employeeData.lastName}</td>
                    </tr>
                    <tr>
                        <th>CIN</th>
                        <td>${employeeData.cin || 'Non renseigné'}</td>
                    </tr>
                    <tr>
                        <th>Poste</th>
                        <td>${employeeData.position || 'Non défini'}</td>
                    </tr>
                    <tr>
                        <th>Salaire</th>
                        <td>${employeeData.salary || 'Non renseigné'} MAD</td>
                    </tr>
                    <tr>
                        <th>Date d'embauche</th>
                        <td>${employeeData.hireDate || 'Non renseignée'}</td>
                    </tr>
                    <tr>
                        <th>Téléphone</th>
                        <td>${employeeData.phone || 'Non renseigné'}</td>
                    </tr>
                    <tr>
                        <th>Email</th>
                        <td>${employeeData.email || 'Non renseigné'}</td>
                    </tr>
                    <tr>
                        <th>Adresse</th>
                        <td>${employeeData.address || 'Non renseignée'}</td>
                    </tr>
                </table>
            </div>
        `;
        
        return this.createPrintDocument('FICHE EMPLOYÉ', content);
    }
}

// Instance globale du système d'impression
const printSystem = new PrintSystem();
