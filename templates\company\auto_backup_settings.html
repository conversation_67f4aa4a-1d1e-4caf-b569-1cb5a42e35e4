{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-cog text-primary me-2"></i>
                        Paramètres de Sauvegarde Automatique
                    </h3>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <!-- Activation et fréquence -->
                            <div class="col-md-6">
                                <h5 class="mb-3 text-primary">
                                    <i class="fas fa-clock me-2"></i>Configuration de Base
                                </h5>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        {{ form.is_enabled(class="form-check-input") }}
                                        {{ form.is_enabled.label(class="form-check-label") }}
                                    </div>
                                    <small class="form-text text-muted">
                                        Active ou désactive les sauvegardes automatiques
                                    </small>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.frequence.label(class="form-label") }}
                                    {{ form.frequence(class="form-select", onchange="toggleFrequencyOptions()") }}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.heure_execution.label(class="form-label") }}
                                    {{ form.heure_execution(class="form-control") }}
                                    <small class="form-text text-muted">
                                        Heure à laquelle la sauvegarde sera exécutée
                                    </small>
                                </div>
                                
                                <div class="mb-3" id="jour-semaine-group" style="display: none;">
                                    {{ form.jour_semaine.label(class="form-label") }}
                                    {{ form.jour_semaine(class="form-select") }}
                                </div>
                                
                                <div class="mb-3" id="jour-mois-group" style="display: none;">
                                    {{ form.jour_mois.label(class="form-label") }}
                                    {{ form.jour_mois(class="form-control") }}
                                    <small class="form-text text-muted">
                                        Jour du mois (1-31). Si le jour n'existe pas dans le mois, la sauvegarde sera faite le dernier jour du mois.
                                    </small>
                                </div>
                            </div>
                            
                            <!-- Rétention et stockage -->
                            <div class="col-md-6">
                                <h5 class="mb-3 text-primary">
                                    <i class="fas fa-archive me-2"></i>Rétention et Stockage
                                </h5>
                                
                                <div class="mb-3">
                                    {{ form.nombre_max_sauvegardes.label(class="form-label") }}
                                    {{ form.nombre_max_sauvegardes(class="form-control") }}
                                    <small class="form-text text-muted">
                                        Nombre maximum de sauvegardes à conserver
                                    </small>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.supprimer_anciennes(class="form-check-input") }}
                                        {{ form.supprimer_anciennes.label(class="form-check-label") }}
                                    </div>
                                    <small class="form-text text-muted">
                                        Supprime automatiquement les sauvegardes les plus anciennes
                                    </small>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.dossier_sauvegarde.label(class="form-label") }}
                                    {{ form.dossier_sauvegarde(class="form-control") }}
                                    <small class="form-text text-muted">
                                        Laissez vide pour utiliser le dossier par défaut
                                    </small>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.inclure_uploads(class="form-check-input") }}
                                        {{ form.inclure_uploads.label(class="form-check-label") }}
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.compression(class="form-check-input") }}
                                        {{ form.compression.label(class="form-check-label") }}
                                    </div>
                                    <small class="form-text text-muted">
                                        Compresse les sauvegardes pour économiser l'espace
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <!-- Notifications -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="mb-3 text-primary">
                                    <i class="fas fa-envelope me-2"></i>Notifications
                                </h5>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                {{ form.email_notification(class="form-check-input", onchange="toggleEmailField()") }}
                                                {{ form.email_notification.label(class="form-check-label") }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3" id="email-field">
                                            {{ form.email_destinataire.label(class="form-label") }}
                                            {{ form.email_destinataire(class="form-control") }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <!-- Informations sur la dernière sauvegarde -->
                        {% if settings.last_backup %}
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="mb-3 text-primary">
                                    <i class="fas fa-info-circle me-2"></i>Dernière Sauvegarde
                                </h5>
                                
                                <div class="alert alert-info">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>Date:</strong> {{ settings.last_backup.strftime('%d/%m/%Y à %H:%M') }}
                                        </div>
                                        <div class="col-md-6">
                                            <strong>Prochaine sauvegarde prévue:</strong>
                                            {% if settings.frequence == 'daily' %}
                                                Demain à {{ settings.heure_execution.strftime('%H:%M') }}
                                            {% elif settings.frequence == 'weekly' %}
                                                {% set jours = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'] %}
                                                {{ jours[settings.jour_semaine|int] }} à {{ settings.heure_execution.strftime('%H:%M') }}
                                            {% elif settings.frequence == 'monthly' %}
                                                Le {{ settings.jour_mois }} du mois à {{ settings.heure_execution.strftime('%H:%M') }}
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('company.backup_management') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Retour aux Sauvegardes
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-primary me-2" onclick="testBackup()">
                                    <i class="fas fa-play me-1"></i> Tester Maintenant
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> Enregistrer les Paramètres
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de test de sauvegarde -->
<div class="modal fade" id="testBackupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test de Sauvegarde</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Voulez-vous exécuter une sauvegarde de test maintenant ?</p>
                <p class="text-muted">Cela créera une sauvegarde avec les paramètres actuels pour vérifier que tout fonctionne correctement.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="executeTestBackup()">
                    <i class="fas fa-play me-1"></i> Exécuter le Test
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    toggleFrequencyOptions();
    toggleEmailField();
});

function toggleFrequencyOptions() {
    const frequence = document.querySelector('select[name="frequence"]').value;
    const jourSemaineGroup = document.getElementById('jour-semaine-group');
    const jourMoisGroup = document.getElementById('jour-mois-group');
    
    // Masquer tous les groupes
    jourSemaineGroup.style.display = 'none';
    jourMoisGroup.style.display = 'none';
    
    // Afficher le groupe approprié
    if (frequence === 'weekly') {
        jourSemaineGroup.style.display = 'block';
    } else if (frequence === 'monthly') {
        jourMoisGroup.style.display = 'block';
    }
}

function toggleEmailField() {
    const emailNotification = document.querySelector('input[name="email_notification"]').checked;
    const emailField = document.getElementById('email-field');
    
    if (emailNotification) {
        emailField.style.display = 'block';
        document.querySelector('input[name="email_destinataire"]').required = true;
    } else {
        emailField.style.display = 'none';
        document.querySelector('input[name="email_destinataire"]').required = false;
    }
}

function testBackup() {
    new bootstrap.Modal(document.getElementById('testBackupModal')).show();
}

function executeTestBackup() {
    // Fermer le modal
    bootstrap.Modal.getInstance(document.getElementById('testBackupModal')).hide();
    
    // Afficher un message de chargement
    const loadingToast = document.createElement('div');
    loadingToast.className = 'toast align-items-center text-white bg-primary border-0';
    loadingToast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-spinner fa-spin me-2"></i>
                Exécution de la sauvegarde de test...
            </div>
        </div>
    `;
    
    // Ajouter le toast au DOM et l'afficher
    document.body.appendChild(loadingToast);
    const toast = new bootstrap.Toast(loadingToast);
    toast.show();
    
    // Simuler l'exécution de la sauvegarde
    setTimeout(() => {
        loadingToast.remove();
        
        // Afficher le résultat
        const resultToast = document.createElement('div');
        resultToast.className = 'toast align-items-center text-white bg-success border-0';
        resultToast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check me-2"></i>
                    Sauvegarde de test exécutée avec succès !
                </div>
            </div>
        `;
        
        document.body.appendChild(resultToast);
        const resultToastInstance = new bootstrap.Toast(resultToast);
        resultToastInstance.show();
        
        setTimeout(() => resultToast.remove(), 5000);
    }, 3000);
}
</script>
{% endblock %}

{% block styles %}
<style>
.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-switch .form-check-input {
    width: 2em;
    margin-left: -2.5em;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280,0,0,.25%29'/%3e%3c/svg%3e");
    background-position: left center;
    background-repeat: no-repeat;
    background-size: contain !important;
    transition: background-position .15s ease-in-out;
}

.form-switch .form-check-input:checked {
    background-position: right center;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1055;
}
</style>
{% endblock %}
