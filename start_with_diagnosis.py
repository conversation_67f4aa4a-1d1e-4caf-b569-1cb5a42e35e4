#!/usr/bin/env python3
"""
Script de démarrage avec diagnostic complet
"""
import os
import sys
import socket

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_port(port):
    """Vérifie si un port est libre"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', port))
            return True
    except OSError:
        return False

def diagnose_system():
    """Diagnostic du système"""
    print("🔍 DIAGNOSTIC DU SYSTÈME")
    print("-" * 30)
    
    # Python version
    print(f"🐍 Python: {sys.version.split()[0]}")
    
    # Current directory
    print(f"📁 Répertoire: {os.getcwd()}")
    
    # Check required files
    required_files = ['app.py', 'config.py', 'extensions.py', 'models.py', 'routes.py']
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} manquant")
    
    # Check database
    db_path = os.path.join('instance', 'app.db')
    if os.path.exists(db_path):
        print(f"✅ Base de données: {db_path}")
    else:
        print(f"⚠️  Base de données sera créée: {db_path}")
    
    # Check ports
    ports = [5001, 5000, 8000]
    print("🔌 Ports disponibles:")
    for port in ports:
        status = "✅ Libre" if check_port(port) else "❌ Occupé"
        print(f"   {port}: {status}")
    
    print("-" * 30)

def main():
    print("🚀 GESTION DES POINTAGES - DÉMARRAGE AVEC DIAGNOSTIC")
    print("=" * 60)
    
    # Diagnostic
    diagnose_system()
    
    try:
        # Import test
        print("\n📦 Test des imports...")
        from app import create_app, create_tables
        print("✅ Imports réussis")
        
        # App creation
        print("🏗️  Création de l'application...")
        app = create_app()
        print("✅ Application créée")
        
        # Database
        print("🗄️  Initialisation de la base de données...")
        create_tables(app)
        print("✅ Base de données prête")
        
        # Find free port
        port = 5001
        if not check_port(port):
            print(f"⚠️  Port {port} occupé, recherche d'un port libre...")
            for test_port in range(5002, 5010):
                if check_port(test_port):
                    port = test_port
                    break
        
        # Start server
        print(f"\n🌐 Démarrage du serveur sur le port {port}...")
        print("=" * 60)
        print(f"📍 URL: http://127.0.0.1:{port}")
        print("👤 Utilisateur: admin")
        print("🔑 Mot de passe: admin")
        print("=" * 60)
        print("⏹️  Appuyez sur Ctrl+C pour arrêter")
        print("=" * 60)
        
        app.run(
            host='127.0.0.1',
            port=port,
            debug=True,
            use_reloader=False
        )
        
    except ImportError as e:
        print(f"\n❌ Erreur d'import: {e}")
        print("💡 Exécutez: pip install -r requirements.txt")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("\n👋 Fermeture...")
        input("Appuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
