{% extends "base.html" %}

{% block title %}Calendrier des présences - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3"><i class="fas fa-calendar-check me-2"></i>Calendrier des présences</h1>
        <p class="text-muted">Consultez et gérez les présences des employés.</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('attendance.daily_attendance', date=date.today().isoformat()) }}" class="btn btn-primary">
            <i class="fas fa-clipboard-check me-1"></i> Saisir les présences du jour
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-4">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>{{ month_name }} {{ year }}</h5>
            </div>
            <div class="col-md-4 text-center">
                <div class="btn-group" role="group">
                    <a href="{{ url_for('attendance.attendance_calendar', year=prev_year, month=prev_month) }}" class="btn btn-outline-primary">
                        <i class="fas fa-chevron-left"></i> Mois précédent
                    </a>
                    <a href="{{ url_for('attendance.attendance_calendar') }}" class="btn btn-outline-primary">
                        Aujourd'hui
                    </a>
                    <a href="{{ url_for('attendance.attendance_calendar', year=next_year, month=next_month) }}" class="btn btn-outline-primary">
                        Mois suivant <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex justify-content-end">
                    <span class="badge bg-success me-2">Présent</span>
                    <span class="badge bg-danger me-2">Absent</span>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr class="text-center">
                        <th>Lundi</th>
                        <th>Mardi</th>
                        <th>Mercredi</th>
                        <th>Jeudi</th>
                        <th>Vendredi</th>
                        <th>Samedi</th>
                        <th>Dimanche</th>
                    </tr>
                </thead>
                <tbody>
                    {% for week in calendar %}
                    <tr>
                        {% for day in week %}
                        <td class="calendar-day {% if day and day.is_today %}today{% endif %}">
                            {% if day %}
                            <div class="day-number">{{ day.day }}</div>
                            
                            <div class="text-end mb-2">
                                <a href="{{ url_for('attendance.daily_attendance', date=day.date.isoformat()) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-clipboard-check"></i>
                                </a>
                            </div>
                            
                            {% if day.attendances %}
                            <div class="attendance-summary mb-2">
                                <div class="mb-1">
                                    <span class="badge bg-success">{{ day.attendances|selectattr('present', 'eq', true)|list|length }} présents</span>
                                </div>
                                <div>
                                    <span class="badge bg-danger">{{ day.attendances|selectattr('present', 'eq', false)|list|length }} absents</span>
                                </div>
                            </div>
                            {% endif %}
                            {% endif %}
                        </td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Légende</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <h6>Présences des employés</h6>
                        <ul class="list-unstyled">
                            <li><span class="badge bg-success me-2">Présent</span> Employé présent ce jour</li>
                            <li><span class="badge bg-danger me-2">Absent</span> Employé absent ce jour</li>
                        </ul>
                    </div>
                </div>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i> Cliquez sur le bouton <i class="fas fa-clipboard-check"></i> d'un jour pour saisir ou modifier les présences de ce jour.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
