{% extends "base.html" %}

{% block title %}Présences quotidiennes - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3"><i class="fas fa-clipboard-check me-2"></i>Présences quotidiennes</h1>
        <p class="text-muted">Enregistrez les présences des employés pour le {{ selected_date.strftime('%d/%m/%Y') }}.</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('attendance.attendance_calendar') }}" class="btn btn-secondary">
            <i class="fas fa-calendar-alt me-1"></i> Retour au calendrier
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0"><i class="fas fa-clipboard-check me-2"></i>Présences du {{ selected_date.strftime('%d/%m/%Y') }}</h5>
            </div>
            <div class="col-md-6 text-end">
                <form class="d-inline-flex" method="GET" action="{{ url_for('attendance.daily_attendance') }}">
                    <input type="date" name="date" class="form-control me-2" value="{{ selected_date.isoformat() }}">
                    <button type="submit" class="btn btn-primary">Afficher</button>
                </form>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if employees %}
        <form method="POST" action="{{ url_for('attendance.daily_attendance', date=selected_date.isoformat()) }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Employé</th>
                            <th>Présent</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employees %}
                        <tr>
                            <td>{{ employee.full_name }}</td>
                            <td>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="present_{{ employee.id }}" name="present_{{ employee.id }}" {% if employee.id in attendances and attendances[employee.id].present %}checked{% endif %}>
                                    <label class="form-check-label" for="present_{{ employee.id }}">Présent</label>
                                </div>
                            </td>
                            <td>
                                <input type="text" class="form-control" name="notes_{{ employee.id }}" placeholder="Notes (optionnel)" value="{{ attendances[employee.id].notes if employee.id in attendances else '' }}">
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                <a href="{{ url_for('attendance.attendance_calendar') }}" class="btn btn-secondary me-md-2">Annuler</a>
                <button type="submit" class="btn btn-primary">Enregistrer les présences</button>
            </div>
        </form>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> Aucun employé actif n'a été trouvé. Veuillez ajouter des employés avant de saisir les présences.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
