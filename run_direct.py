"""
تشغيل البرنامج مباشرة من المصدر الأصلي
بدون ملف تنفيذي - تشغيل مباشر
"""

import os
import sys
import time
import threading
import webbrowser

# إعداد المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(current_dir)
sys.path.insert(0, current_dir)

def setup_environment():
    """إعداد البيئة"""
    print("إعداد البيئة...")
    directories = ['instance', 'logs', 'backups', 'uploads']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    print("تم إعداد البيئة")

def fix_database():
    """إصلاح قاعدة البيانات"""
    print("فحص قاعدة البيانات...")
    try:
        # تشغيل سكريبت الإصلاح
        import subprocess
        result = subprocess.run([sys.executable, 'fix_database_final.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("تم إصلاح قاعدة البيانات")
            return True
        else:
            print("خطأ في إصلاح قاعدة البيانات")
            return False
    except Exception as e:
        print(f"خطأ: {e}")
        return False

def open_browser():
    """فتح المتصفح"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5001')
        print("تم فتح المتصفح")
    except:
        print("افتح المتصفح يدوياً: http://localhost:5001")

def start_flask_app():
    """تشغيل تطبيق Flask"""
    try:
        # استيراد التطبيق
        from app import create_app
        
        app = create_app()
        
        # إعدادات الإنتاج
        app.config['DEBUG'] = False
        app.config['TESTING'] = False
        
        print("تم تشغيل الخادم بنجاح")
        print("الرابط: http://localhost:5001")
        print("للإيقاف: اضغط Ctrl+C")
        
        # تشغيل الخادم
        app.run(
            host='127.0.0.1',
            port=5001,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للإغلاق...")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("نظام إدارة الحضور والانصراف")
    print("تشغيل مباشر من المصدر")
    print("=" * 60)
    print()
    
    # إعداد البيئة
    setup_environment()
    
    # فحص وإصلاح قاعدة البيانات إذا لزم الأمر
    if not os.path.exists('instance/app.db'):
        if not fix_database():
            print("فشل في إعداد قاعدة البيانات")
            input("اضغط Enter للإغلاق...")
            return
    
    print()
    print("بدء تشغيل الخادم...")
    print("المستخدم: admin")
    print("كلمة المرور: admin")
    print("سيتم فتح المتصفح تلقائياً...")
    print("-" * 60)
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    # تشغيل التطبيق
    start_flask_app()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\nتم إيقاف الخادم")
    except Exception as e:
        print(f"خطأ: {e}")
        input("اضغط Enter للإغلاق...")
