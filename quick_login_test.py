#!/usr/bin/env python3
"""
Test rapide de connexion
"""

import requests
import sys

def test_login():
    """Test de connexion"""
    base_url = "http://127.0.0.1:5001"
    
    try:
        # Test de la page de connexion
        print("🔍 Test de la page de connexion...")
        response = requests.get(f"{base_url}/login", timeout=5)
        
        if response.status_code == 200:
            print("✅ Page de connexion accessible")
        else:
            print(f"❌ Page de connexion inaccessible: {response.status_code}")
            return False
        
        # Test de connexion avec admin/admin
        print("🔍 Test de connexion admin/admin...")
        
        # Obtenir le token CSRF
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(response.text, 'html.parser')
        csrf_token = soup.find('input', {'name': 'csrf_token'})
        
        login_data = {
            'username': 'admin',
            'password': 'admin',
            'remember_me': False
        }
        
        if csrf_token:
            login_data['csrf_token'] = csrf_token['value']
        
        # Session pour maintenir les cookies
        session = requests.Session()
        
        # Obtenir la page de connexion avec la session
        login_page = session.get(f"{base_url}/login")
        soup = BeautifulSoup(login_page.text, 'html.parser')
        csrf_token = soup.find('input', {'name': 'csrf_token'})
        
        if csrf_token:
            login_data['csrf_token'] = csrf_token['value']
        
        # Tenter la connexion
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code == 200 and 'dashboard' in login_response.url:
            print("✅ Connexion réussie!")
            return True
        else:
            print(f"❌ Échec de connexion: {login_response.status_code}")
            print(f"URL de redirection: {login_response.url}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter au serveur")
        print("💡 Assurez-vous que le serveur est démarré avec: python run_app.py")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Test rapide de connexion")
    print("=" * 40)
    
    if test_login():
        print("\n🎉 Test réussi!")
        print("💡 Vous pouvez vous connecter avec admin/admin")
    else:
        print("\n⚠️  Test échoué!")
        print("💡 Vérifiez que le serveur est démarré")
        print("💡 Utilisez: python run_app.py")

if __name__ == "__main__":
    main()
