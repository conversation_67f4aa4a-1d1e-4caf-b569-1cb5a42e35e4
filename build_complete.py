"""
بناء البرنامج الكامل مع جميع الملفات والقوالب
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build():
    """تنظيف ملفات البناء السابقة"""
    print("🧹 تنظيف ملفات البناء السابقة...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ تم حذف {dir_name}")

def collect_all_files():
    """جمع جميع الملفات المطلوبة"""
    print("📁 جمع جميع الملفات...")
    
    # الملفات الأساسية
    python_files = [
        'app.py', 'models.py', 'routes.py', 'routes_admin.py', 'routes_company.py',
        'forms.py', 'forms_company.py', 'config.py', 'extensions.py',
        'activity_logger.py', 'models_company.py', 'start.py',
        'fix_database_final.py'
    ]
    
    # التحقق من وجود الملفات
    missing_files = []
    for file in python_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"⚠️ ملفات مفقودة: {missing_files}")
        return False
    
    print("✓ جميع الملفات الأساسية موجودة")
    return True

def build_executable():
    """بناء الملف التنفيذي"""
    print("🔨 بناء الملف التنفيذي...")
    
    # أمر PyInstaller مع جميع الملفات
    cmd = [
        'pyinstaller',
        '--onefile',
        '--console',
        '--name=GestionPointagesComplete',
        '--distpath=dist',
        '--workpath=build',
        '--clean',
        '--noconfirm',
        
        # إضافة جميع المجلدات
        '--add-data=templates;templates',
        '--add-data=static;static',
        '--add-data=instance;instance',
        
        # إضافة الملفات الأساسية
        '--add-data=app.py;.',
        '--add-data=models.py;.',
        '--add-data=routes.py;.',
        '--add-data=routes_admin.py;.',
        '--add-data=routes_company.py;.',
        '--add-data=forms.py;.',
        '--add-data=forms_company.py;.',
        '--add-data=config.py;.',
        '--add-data=extensions.py;.',
        '--add-data=activity_logger.py;.',
        '--add-data=models_company.py;.',
        '--add-data=start.py;.',
        '--add-data=fix_database_final.py;.',
        
        # الوحدات المخفية
        '--hidden-import=flask',
        '--hidden-import=flask_sqlalchemy',
        '--hidden-import=flask_login',
        '--hidden-import=flask_wtf',
        '--hidden-import=flask_babel',
        '--hidden-import=wtforms',
        '--hidden-import=sqlalchemy',
        '--hidden-import=babel',
        '--hidden-import=jinja2',
        '--hidden-import=werkzeug',
        '--hidden-import=markupsafe',
        '--hidden-import=itsdangerous',
        '--hidden-import=click',
        '--hidden-import=blinker',
        '--hidden-import=alembic',
        '--hidden-import=email_validator',
        '--hidden-import=sqlite3',
        '--hidden-import=models',
        '--hidden-import=models_company',
        '--hidden-import=forms',
        '--hidden-import=forms_company',
        '--hidden-import=routes',
        '--hidden-import=routes_admin',
        '--hidden-import=routes_company',
        '--hidden-import=activity_logger',
        
        # الملف الرئيسي
        'run_original.py'
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ تم بناء الملف التنفيذي بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ خطأ في البناء: {e}")
        print(f"الخطأ: {e.stderr}")
        return False

def verify_build():
    """التحقق من نجاح البناء"""
    exe_path = 'dist/GestionPointagesComplete.exe'
    if os.path.exists(exe_path):
        size = os.path.getsize(exe_path) / (1024 * 1024)  # بالميجابايت
        print(f"✓ الملف التنفيذي تم إنشاؤه: {size:.1f} MB")
        return True
    else:
        print("✗ فشل في إنشاء الملف التنفيذي")
        return False

def create_test_script():
    """إنشاء سكريبت اختبار"""
    test_script = '''@echo off
echo ========================================
echo   اختبار البرنامج الكامل
echo ========================================
echo.

echo تشغيل البرنامج...
cd dist
GestionPointagesComplete.exe

pause
'''
    
    with open('test_complete.bat', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✓ تم إنشاء سكريبت الاختبار: test_complete.bat")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏗️ بناء البرنامج الكامل مع جميع الملفات")
    print("=" * 60)
    print()
    
    # تنظيف ملفات البناء السابقة
    clean_build()
    
    # جمع جميع الملفات
    if not collect_all_files():
        print("✗ فشل في جمع الملفات")
        return
    
    # بناء الملف التنفيذي
    if not build_executable():
        print("✗ فشل في بناء الملف التنفيذي")
        return
    
    # التحقق من نجاح البناء
    if not verify_build():
        print("✗ فشل في التحقق من البناء")
        return
    
    # إنشاء سكريبت الاختبار
    create_test_script()
    
    print()
    print("🎉 تم بناء البرنامج الكامل بنجاح!")
    print()
    print("📋 الملفات المُنشأة:")
    print("- dist/GestionPointagesComplete.exe")
    print("- test_complete.bat")
    print()
    print("🚀 للاختبار:")
    print("1. شغل test_complete.bat")
    print("2. أو انتقل إلى مجلد dist وشغل GestionPointagesComplete.exe")
    print()
    print("👤 بيانات الدخول: admin / admin")

if __name__ == "__main__":
    main()
