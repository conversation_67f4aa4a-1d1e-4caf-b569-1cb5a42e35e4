{% extends "base.html" %}

{% block title %}Suivi des activités utilisateurs - Gestion des Pointages{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête de la page -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="mb-3">
                <i class="fas fa-user-clock me-2"></i>
                Suivi des activités utilisateurs
            </h1>
            <p class="text-muted">Consultez l'historique des actions effectuées par les utilisateurs</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
                    <i class="fas fa-filter me-1"></i>
                    Filtrer
                </button>
                <a href="{{ url_for('admin.export_activities') }}" class="btn btn-outline-success">
                    <i class="fas fa-download me-1"></i>
                    Exporter
                </a>
            </div>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Aujourd'hui</h6>
                            <h4 class="mb-0">{{ stats.today_count }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Cette semaine</h6>
                            <h4 class="mb-0">{{ stats.week_count }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-week fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Utilisateurs actifs</h6>
                            <h4 class="mb-0">{{ stats.active_users }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total activités</h6>
                            <h4 class="mb-0">{{ stats.total_count }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des activités -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Historique des activités
            </h5>
        </div>
        <div class="card-body">
            {% if activities %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date et heure</th>
                            <th>Utilisateur</th>
                            <th>Action</th>
                            <th>Description</th>
                            <th>Adresse IP</th>
                            <th>Détails</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for activity in activities %}
                        <tr>
                            <td>
                                <small class="text-muted">{{ activity.formatted_timestamp }}</small>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-primary text-white me-2">
                                        {{ activity.user.username[0].upper() if activity.user else '?' }}
                                    </div>
                                    <div>
                                        <strong>{{ activity.user.username if activity.user else 'Utilisateur supprimé' }}</strong>
                                        {% if activity.user and activity.user.email %}
                                        <br><small class="text-muted">{{ activity.user.email }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ get_action_color(activity.action) }}">
                                    {{ get_action_label(activity.action) }}
                                </span>
                            </td>
                            <td>
                                <span class="text-truncate" style="max-width: 200px; display: inline-block;" 
                                      title="{{ activity.description }}">
                                    {{ activity.description or '-' }}
                                </span>
                            </td>
                            <td>
                                <small class="text-muted">{{ activity.ip_address or '-' }}</small>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-info" 
                                        onclick="showActivityDetails({{ activity.id }})"
                                        data-bs-toggle="modal" data-bs-target="#detailsModal">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if pagination %}
            <nav aria-label="Navigation des activités">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.user_activities', page=pagination.prev_num) }}">
                            Précédent
                        </a>
                    </li>
                    {% endif %}

                    {% for page_num in pagination.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != pagination.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.user_activities', page=page_num) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.user_activities', page=pagination.next_num) }}">
                            Suivant
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-user-clock fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Aucune activité enregistrée</h5>
                <p class="text-muted">Les activités des utilisateurs apparaîtront ici</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal de filtrage -->
<div class="modal fade" id="filterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-filter me-2"></i>
                    Filtrer les activités
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="GET" action="{{ url_for('admin.user_activities') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Utilisateur</label>
                        <select class="form-select" name="user_id">
                            <option value="">Tous les utilisateurs</option>
                            {% for user in users %}
                            <option value="{{ user.id }}" {% if request.args.get('user_id') == user.id|string %}selected{% endif %}>
                                {{ user.username }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Type d'action</label>
                        <select class="form-select" name="action">
                            <option value="">Toutes les actions</option>
                            <option value="login" {% if request.args.get('action') == 'login' %}selected{% endif %}>Connexion</option>
                            <option value="logout" {% if request.args.get('action') == 'logout' %}selected{% endif %}>Déconnexion</option>
                            <option value="create" {% if request.args.get('action') == 'create' %}selected{% endif %}>Création</option>
                            <option value="update" {% if request.args.get('action') == 'update' %}selected{% endif %}>Modification</option>
                            <option value="delete" {% if request.args.get('action') == 'delete' %}selected{% endif %}>Suppression</option>
                            <option value="view" {% if request.args.get('action') == 'view' %}selected{% endif %}>Consultation</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Date de début</label>
                                <input type="date" class="form-control" name="date_from" 
                                       value="{{ request.args.get('date_from', '') }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Date de fin</label>
                                <input type="date" class="form-control" name="date_to" 
                                       value="{{ request.args.get('date_to', '') }}">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <a href="{{ url_for('admin.user_activities') }}" class="btn btn-outline-warning">Réinitialiser</a>
                    <button type="submit" class="btn btn-primary">Appliquer les filtres</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de détails -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Détails de l'activité
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="activityDetails">
                <!-- Le contenu sera chargé dynamiquement -->
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showActivityDetails(activityId) {
    const detailsContainer = document.getElementById('activityDetails');
    
    // Afficher le spinner
    detailsContainer.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    `;
    
    // Charger les détails
    fetch(`/admin/activity_details/${activityId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                detailsContainer.innerHTML = data.html;
            } else {
                detailsContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erreur lors du chargement des détails
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            detailsContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Erreur de connexion
                </div>
            `;
        });
}
</script>
{% endblock %}
