"""
Script d'initialisation pour l'application
"""
import os
import sqlite3
from config import Config

def ensure_upload_folder():
    """S'assure que le dossier d'uploads existe"""
    upload_folder = Config.UPLOAD_FOLDER
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder, exist_ok=True)
        print(f"Dossier d'uploads créé: {upload_folder}")
    else:
        print(f"Le dossier d'uploads existe déjà: {upload_folder}")
    return True

def ensure_attachment_path_column():
    """S'assure que la colonne attachment_path existe dans la table tasks"""
    # Obtenir le chemin de la base de données
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'app.db')
    
    if not os.path.exists(db_path):
        print(f"Erreur: Le fichier de base de données {db_path} n'existe pas.")
        return False
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vérifier si la colonne existe déjà
        cursor.execute("PRAGMA table_info(tasks)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'attachment_path' in column_names:
            print("La colonne attachment_path existe déjà dans la table tasks.")
        else:
            # Ajouter la colonne
            cursor.execute("ALTER TABLE tasks ADD COLUMN attachment_path TEXT")
            conn.commit()
            print("La colonne attachment_path a été ajoutée à la table tasks.")
        
        # Fermer la connexion
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"Erreur SQLite: {e}")
        return False
    except Exception as e:
        print(f"Erreur: {e}")
        return False

if __name__ == "__main__":
    print("Initialisation de l'application...")
    
    # S'assurer que le dossier d'uploads existe
    ensure_upload_folder()
    
    # S'assurer que la colonne attachment_path existe
    ensure_attachment_path_column()
    
    print("Initialisation terminée.")
