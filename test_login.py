#!/usr/bin/env python3
"""
Test script for login functionality
"""
import sys
import os

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_login():
    print("🔍 TEST DE CONNEXION")
    print("=" * 40)
    
    try:
        # Import de l'application
        from app import create_app, create_tables
        from models import User
        from extensions import db
        
        app = create_app()
        
        with app.app_context():
            # Créer les tables si nécessaire
            create_tables(app)
            
            # Vérifier si l'utilisateur admin existe
            admin = User.query.filter_by(username='admin').first()
            
            if admin:
                print("✅ Utilisateur admin trouvé")
                print(f"   - Username: {admin.username}")
                print(f"   - Email: {admin.email}")
                print(f"   - Is Admin: {admin.is_admin}")
                print(f"   - Created: {admin.created_at}")
                print(f"   - Last Login: {admin.last_login}")
                
                # Test du mot de passe
                if admin.check_password('admin'):
                    print("✅ Mot de passe 'admin' correct")
                else:
                    print("❌ Mot de passe 'admin' incorrect")
                    
                    # Essayer de réinitialiser le mot de passe
                    print("🔧 Réinitialisation du mot de passe...")
                    admin.set_password('admin')
                    db.session.commit()
                    print("✅ Mot de passe réinitialisé à 'admin'")
                    
            else:
                print("❌ Utilisateur admin non trouvé")
                print("🔧 Création de l'utilisateur admin...")
                
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    is_admin=True,
                    can_manage_users=True,
                    can_manage_employees=True,
                    can_manage_tasks=True,
                    can_manage_finances=True,
                    can_access_registration_files=True,
                    can_access_technical_files=True,
                    can_access_reimbursement_files=True,
                    can_access_organization_files=True,
                    can_access_trainer_files=True,
                    can_access_trainer_schedule=True
                )
                admin.set_password('admin')
                db.session.add(admin)
                db.session.commit()
                print("✅ Utilisateur admin créé")
            
            # Lister tous les utilisateurs
            users = User.query.all()
            print(f"\n📋 Total des utilisateurs: {len(users)}")
            for user in users:
                print(f"   - {user.username} ({user.email}) - Admin: {user.is_admin}")
                
            # Test de la route de connexion
            print("\n🌐 Test des routes...")
            with app.test_client() as client:
                # Test GET login page
                response = client.get('/login')
                print(f"   GET /login: {response.status_code}")
                
                # Test POST login
                response = client.post('/login', data={
                    'username': 'admin',
                    'password': 'admin',
                    'csrf_token': 'test'  # Pour les tests
                }, follow_redirects=False)
                print(f"   POST /login: {response.status_code}")
                
                if response.status_code == 302:
                    print(f"   Redirection vers: {response.location}")
                
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login()
