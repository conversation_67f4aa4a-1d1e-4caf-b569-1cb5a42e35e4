"""
Créer la distribution professionnelle de Gestion des Pointages
"""

import os
import shutil
import zipfile
from datetime import datetime

def create_distribution():
    """Créer la distribution complète"""
    
    # Nom du dossier de distribution
    dist_name = "Gestion_des_Pointages_v1.0.0_Professional"
    
    # Créer le dossier de distribution
    if os.path.exists(dist_name):
        shutil.rmtree(dist_name)
    os.makedirs(dist_name)
    
    print(f"Création de la distribution: {dist_name}")
    
    # Copier le fichier exécutable principal
    exe_source = os.path.join("dist", "Gestion des Pointages.exe")
    exe_dest = os.path.join(dist_name, "Gestion des Pointages.exe")
    
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, exe_dest)
        print("✓ Fichier exécutable copié")
    else:
        print("✗ Fichier exécutable non trouvé!")
        return False
    
    # Copier le guide utilisateur
    guide_source = "GUIDE_UTILISATEUR.md"
    guide_dest = os.path.join(dist_name, "GUIDE_UTILISATEUR.md")
    
    if os.path.exists(guide_source):
        shutil.copy2(guide_source, guide_dest)
        print("✓ Guide utilisateur copié")
    
    # Créer un fichier README en français
    readme_content = """# Gestion des Pointages - Version Professionnelle

## Installation Rapide

1. **Lancez** le fichier `Gestion des Pointages.exe`
2. **Cliquez** sur "Démarrer le Serveur"
3. **Ouvrez** votre navigateur sur http://localhost:5001
4. **Connectez-vous** avec: admin / admin

## Support Multi-utilisateurs

- **Serveur:** Lancez l'application sur l'ordinateur principal
- **Clients:** Connectez-vous via navigateur à l'adresse IP du serveur
- **Port:** 5001 (assurez-vous qu'il soit ouvert dans le pare-feu)

## Configuration Système

- **Windows 7/8/8.1/10/11** (32-bit / 64-bit)
- **Aucune installation** supplémentaire requise
- **Fonctionne** immédiatement après téléchargement

## Documentation Complète

Consultez le fichier `GUIDE_UTILISATEUR.md` pour la documentation complète.

---
© 2025 Gestion des Pointages. Tous droits réservés.
"""
    
    readme_path = os.path.join(dist_name, "README.txt")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✓ README créé")
    
    # Créer un fichier de version
    version_info = {
        "nom": "Gestion des Pointages",
        "version": "1.0.0",
        "date_build": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "description": "Système professionnel de gestion des pointages",
        "compatibilite": "Windows 7/8/8.1/10/11 (32-bit/64-bit)",
        "fonctionnalites": [
            "Gestion des employés",
            "Système de pointage",
            "Gestion des tâches", 
            "Module financier",
            "Support multi-utilisateurs",
            "Sauvegarde automatique",
            "Interface professionnelle"
        ]
    }
    
    version_path = os.path.join(dist_name, "version_info.json")
    import json
    with open(version_path, 'w', encoding='utf-8') as f:
        json.dump(version_info, f, indent=2, ensure_ascii=False)
    print("✓ Informations de version créées")
    
    # Créer un script de lancement rapide
    launcher_script = """@echo off
chcp 65001 >nul
title Gestion des Pointages - Lancement Rapide
echo.
echo ================================================
echo   Gestion des Pointages - Version 1.0.0
echo ================================================
echo.
echo Lancement de l'application...
echo.

start "" "Gestion des Pointages.exe"

echo Application lancée!
echo.
echo Fermez cette fenêtre après avoir démarré le serveur.
timeout /t 3 >nul
"""
    
    launcher_path = os.path.join(dist_name, "Lancer_Application.bat")
    with open(launcher_path, 'w', encoding='utf-8') as f:
        f.write(launcher_script)
    print("✓ Script de lancement créé")
    
    # Créer un fichier de configuration réseau
    network_config = """# Configuration Réseau - Gestion des Pointages

## Pour l'Administrateur Réseau

### Port Utilisé
- Port TCP: 5001
- Protocole: HTTP

### Configuration Pare-feu Windows
1. Ouvrir "Pare-feu Windows avec sécurité avancée"
2. Cliquer sur "Règles de trafic entrant"
3. Cliquer sur "Nouvelle règle..."
4. Sélectionner "Port" → Suivant
5. Sélectionner "TCP" et saisir "5001" → Suivant
6. Sélectionner "Autoriser la connexion" → Suivant
7. Cocher toutes les cases → Suivant
8. Nom: "Gestion des Pointages" → Terminer

### Adresses IP Typiques
- Réseau local: 192.168.1.x
- Exemple serveur: *************:5001
- Exemple client: http://*************:5001

### Test de Connectivité
1. Sur le serveur: ping [IP_CLIENT]
2. Sur le client: telnet [IP_SERVEUR] 5001
3. Navigateur: http://[IP_SERVEUR]:5001
"""
    
    network_path = os.path.join(dist_name, "Configuration_Reseau.txt")
    with open(network_path, 'w', encoding='utf-8') as f:
        f.write(network_config)
    print("✓ Guide de configuration réseau créé")
    
    # Calculer la taille totale
    total_size = 0
    for root, dirs, files in os.walk(dist_name):
        for file in files:
            file_path = os.path.join(root, file)
            total_size += os.path.getsize(file_path)
    
    size_mb = total_size / (1024 * 1024)
    print(f"✓ Taille totale: {size_mb:.1f} MB")
    
    # Créer l'archive ZIP
    zip_name = f"{dist_name}.zip"
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(dist_name):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, dist_name)
                zipf.write(file_path, arc_name)
    
    zip_size = os.path.getsize(zip_name) / (1024 * 1024)
    print(f"✓ Archive ZIP créée: {zip_name} ({zip_size:.1f} MB)")
    
    print("\n" + "="*60)
    print("DISTRIBUTION CRÉÉE AVEC SUCCÈS!")
    print("="*60)
    print(f"Dossier: {dist_name}/")
    print(f"Archive: {zip_name}")
    print(f"Fichiers inclus:")
    print("  - Gestion des Pointages.exe (Application principale)")
    print("  - GUIDE_UTILISATEUR.md (Documentation complète)")
    print("  - README.txt (Installation rapide)")
    print("  - Lancer_Application.bat (Script de lancement)")
    print("  - Configuration_Reseau.txt (Guide réseau)")
    print("  - version_info.json (Informations de version)")
    print("\n🎉 Prêt pour la distribution!")
    
    return True

if __name__ == "__main__":
    create_distribution()
