#!/usr/bin/env python3
"""
Detailed test script for login functionality
"""
import sys
import os

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_login_detailed():
    print("🔍 TEST DÉTAILLÉ DE CONNEXION")
    print("=" * 50)
    
    try:
        # Import de l'application
        from app import create_app, create_tables
        from models import User
        from extensions import db
        from forms import LoginForm
        
        app = create_app()
        
        with app.app_context():
            # Créer les tables si nécessaire
            create_tables(app)
            
            # Test avec le client de test Flask
            with app.test_client() as client:
                print("1️⃣ Test de la page de connexion...")
                
                # GET login page
                response = client.get('/login')
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    print("   ✅ Page de connexion accessible")
                    
                    # Extraire le token CSRF de la réponse
                    html_content = response.data.decode('utf-8')
                    
                    # Chercher le token CSRF
                    import re
                    csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', html_content)
                    
                    if csrf_match:
                        csrf_token = csrf_match.group(1)
                        print(f"   ✅ Token CSRF trouvé: {csrf_token[:20]}...")
                        
                        print("\n2️⃣ Test de connexion avec credentials corrects...")
                        
                        # POST login avec token CSRF
                        response = client.post('/login', data={
                            'username': 'admin',
                            'password': 'admin',
                            'csrf_token': csrf_token,
                            'remember_me': False,
                            'submit': 'Se connecter'
                        }, follow_redirects=False)
                        
                        print(f"   Status: {response.status_code}")
                        
                        if response.status_code == 302:
                            print("   ✅ Redirection réussie")
                            print(f"   📍 Redirection vers: {response.location}")
                            
                            # Suivre la redirection
                            response = client.get(response.location)
                            print(f"   Status après redirection: {response.status_code}")
                            
                            if response.status_code == 200:
                                print("   ✅ Dashboard accessible après connexion")
                            else:
                                print("   ❌ Problème d'accès au dashboard")
                                
                        elif response.status_code == 200:
                            print("   ❌ Pas de redirection - erreur de connexion")
                            # Vérifier les erreurs dans la réponse
                            html_content = response.data.decode('utf-8')
                            if 'incorrect' in html_content.lower():
                                print("   ❌ Credentials incorrects")
                            elif 'csrf' in html_content.lower():
                                print("   ❌ Erreur CSRF")
                            else:
                                print("   ❌ Erreur inconnue")
                        else:
                            print(f"   ❌ Status inattendu: {response.status_code}")
                            
                    else:
                        print("   ❌ Token CSRF non trouvé")
                        
                        # Test sans CSRF (pour debug)
                        print("\n🔧 Test sans CSRF...")
                        response = client.post('/login', data={
                            'username': 'admin',
                            'password': 'admin',
                            'remember_me': False,
                            'submit': 'Se connecter'
                        }, follow_redirects=False)
                        print(f"   Status sans CSRF: {response.status_code}")
                        
                else:
                    print(f"   ❌ Page de connexion inaccessible: {response.status_code}")
                
                print("\n3️⃣ Test de validation du formulaire...")
                
                # Test du formulaire directement
                with app.test_request_context('/login', method='POST', data={
                    'username': 'admin',
                    'password': 'admin',
                    'csrf_token': 'test-token'
                }):
                    form = LoginForm()
                    print(f"   Form validate_on_submit: {form.validate_on_submit()}")
                    print(f"   Form errors: {form.errors}")
                    
                    if form.username.data:
                        print(f"   Username: {form.username.data}")
                    if form.password.data:
                        print(f"   Password: {'*' * len(form.password.data)}")
                        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login_detailed()
