@echo off
title Construction Gestion des Pointages - Version Reseau
color 0A
chcp 65001 >nul

echo ===============================================
echo    Gestion des Pointages
echo    Construction Version Reseau Multi-utilisateurs
echo ===============================================
echo.

echo 🔍 Verification de l'environnement...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python non installe ou absent du PATH
    echo Veuillez installer Python 3.8 ou plus recent
    pause
    exit /b 1
)

echo ✅ Python disponible
echo.

echo 📦 Verification des dependances...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Installation des dependances...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Echec de l'installation des dependances
        pause
        exit /b 1
    )
)

echo ✅ Toutes les dependances sont disponibles
echo.

echo 🔨 Debut de la construction...
python build_network_exe.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في بناء البرنامج
    echo يرجى مراجعة رسائل الخطأ أعلاه
    pause
    exit /b 1
)

echo.
echo 🎉 تم بناء البرنامج بنجاح!
echo.

echo 📁 الملفات المُنشأة:
if exist "dist\GestionPointages_v1.0.0\GestionPointages.exe" (
    echo ✅ الملف التنفيذي: dist\GestionPointages_v1.0.0\GestionPointages.exe
    for %%A in ("dist\GestionPointages_v1.0.0\GestionPointages.exe") do (
        set /a size=%%~zA/1024/1024
        echo    الحجم: !size! ميجابايت تقريباً
    )
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
)

if exist "start_server.bat" (
    echo ✅ سكريبت الخادم: start_server.bat
)

if exist "start_client.bat" (
    echo ✅ سكريبت العميل: start_client.bat
)

if exist "version_info.json" (
    echo ✅ معلومات الإصدار: version_info.json
)

echo.
echo 📋 الخطوات التالية:
echo 1. اختبار البرنامج على الجهاز المحلي
echo 2. إنشاء برنامج التثبيت باستخدام Inno Setup
echo 3. اختبار التثبيت على أجهزة مختلفة
echo 4. توزيع البرنامج على الشبكة المحلية
echo.

echo 🔧 لإنشاء برنامج التثبيت:
echo 1. تأكد من تثبيت Inno Setup
echo 2. افتح ملف setup_network_installer.iss
echo 3. اضغط F9 أو Build → Compile
echo.

echo ✅ عملية البناء مكتملة!
echo.
pause
