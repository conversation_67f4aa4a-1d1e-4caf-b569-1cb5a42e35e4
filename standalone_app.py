"""
Application autonome - Gestion des Pointages
Démarrage direct sans interface supplémentaire
"""

import os
import sys
import time
import threading
import webbrowser
import sqlite3
from pathlib import Path

# Ajouter le répertoire du projet au path
if getattr(sys, 'frozen', False):
    # Mode exécutable
    application_path = os.path.dirname(sys.executable)
    os.chdir(application_path)
else:
    # Mode développement
    application_path = os.path.dirname(os.path.abspath(__file__))
    os.chdir(application_path)

sys.path.insert(0, application_path)

def setup_environment():
    """Configurer l'environnement d'exécution"""
    # Créer les répertoires nécessaires
    directories = ['instance', 'logs', 'backups', 'uploads']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    # Configurer les variables d'environnement
    os.environ['FLASK_ENV'] = 'production'
    os.environ['FLASK_DEBUG'] = 'False'
    
    # Vérifier si la base de données existe
    db_path = os.path.join('instance', 'app.db')
    if not os.path.exists(db_path):
        print("⚠️ Base de données non trouvée, création en cours...")
        create_empty_database(db_path)

def create_empty_database(db_path):
    """Créer une base de données vide"""
    try:
        conn = sqlite3.connect(db_path)
        conn.close()
        print("✓ Base de données vide créée")
    except Exception as e:
        print(f"✗ Erreur lors de la création de la base de données vide: {e}")

def initialize_database():
    """Initialiser la base de données avec les tables et données nécessaires"""
    try:
        # Vérifier si la base de données existe et est valide
        db_path = os.path.join('instance', 'app.db')
        if not os.path.exists(db_path):
            print("⚠️ Base de données non trouvée, exécution du script de correction...")
            import subprocess
            result = subprocess.run([sys.executable, 'fix_database_final.py'],
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"✗ Erreur lors de la création de la base de données: {result.stderr}")
                return False

        from app import create_app
        from models import db, User

        app = create_app()
        with app.app_context():
            # Vérifier que les tables existent
            try:
                admin_user = User.query.filter_by(username='admin').first()
                if not admin_user:
                    print("⚠️ Utilisateur admin non trouvé, recréation de la base de données...")
                    import subprocess
                    result = subprocess.run([sys.executable, 'fix_database_final.py'],
                                          capture_output=True, text=True)
                    if result.returncode != 0:
                        print(f"✗ Erreur lors de la recréation: {result.stderr}")
                        return False

                print("✓ Base de données initialisée avec succès")
                return True

            except Exception as e:
                print(f"⚠️ Erreur de structure de base de données: {e}")
                print("🔧 Correction automatique en cours...")
                import subprocess
                result = subprocess.run([sys.executable, 'fix_database_final.py'],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("✓ Base de données corrigée avec succès")
                    return True
                else:
                    print(f"✗ Erreur lors de la correction: {result.stderr}")
                    return False

    except Exception as e:
        print(f"✗ Erreur fatale lors de l'initialisation: {e}")
        return False

def open_browser():
    """Ouvrir le navigateur après un délai"""
    time.sleep(2)  # Attendre que le serveur démarre
    try:
        webbrowser.open('http://localhost:5001')
        print("✓ Navigateur ouvert")
    except Exception as e:
        print(f"⚠️ Impossible d'ouvrir le navigateur automatiquement: {e}")
        print("📍 Ouvrez manuellement: http://localhost:5001")

def start_flask_server():
    """Démarrer le serveur Flask"""
    try:
        from app import create_app
        
        app = create_app()
        
        # Configuration pour la production
        app.config['DEBUG'] = False
        app.config['TESTING'] = False
        app.config['ENV'] = 'production'
        
        # Démarrer le serveur
        app.run(
            host='127.0.0.1',
            port=5001,
            debug=False,
            threaded=True,
            use_reloader=False
        )
        
    except Exception as e:
        print(f"✗ Erreur lors du démarrage du serveur: {e}")
        print(f"Détails de l'erreur: {str(e)}")
        input("Appuyez sur Entrée pour fermer...")

def main():
    """Fonction principale"""
    print("=" * 60)
    print("🏢 SYSTÈME DE GESTION DES POINTAGES")
    print("=" * 60)
    print()
    
    # Configurer l'environnement
    print("⚙️ Configuration de l'environnement...")
    setup_environment()
    
    # Initialiser la base de données
    print("🗄️ Initialisation de la base de données...")
    if not initialize_database():
        print("✗ Échec de l'initialisation de la base de données")
        input("Appuyez sur Entrée pour fermer...")
        return
    
    # Informations de connexion
    print()
    print("🚀 Démarrage du serveur...")
    print("📍 URL: http://localhost:5001")
    print("👤 Connexion: admin / admin")
    print("🔄 Le navigateur va s'ouvrir automatiquement...")
    print("⚠️ Pour arrêter le serveur, fermez cette fenêtre")
    print("-" * 60)
    
    # Démarrer le navigateur dans un thread séparé
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    # Démarrer le serveur Flask (bloquant)
    start_flask_server()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur...")
    except Exception as e:
        print(f"✗ Erreur fatale: {e}")
        input("Appuyez sur Entrée pour fermer...")
