"""
Script pour corriger définitivement la base de données
Résout tous les problèmes de colonnes et de tables
"""

import os
import sys
import sqlite3
from datetime import datetime
from werkzeug.security import generate_password_hash

def backup_database():
    """Sauvegarder la base de données actuelle"""
    if os.path.exists('instance/app.db'):
        backup_name = f'instance/app_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        import shutil
        shutil.copy2('instance/app.db', backup_name)
        print(f"✓ Sauvegarde créée: {backup_name}")
        return True
    return False

def create_fresh_database():
    """Créer une nouvelle base de données propre"""
    
    # Supprimer l'ancienne base de données
    if os.path.exists('instance/app.db'):
        os.remove('instance/app.db')
        print("✓ Ancienne base de données supprimée")
    
    # <PERSON><PERSON><PERSON> le répertoire instance s'il n'existe pas
    os.makedirs('instance', exist_ok=True)
    
    # Créer la nouvelle base de données
    conn = sqlite3.connect('instance/app.db')
    cursor = conn.cursor()
    
    # Table des utilisateurs
    cursor.execute('''
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(64) UNIQUE NOT NULL,
            email VARCHAR(120) UNIQUE NOT NULL,
            password_hash VARCHAR(128) NOT NULL,
            role VARCHAR(20) DEFAULT 'user',
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME,
            
            -- Permissions
            is_admin BOOLEAN DEFAULT 0,
            can_manage_users BOOLEAN DEFAULT 0,
            can_manage_employees BOOLEAN DEFAULT 0,
            can_manage_tasks BOOLEAN DEFAULT 0,
            can_manage_finances BOOLEAN DEFAULT 0,
            can_access_registration_files BOOLEAN DEFAULT 0,
            can_access_technical_files BOOLEAN DEFAULT 0,
            can_access_reimbursement_files BOOLEAN DEFAULT 0,
            can_access_organization_files BOOLEAN DEFAULT 0,
            can_access_trainer_files BOOLEAN DEFAULT 0,
            can_access_trainer_schedule BOOLEAN DEFAULT 0
        )
    ''')
    
    # Table des employés
    cursor.execute('''
        CREATE TABLE employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            first_name VARCHAR(64) NOT NULL,
            last_name VARCHAR(64) NOT NULL,
            cin VARCHAR(20),
            position VARCHAR(100),
            department VARCHAR(100),
            salary FLOAT,
            hire_date DATE,
            birth_date DATE,
            phone VARCHAR(20),
            email VARCHAR(120),
            address TEXT,
            emergency_contact VARCHAR(100),
            emergency_phone VARCHAR(20),
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Table des tâches
    cursor.execute('''
        CREATE TABLE tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(128) NOT NULL,
            description TEXT,
            attachment_path VARCHAR(255),
            priority INTEGER DEFAULT 0,
            status VARCHAR(20) DEFAULT 'pending',
            due_date DATE,
            category VARCHAR(50),
            tags TEXT,
            color VARCHAR(20) DEFAULT '#3498db',
            assigned_to INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (assigned_to) REFERENCES users (id)
        )
    ''')
    
    # Table des finances
    cursor.execute('''
        CREATE TABLE finances (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type VARCHAR(20) NOT NULL,
            amount FLOAT NOT NULL,
            description TEXT,
            date DATE NOT NULL,
            category VARCHAR(50),
            employee_id INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    # Table des présences
    cursor.execute('''
        CREATE TABLE attendances (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            date DATE NOT NULL,
            check_in_time DATETIME,
            check_out_time DATETIME,
            status VARCHAR(20) DEFAULT 'present',
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    # Table des informations de l'entreprise
    cursor.execute('''
        CREATE TABLE company_info (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_name VARCHAR(200) NOT NULL,
            address TEXT,
            phone VARCHAR(20),
            email VARCHAR(120),
            website VARCHAR(200),
            logo_path VARCHAR(255),
            footer_text TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Table des sauvegardes
    cursor.execute('''
        CREATE TABLE database_backups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INTEGER,
            backup_type VARCHAR(20) DEFAULT 'manual',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            notes TEXT
        )
    ''')
    
    # Table des paramètres de sauvegarde automatique
    cursor.execute('''
        CREATE TABLE auto_backup_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            is_enabled BOOLEAN DEFAULT 0,
            frequency VARCHAR(20) DEFAULT 'daily',
            backup_time VARCHAR(10) DEFAULT '02:00',
            max_backups INTEGER DEFAULT 30,
            backup_location VARCHAR(500),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    print("✓ Tables créées avec succès")
    
    # Créer l'utilisateur admin par défaut
    admin_hash = generate_password_hash('admin')
    cursor.execute('''
        INSERT INTO users (username, email, password_hash, role, is_active, is_admin, 
                          can_manage_users, can_manage_employees, can_manage_tasks, can_manage_finances)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', ('admin', '<EMAIL>', admin_hash, 'admin', 1, 1, 1, 1, 1, 1))
    
    # Créer les informations de l'entreprise par défaut
    cursor.execute('''
        INSERT INTO company_info (company_name, address, phone, email, footer_text)
        VALUES (?, ?, ?, ?, ?)
    ''', (
        'Gestion des Pointages',
        'Adresse de l\'entreprise',
        '+212 XXX XXX XXX',
        '<EMAIL>',
        'Document généré automatiquement par le système de gestion'
    ))
    
    # Créer les paramètres de sauvegarde par défaut
    cursor.execute('''
        INSERT INTO auto_backup_settings (is_enabled, frequency, backup_time, max_backups, backup_location)
        VALUES (?, ?, ?, ?, ?)
    ''', (0, 'daily', '02:00', 30, 'backups/'))
    
    conn.commit()
    conn.close()
    
    print("✓ Données par défaut créées")
    print("✓ Utilisateur admin créé (admin/admin)")

def main():
    """Fonction principale"""
    print("=" * 60)
    print("🔧 CORRECTION DÉFINITIVE DE LA BASE DE DONNÉES")
    print("=" * 60)
    print()
    
    # Sauvegarder l'ancienne base de données
    print("📦 Sauvegarde de l'ancienne base de données...")
    backup_database()
    
    # Créer une nouvelle base de données propre
    print("🗄️ Création d'une nouvelle base de données...")
    create_fresh_database()
    
    print()
    print("✅ CORRECTION TERMINÉE AVEC SUCCÈS!")
    print()
    print("📋 Résumé:")
    print("- ✓ Ancienne base de données sauvegardée")
    print("- ✓ Nouvelle base de données créée")
    print("- ✓ Toutes les tables créées correctement")
    print("- ✓ Utilisateur admin créé (admin/admin)")
    print("- ✓ Données par défaut ajoutées")
    print()
    print("🚀 Vous pouvez maintenant démarrer l'application!")

if __name__ == "__main__":
    main()
