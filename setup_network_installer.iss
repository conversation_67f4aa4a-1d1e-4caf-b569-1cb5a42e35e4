; Script Inno Setup pour Gestion des Pointages
; Version réseau multi-utilisateurs
; Compatible Windows 7-11 (32/64 bit)

#define MyAppName "Gestion des Pointages"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "Système de Gestion des Pointages"
#define MyAppURL "https://github.com/gestion-pointages"
#define MyAppExeName "GestionPointages.exe"
#define MyAppDescription "Système de gestion des pointages multi-utilisateurs pour réseau local"

[Setup]
; Informations de base
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
AppCopyright=Copyright © 2024 {#MyAppPublisher}

; Répertoires d'installation
DefaultDirName={autopf}\{#MyAppName}
DefaultGroupName={#MyAppName}
DisableProgramGroupPage=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=dist\installer
OutputBaseFilename=GestionPointages_Setup_v{#MyAppVersion}
SetupIconFile=static\Image\App Gestion Des Pointages.png
Compression=lzma
SolidCompression=yes
WizardStyle=modern

; Compatibilité Windows
MinVersion=6.1
ArchitecturesAllowed=x86 x64
ArchitecturesInstallIn64BitMode=x64

; Privilèges
PrivilegesRequired=admin
PrivilegesRequiredOverridesAllowed=dialog

; Interface utilisateur
ShowLanguageDialog=no
LanguageDetectionMethod=uilanguage

[Languages]
Name: "french"; MessagesFile: "compiler:Languages\French.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "startmenuicon"; Description: "Créer un raccourci dans le menu Démarrer"; GroupDescription: "{cm:AdditionalIcons}"; Flags: checked
Name: "firewallrule"; Description: "Configurer les règles de pare-feu pour le réseau local"; GroupDescription: "Configuration réseau"; Flags: checked

[Files]
; Fichiers principaux de l'application
Source: "dist\GestionPointages_v{#MyAppVersion}\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

; Fichiers de configuration
Source: "network_config.json"; DestDir: "{app}"; Flags: ignoreversion; Check: not FileExists(ExpandConstant('{app}\network_config.json'))
Source: "version_info.json"; DestDir: "{app}"; Flags: ignoreversion
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion

; Scripts de démarrage
Source: "start_server.bat"; DestDir: "{app}"; Flags: ignoreversion
Source: "start_client.bat"; DestDir: "{app}"; Flags: ignoreversion

; Documentation
Source: "INSTALLATION_GUIDE.md"; DestDir: "{app}\docs"; Flags: ignoreversion; Check: FileExists('INSTALLATION_GUIDE.md')

[Icons]
; Icône principale
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; WorkingDir: "{app}"; IconFilename: "{app}\static\Image\App Gestion Des Pointages.png"

; Icônes pour serveur et client
Name: "{group}\{#MyAppName} - Serveur"; Filename: "{app}\start_server.bat"; WorkingDir: "{app}"; IconFilename: "{app}\static\Image\App Gestion Des Pointages.png"; Comment: "Démarrer en mode serveur"
Name: "{group}\{#MyAppName} - Client"; Filename: "{app}\start_client.bat"; WorkingDir: "{app}"; IconFilename: "{app}\static\Image\App Gestion Des Pointages.png"; Comment: "Démarrer en mode client"

; Icônes optionnelles
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; WorkingDir: "{app}"; IconFilename: "{app}\static\Image\App Gestion Des Pointages.png"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; WorkingDir: "{app}"; Tasks: quicklaunchicon

; Désinstallation
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"

[Run]
; Configuration initiale
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#MyAppName}}"; Flags: nowait postinstall skipifsilent

; Configuration du pare-feu Windows
Filename: "netsh"; Parameters: "advfirewall firewall add rule name=""Gestion des Pointages - Serveur"" dir=in action=allow protocol=TCP localport=5001"; Flags: runhidden; Tasks: firewallrule
Filename: "netsh"; Parameters: "advfirewall firewall add rule name=""Gestion des Pointages - Client"" dir=out action=allow protocol=TCP localport=5001"; Flags: runhidden; Tasks: firewallrule

[UninstallRun]
; Supprimer les règles de pare-feu lors de la désinstallation
Filename: "netsh"; Parameters: "advfirewall firewall delete rule name=""Gestion des Pointages - Serveur"""; Flags: runhidden
Filename: "netsh"; Parameters: "advfirewall firewall delete rule name=""Gestion des Pointages - Client"""; Flags: runhidden

[Code]
// Code Pascal pour la configuration avancée

function GetUninstallString(): String;
var
  sUnInstPath: String;
  sUnInstallString: String;
begin
  sUnInstPath := ExpandConstant('Software\Microsoft\Windows\CurrentVersion\Uninstall\{#emit SetupSetting("AppId")}_is1');
  sUnInstallString := '';
  if not RegQueryStringValue(HKLM, sUnInstPath, 'UninstallString', sUnInstallString) then
    RegQueryStringValue(HKCU, sUnInstPath, 'UninstallString', sUnInstallString);
  Result := sUnInstallString;
end;

function IsUpgrade(): Boolean;
begin
  Result := (GetUninstallString() <> '');
end;

function UnInstallOldVersion(): Integer;
var
  sUnInstallString: String;
  iResultCode: Integer;
begin
  Result := 0;
  sUnInstallString := GetUninstallString();
  if sUnInstallString <> '' then begin
    sUnInstallString := RemoveQuotes(sUnInstallString);
    if Exec(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode) then
      Result := 3
    else
      Result := 2;
  end else
    Result := 1;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if (CurStep=ssInstall) then
  begin
    if (IsUpgrade()) then
    begin
      UnInstallOldVersion();
    end;
  end;
end;

function InitializeSetup(): Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  
  // Vérifier la compatibilité Windows
  if Version.Major < 6 then begin
    MsgBox('Ce programme nécessite Windows 7 ou une version plus récente.', mbError, MB_OK);
    Result := False;
  end else begin
    Result := True;
  end;
end;

procedure InitializeWizard();
begin
  // Configuration de l'assistant d'installation
  WizardForm.WelcomeLabel1.Caption := 'Bienvenue dans l''assistant d''installation de ' + '{#MyAppName}';
  WizardForm.WelcomeLabel2.Caption := 'Cet assistant va installer {#MyAppName} version {#MyAppVersion} sur votre ordinateur.' + #13#13 +
    'Ce logiciel permet la gestion des pointages en réseau local avec support multi-utilisateurs.' + #13#13 +
    'Compatible avec Windows 7, 8, 8.1, 10 et 11 (32-bit et 64-bit).' + #13#13 +
    'Il est recommandé de fermer toutes les autres applications avant de continuer.';
end;

function ShouldSkipPage(PageID: Integer): Boolean;
begin
  // Personnaliser les pages affichées
  Result := False;
end;

procedure CurPageChanged(CurPageID: Integer);
begin
  // Actions lors du changement de page
  case CurPageID of
    wpSelectTasks:
    begin
      // Page de sélection des tâches
      WizardForm.TasksList.Checked[0] := True; // Icône bureau
      WizardForm.TasksList.Checked[1] := True; // Menu démarrer
      WizardForm.TasksList.Checked[2] := True; // Règles pare-feu
    end;
  end;
end;

[Messages]
; Messages personnalisés en français
WelcomeLabel1=Bienvenue dans l'assistant d'installation de [name]
WelcomeLabel2=Cet assistant va installer [name/ver] sur votre ordinateur.%n%nIl est recommandé de fermer toutes les autres applications avant de continuer.
WizardLicense=Contrat de licence
WizardPassword=Mot de passe
WizardInfoBefore=Information
WizardUserInfo=Informations utilisateur
WizardSelectDir=Sélection du répertoire de destination
WizardSelectComponents=Sélection des composants
WizardSelectProgramGroup=Sélection du dossier du menu Démarrer
WizardSelectTasks=Sélection des tâches supplémentaires
WizardReady=Prêt à installer
WizardInstalling=Installation en cours
WizardFinished=Fin de l'installation

[CustomMessages]
; Messages personnalisés
french.NetworkConfig=Configuration réseau
french.ServerMode=Mode serveur (pour l'ordinateur principal)
french.ClientMode=Mode client (pour les autres ordinateurs)
french.FirewallConfig=Configuration du pare-feu Windows
french.CreateNetworkShare=Créer un partage réseau pour la base de données
