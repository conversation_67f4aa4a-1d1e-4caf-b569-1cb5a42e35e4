# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    # Contains a bitmask that specifies the valid bits 'flags'r
    mask=0x3f,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x4,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040C04B0',
        [StringStruct(u'CompanyName', u'Gestion des Pointages'),
        StringStruct(u'FileDescription', u'Système de Gestion des Pointages - Application de gestion multi-utilisateurs'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'GestionPointages'),
        StringStruct(u'LegalCopyright', u'Copyright © 2025 Gestion des Pointages. Tous droits réservés.'),
        StringStruct(u'OriginalFilename', u'GestionPointages.exe'),
        StringStruct(u'ProductName', u'Gestion des Pointages'),
        StringStruct(u'ProductVersion', u'*******'),
        StringStruct(u'Comments', u'Système complet de gestion des pointages, employés, tâches et finances. Support multi-utilisateurs via réseau local.'),
        StringStruct(u'LegalTrademarks', u'Gestion des Pointages™')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1036, 1200])])
  ]
)
