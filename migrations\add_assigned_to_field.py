#!/usr/bin/env python3
"""
Migration pour ajouter le champ assigned_to à la table simple_tasks
"""
import sqlite3
import os

def add_assigned_to_field():
    """Ajouter le champ assigned_to à la table simple_tasks"""
    
    # Chemin vers la base de données
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'app.db')
    
    if not os.path.exists(db_path):
        print("❌ Base de données non trouvée!")
        return
    
    # Connexion à la base de données
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Vérifier si la colonne existe déjà
        cursor.execute("PRAGMA table_info(simple_tasks)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'assigned_to' in columns:
            print("ℹ️  La colonne 'assigned_to' existe déjà.")
            return
        
        print("🔧 Ajout de la colonne 'assigned_to' à la table simple_tasks...")
        
        # Ajouter la colonne assigned_to
        cursor.execute('''
            ALTER TABLE simple_tasks 
            ADD COLUMN assigned_to INTEGER
        ''')
        
        # Valider les changements
        conn.commit()
        print("✅ Colonne 'assigned_to' ajoutée avec succès!")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Erreur lors de l'ajout de la colonne: {e}")
        raise
    
    finally:
        conn.close()

if __name__ == '__main__':
    print("🔧 Migration: Ajout du champ assigned_to")
    print("=" * 50)
    
    add_assigned_to_field()
    
    print("=" * 50)
    print("✅ Migration terminée!")
    print("🚀 Vous pouvez maintenant redémarrer l'application")
    
    input("Appuyez sur Entrée pour continuer...")
