"""
مشغل نهائي محسن للملف التنفيذي
يحل جميع المشاكل المكتشفة
"""

import os
import sys
import time
import threading
import webbrowser
import warnings

# تجاهل التحذيرات
warnings.filterwarnings('ignore')

def setup_paths():
    """إعداد المسارات للملف التنفيذي"""
    if getattr(sys, 'frozen', False):
        # نحن في ملف تنفيذي
        application_path = os.path.dirname(sys.executable)
        bundle_dir = sys._MEIPASS
    else:
        # نحن في بيئة التطوير
        application_path = os.path.dirname(os.path.abspath(__file__))
        bundle_dir = application_path
    
    # تغيير المجلد الحالي
    os.chdir(application_path)
    
    # إضافة المسارات إلى sys.path
    sys.path.insert(0, application_path)
    sys.path.insert(0, bundle_dir)
    
    return application_path, bundle_dir

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    dirs = ['instance', 'logs', 'backups', 'uploads', 'static/uploads']
    for directory in dirs:
        try:
            os.makedirs(directory, exist_ok=True)
        except:
            pass

def check_database():
    """فحص قاعدة البيانات"""
    db_path = 'instance/app.db'
    
    if os.path.exists(db_path):
        size = os.path.getsize(db_path)
        if size > 1000:  # أكبر من 1KB
            print("قاعدة البيانات جاهزة")
            return True
    
    print("انشاء قاعدة البيانات...")
    try:
        # تشغيل سكريبت إنشاء قاعدة البيانات
        import subprocess
        result = subprocess.run([sys.executable, 'fix_database_final.py'], 
                              capture_output=True, text=True, cwd='.')
        if result.returncode == 0:
            print("تم انشاء قاعدة البيانات")
            return True
        else:
            # إنشاء قاعدة بيانات بسيطة
            return create_simple_database()
    except:
        return create_simple_database()

def create_simple_database():
    """إنشاء قاعدة بيانات بسيطة"""
    try:
        import sqlite3
        from werkzeug.security import generate_password_hash
        
        db_path = 'instance/app.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(64) UNIQUE NOT NULL,
                email VARCHAR(120) UNIQUE NOT NULL,
                password_hash VARCHAR(128) NOT NULL,
                role VARCHAR(20) DEFAULT 'user',
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME
            )
        ''')
        
        # إنشاء المستخدم الافتراضي
        admin_hash = generate_password_hash('admin')
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, email, password_hash, role, is_active)
            VALUES (?, ?, ?, ?, ?)
        ''', ('admin', '<EMAIL>', admin_hash, 'admin', 1))
        
        conn.commit()
        conn.close()
        print("تم انشاء قاعدة بيانات بسيطة")
        return True
        
    except Exception as e:
        print(f"خطأ في انشاء قاعدة البيانات: {e}")
        return False

def open_browser():
    """فتح المتصفح"""
    time.sleep(4)
    try:
        webbrowser.open('http://localhost:5001')
        print("تم فتح المتصفح")
    except:
        print("افتح المتصفح يدوياً: http://localhost:5001")

def start_flask_app():
    """تشغيل تطبيق Flask"""
    try:
        print("تحميل التطبيق...")
        
        # تجاهل التحذيرات أثناء الاستيراد
        import warnings
        warnings.filterwarnings('ignore', category=UserWarning)
        warnings.filterwarnings('ignore', module='sqlalchemy')
        
        # استيراد التطبيق
        from app import create_app
        
        app = create_app()
        
        # إعدادات محسنة
        app.config.update({
            'DEBUG': False,
            'TESTING': False,
            'SECRET_KEY': 'gestion-pointages-2025-secure-key',
            'SQLALCHEMY_DATABASE_URI': 'sqlite:///instance/app.db',
            'SQLALCHEMY_TRACK_MODIFICATIONS': False,
            'SQLALCHEMY_ENGINE_OPTIONS': {
                'pool_pre_ping': True,
                'pool_recycle': 300,
            },
            'WTF_CSRF_ENABLED': True,
            'WTF_CSRF_TIME_LIMIT': None,
            'UPLOAD_FOLDER': 'uploads',
            'MAX_CONTENT_LENGTH': 16 * 1024 * 1024  # 16MB max file size
        })
        
        print("تم تحميل التطبيق بنجاح")
        print()
        print("=" * 50)
        print("الخادم يعمل على: http://localhost:5001")
        print("المستخدم: admin")
        print("كلمة المرور: admin")
        print("=" * 50)
        print("للايقاف: اغلق هذه النافذة")
        print()
        
        # تشغيل الخادم
        app.run(
            host='127.0.0.1',
            port=5001,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except ImportError as e:
        print(f"خطأ في استيراد التطبيق: {e}")
        print("تأكد من وجود جميع الملفات المطلوبة")
        input("اضغط Enter للاغلاق...")
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للاغلاق...")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("نظام ادارة الحضور والانصراف")
    print("الاصدار المحسن")
    print("=" * 50)
    print()
    
    # إعداد المسارات
    app_path, bundle_path = setup_paths()
    
    # إنشاء المجلدات
    create_directories()
    
    # فحص قاعدة البيانات
    if not check_database():
        print("فشل في اعداد قاعدة البيانات")
        input("اضغط Enter للاغلاق...")
        return
    
    print("بدء تشغيل الخادم...")
    print("سيتم فتح المتصفح تلقائياً...")
    print()
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    # تشغيل التطبيق
    start_flask_app()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\nتم ايقاف الخادم")
    except Exception as e:
        print(f"خطأ عام: {e}")
        input("اضغط Enter للاغلاق...")
