{% extends "base.html" %}

{% block title %}Ajouter un utilisateur - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3"><i class="fas fa-user-plus me-2"></i>Ajouter un utilisateur</h1>
        <p class="text-muted">Créez un nouvel utilisateur et définissez ses permissions.</p>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>Formulaire d'ajout d'utilisateur</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('user.add_user') }}">
                    {{ form.hidden_tag() }}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="username" class="form-label">{{ form.username.label }}</label>
                            {{ form.username(class="form-control", placeholder="Entrez le nom d'utilisateur") }}
                            {% for error in form.username.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">{{ form.email.label }}</label>
                            {{ form.email(class="form-control", placeholder="Entrez l'email") }}
                            {% for error in form.email.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="password" class="form-label">{{ form.password.label }}</label>
                            {{ form.password(class="form-control", placeholder="Entrez le mot de passe") }}
                            {% for error in form.password.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        <div class="col-md-6">
                            <label for="password2" class="form-label">{{ form.password2.label }}</label>
                            {{ form.password2(class="form-control", placeholder="Confirmez le mot de passe") }}
                            {% for error in form.password2.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>

                    <h5 class="mt-4 mb-3"><i class="fas fa-shield-alt me-2"></i>Permissions et Autorisations</h5>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Définissez les permissions pour ce nouvel utilisateur. L'administrateur a accès à toutes les fonctionnalités.
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0"><i class="fas fa-crown me-2"></i>Permissions Administrateur</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-check mb-3">
                                        {{ form.is_admin(class="form-check-input") }}
                                        <label class="form-check-label" for="is_admin">
                                            <strong><i class="fas fa-user-shield me-2"></i>{{ form.is_admin.label }}</strong>
                                            <br><small class="text-muted">Accès complet à toutes les fonctionnalités du système</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Permissions Spécifiques</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-check mb-2">
                                        {{ form.can_manage_users(class="form-check-input") }}
                                        <label class="form-check-label" for="can_manage_users">
                                            <i class="fas fa-users me-2 text-success"></i>{{ form.can_manage_users.label }}
                                            <br><small class="text-muted">Créer, modifier et supprimer des utilisateurs</small>
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        {{ form.can_manage_employees(class="form-check-input") }}
                                        <label class="form-check-label" for="can_manage_employees">
                                            <i class="fas fa-user-tie me-2 text-info"></i>{{ form.can_manage_employees.label }}
                                            <br><small class="text-muted">Gérer les informations des employés</small>
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        {{ form.can_manage_tasks(class="form-check-input") }}
                                        <label class="form-check-label" for="can_manage_tasks">
                                            <i class="fas fa-tasks me-2 text-warning"></i>{{ form.can_manage_tasks.label }}
                                            <br><small class="text-muted">Créer et assigner des tâches</small>
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        {{ form.can_manage_finances(class="form-check-input") }}
                                        <label class="form-check-label" for="can_manage_finances">
                                            <i class="fas fa-coins me-2 text-primary"></i>{{ form.can_manage_finances.label }}
                                            <br><small class="text-muted">Accès aux données financières</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{{ url_for('user.users') }}" class="btn btn-secondary me-md-2">Annuler</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
