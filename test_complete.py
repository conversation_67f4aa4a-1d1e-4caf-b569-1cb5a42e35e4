"""
اختبار البرنامج الكامل
"""

import os
import subprocess
import sys

def test_executable():
    """اختبار الملف التنفيذي"""
    exe_path = os.path.join('dist', 'GestionPointagesComplete.exe')
    
    if not os.path.exists(exe_path):
        print("✗ الملف التنفيذي غير موجود")
        return False
    
    # حجم الملف
    size = os.path.getsize(exe_path) / (1024 * 1024)
    print(f"✓ الملف موجود: {size:.1f} MB")
    
    # تشغيل الملف
    print("🚀 تشغيل البرنامج...")
    try:
        process = subprocess.Popen([exe_path], cwd=os.path.dirname(exe_path))
        print("✓ تم تشغيل البرنامج بنجاح")
        print("📍 تحقق من فتح المتصفح على: http://localhost:5001")
        print("👤 بيانات الدخول: admin / admin")
        return True
    except Exception as e:
        print(f"✗ خطأ في التشغيل: {e}")
        return False

def main():
    print("=" * 50)
    print("🧪 اختبار البرنامج الكامل")
    print("=" * 50)
    print()
    
    if test_executable():
        print()
        print("🎉 الاختبار نجح!")
        print("📋 التعليمات:")
        print("1. تحقق من فتح المتصفح")
        print("2. سجل دخول بـ: admin / admin")
        print("3. تحقق من وجود جميع القوالب والصفحات")
    else:
        print()
        print("❌ فشل الاختبار")

if __name__ == "__main__":
    main()
