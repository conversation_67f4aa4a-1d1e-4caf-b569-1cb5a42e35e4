#!/usr/bin/env python3
"""
Script pour démarrer l'application Flask
"""
import os
import sys

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Importer et démarrer l'application
if __name__ == "__main__":
    try:
        from app import app
        print("🚀 Démarrage de l'application Gestion des Pointages...")
        print("📍 URL: http://127.0.0.1:5001")
        print("⏹️  Appuyez sur Ctrl+C pour arrêter")
        print("-" * 50)
        app.run(debug=True, host='0.0.0.0', port=5001)
    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")
        input("Appuyez sur Entrée pour fermer...")
