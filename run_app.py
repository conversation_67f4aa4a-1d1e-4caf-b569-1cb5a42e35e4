from app import create_app

if __name__ == "__main__":
    app = create_app()

    # إنشاء الجداول إذا لم تكن موجودة
    with app.app_context():
        from extensions import db
        from models import User

        # إنشاء الجداول
        db.create_all()

        # إنشاء مستخدم admin إذا لم يكن موجوداً
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True,
                can_manage_users=True,
                can_manage_employees=True,
                can_manage_tasks=True,
                can_manage_finances=True
            )
            admin.set_password('admin')
            db.session.add(admin)
            db.session.commit()
            print('✅ Utilisateur admin créé: admin/admin')

    print('🚀 Démarrage du serveur...')
    print('🌐 URL: http://127.0.0.1:5001')
    print('👤 Utilisateur: admin')
    print('🔑 Mot de passe: admin')

    app.run(debug=True, host='127.0.0.1', port=5001)
