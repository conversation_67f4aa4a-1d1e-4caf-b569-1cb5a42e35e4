@echo off
echo ========================================
echo   بناء البرنامج الكامل مع القوالب
echo ========================================
echo.

echo تنظيف الملفات السابقة...
if exist "dist" rmdir /s /q dist
if exist "build" rmdir /s /q build

echo.
echo بناء البرنامج مع جميع الملفات...
pyinstaller --onefile --console --name=GestionPointagesComplete ^
  --add-data="templates;templates" ^
  --add-data="static;static" ^
  --add-data="instance;instance" ^
  --add-data="app.py;." ^
  --add-data="models.py;." ^
  --add-data="routes.py;." ^
  --add-data="routes_admin.py;." ^
  --add-data="routes_company.py;." ^
  --add-data="forms.py;." ^
  --add-data="forms_company.py;." ^
  --add-data="config.py;." ^
  --add-data="extensions.py;." ^
  --add-data="activity_logger.py;." ^
  --add-data="models_company.py;." ^
  --add-data="start.py;." ^
  --add-data="fix_database_final.py;." ^
  --hidden-import=flask ^
  --hidden-import=flask_sqlalchemy ^
  --hidden-import=flask_login ^
  --hidden-import=flask_wtf ^
  --hidden-import=flask_babel ^
  --hidden-import=wtforms ^
  --hidden-import=sqlalchemy ^
  --hidden-import=babel ^
  --hidden-import=jinja2 ^
  --hidden-import=werkzeug ^
  --hidden-import=markupsafe ^
  --hidden-import=itsdangerous ^
  --hidden-import=click ^
  --hidden-import=blinker ^
  --hidden-import=alembic ^
  --hidden-import=email_validator ^
  --hidden-import=sqlite3 ^
  --hidden-import=models ^
  --hidden-import=models_company ^
  --hidden-import=forms ^
  --hidden-import=forms_company ^
  --hidden-import=routes ^
  --hidden-import=routes_admin ^
  --hidden-import=routes_company ^
  --hidden-import=activity_logger ^
  run_original.py

echo.
echo البناء مكتمل!
echo.
echo الملف المُنشأ: dist\GestionPointagesComplete.exe
echo.

pause
