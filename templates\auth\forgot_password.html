<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mot de passe oublié - Gestion des Pointages</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="login-container">
                    <!-- Logo -->
                    <div class="login-logo">
                        <i class="fas fa-clipboard-check fa-3x text-primary mb-3"></i>
                        <h4 class="text-center mb-4">Gestion des Pointages</h4>
                    </div>

                    <!-- Formulaire de mot de passe oublié -->
                    <div class="card login-card">
                        <div class="card-header text-center bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-key me-2"></i>
                                Mot de passe oublié
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Messages flash -->
                            {% with messages = get_flashed_messages(with_categories=true) %}
                                {% if messages %}
                                    {% for category, message in messages %}
                                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                            {{ message }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            {% endwith %}

                            <p class="text-muted text-center mb-4">
                                <i class="fas fa-info-circle me-1"></i>
                                Saisissez votre adresse email pour recevoir un lien de réinitialisation
                            </p>

                            <form method="POST">
                                {{ form.hidden_tag() }}
                                
                                <div class="mb-3">
                                    {{ form.email.label(class="form-label") }}
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-envelope"></i>
                                        </span>
                                        {{ form.email(class="form-control", placeholder="<EMAIL>") }}
                                    </div>
                                    {% if form.email.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.email.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="d-grid mb-3">
                                    {{ form.submit(class="btn btn-primary") }}
                                </div>
                            </form>

                            <div class="text-center">
                                <a href="{{ url_for('auth.login') }}" class="text-decoration-none">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Retour à la connexion
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Informations supplémentaires -->
                    <div class="text-center mt-4">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            Vos données sont sécurisées
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-hide des alertes après 5 secondes
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    if (alert.parentNode) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                }, 5000);
            });
        });
    </script>
</body>
</html>
