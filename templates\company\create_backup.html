{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-save text-primary me-2"></i>
                        Créer une Nouvelle Sauvegarde
                    </h3>
                </div>
                <div class="card-body">
                    <form method="POST" id="backup-form">
                        <!-- Description -->
                        <div class="mb-4">
                            <h5 class="mb-3 text-primary">
                                <i class="fas fa-info-circle me-2"></i>Informations de la Sauvegarde
                            </h5>
                            
                            <div class="mb-3">
                                {{ form.description.label(class="form-label") }}
                                {{ form.description(class="form-control") }}
                                <small class="form-text text-muted">
                                    Ajoutez une description pour identifier facilement cette sauvegarde
                                </small>
                            </div>
                        </div>
                        
                        <!-- Options de sauvegarde -->
                        <div class="mb-4">
                            <h5 class="mb-3 text-primary">
                                <i class="fas fa-cog me-2"></i>Options de Sauvegarde
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-light">
                                        <div class="card-body">
                                            <div class="form-check">
                                                {{ form.inclure_uploads(class="form-check-input") }}
                                                {{ form.inclure_uploads.label(class="form-check-label") }}
                                            </div>
                                            <small class="form-text text-muted">
                                                Inclut tous les fichiers uploadés (logos, documents, etc.)
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card border-light">
                                        <div class="card-body">
                                            <div class="form-check">
                                                {{ form.compression(class="form-check-input") }}
                                                {{ form.compression.label(class="form-check-label") }}
                                            </div>
                                            <small class="form-text text-muted">
                                                Compresse la sauvegarde pour réduire la taille du fichier
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Informations sur ce qui sera sauvegardé -->
                        <div class="mb-4">
                            <h5 class="mb-3 text-primary">
                                <i class="fas fa-list me-2"></i>Contenu de la Sauvegarde
                            </h5>
                            
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-database me-2"></i>Cette sauvegarde inclura:
                                </h6>
                                <ul class="mb-0">
                                    <li><strong>Base de données complète:</strong> Tous les utilisateurs, employés, tâches, présences, et enregistrements financiers</li>
                                    <li><strong>Informations de l'entreprise:</strong> Configuration et paramètres</li>
                                    <li><strong>Fichiers uploadés:</strong> Logos, documents, pièces jointes (si option activée)</li>
                                    <li><strong>Configuration système:</strong> Paramètres de l'application</li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- Estimation de la taille -->
                        <div class="mb-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-4">
                                            <h6 class="text-muted">Taille estimée de la base</h6>
                                            <h4 class="text-primary" id="db-size">Calcul...</h4>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-muted">Fichiers uploadés</h6>
                                            <h4 class="text-info" id="files-size">Calcul...</h4>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-muted">Total estimé</h6>
                                            <h4 class="text-success" id="total-size">Calcul...</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('company.backup_management') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg" id="create-btn">
                                <i class="fas fa-save me-2"></i> Créer la Sauvegarde
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de progression -->
<div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog fa-spin me-2"></i>Création de la Sauvegarde
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="progress-bar">
                            0%
                        </div>
                    </div>
                </div>
                <p class="mb-0" id="progress-text">Initialisation...</p>
                <small class="text-muted" id="progress-detail">Préparation de la sauvegarde</small>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    calculateSizes();
    
    // Recalculer les tailles quand les options changent
    document.getElementById('inclure_uploads').addEventListener('change', calculateSizes);
    document.getElementById('compression').addEventListener('change', calculateSizes);
});

function calculateSizes() {
    // Simuler le calcul des tailles (en réalité, cela devrait être fait via AJAX)
    setTimeout(() => {
        const dbSize = Math.random() * 10 + 5; // 5-15 MB
        const filesSize = document.getElementById('inclure_uploads').checked ? Math.random() * 50 + 10 : 0; // 10-60 MB
        const compression = document.getElementById('compression').checked ? 0.3 : 1; // 30% de compression
        
        const totalSize = (dbSize + filesSize) * compression;
        
        document.getElementById('db-size').textContent = formatSize(dbSize * 1024 * 1024);
        document.getElementById('files-size').textContent = filesSize > 0 ? formatSize(filesSize * 1024 * 1024) : 'Non inclus';
        document.getElementById('total-size').textContent = formatSize(totalSize * 1024 * 1024);
    }, 500);
}

function formatSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// Gestion de la soumission du formulaire
document.getElementById('backup-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Afficher le modal de progression
    const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
    progressModal.show();
    
    // Simuler la progression
    simulateProgress();
    
    // Soumettre le formulaire après un délai
    setTimeout(() => {
        this.submit();
    }, 5000);
});

function simulateProgress() {
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const progressDetail = document.getElementById('progress-detail');
    
    const steps = [
        { percent: 10, text: 'Préparation...', detail: 'Vérification des permissions' },
        { percent: 25, text: 'Sauvegarde de la base de données...', detail: 'Export des tables' },
        { percent: 50, text: 'Compression des données...', detail: 'Optimisation de la taille' },
        { percent: 75, text: 'Inclusion des fichiers...', detail: 'Copie des uploads' },
        { percent: 90, text: 'Finalisation...', detail: 'Vérification de l\'intégrité' },
        { percent: 100, text: 'Terminé!', detail: 'Sauvegarde créée avec succès' }
    ];
    
    let currentStep = 0;
    
    const interval = setInterval(() => {
        if (currentStep < steps.length) {
            const step = steps[currentStep];
            progressBar.style.width = step.percent + '%';
            progressBar.textContent = step.percent + '%';
            progressText.textContent = step.text;
            progressDetail.textContent = step.detail;
            
            if (step.percent === 100) {
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-success');
            }
            
            currentStep++;
        } else {
            clearInterval(interval);
        }
    }, 800);
}
</script>
{% endblock %}

{% block styles %}
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.progress {
    background-color: #e9ecef;
}

.progress-bar {
    transition: width 0.6s ease;
}

.text-primary {
    color: #0d6efd !important;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

.modal-content {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
</style>
{% endblock %}
