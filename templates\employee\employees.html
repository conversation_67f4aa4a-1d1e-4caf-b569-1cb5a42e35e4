{% extends "base.html" %}

{% block title %}Gestion des employés - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3"><i class="fas fa-users me-2"></i>Gestion des employés</h1>
        <p class="text-muted">Gérez les employés et leurs informations.</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('employee.add_employee') }}" class="btn btn-primary">
            <i class="fas fa-user-plus me-1"></i> Ajouter un employé
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>Liste des employés</h5>
            </div>
            <div class="col-md-6">
                <input type="text" id="table-filter" class="form-control" placeholder="Rechercher un employé...">
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped filterable-table">
                <thead>
                    <tr>
                        <th>Nom</th>
                        <th>Poste</th>
                        <th>CIN</th>
                        <th>CNSS</th>
                        <th>Téléphone</th>
                        <th>Prix/jour (MAD)</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr>
                        <td>{{ employee.full_name }}</td>
                        <td>{{ employee.position }}</td>
                        <td>{{ employee.cin }}</td>
                        <td>{{ employee.cnss or '-' }}</td>
                        <td>{{ employee.phone or '-' }}</td>
                        <td>{{ employee.daily_rate|round(2) }} MAD</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('employee.edit_employee', id=employee.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ url_for('employee.delete_employee', id=employee.id) }}" method="POST" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                    <button type="submit" class="btn btn-sm btn-danger delete-confirm" data-bs-toggle="tooltip" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                <button type="button" class="btn btn-sm btn-info print-item" data-bs-toggle="tooltip" title="Imprimer" onclick="printEmployeeInfo({{ employee.id }}, '{{ employee.full_name }}', '{{ employee.position }}', '{{ employee.cin }}', '{{ employee.cnss or '' }}', '{{ employee.phone or '' }}', {{ employee.daily_rate }})">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">Aucun employé trouvé</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function printEmployeeInfo(id, fullName, position, cin, cnss, phone, dailyRate) {
        // Créer une fenêtre d'impression
        let printWindow = window.open('', '_blank');

        // Contenu HTML à imprimer
        let content = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Fiche Employé - ${fullName}</title>
                <style>
                    body { font-family: Arial, sans-serif; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .info-table { width: 100%; border-collapse: collapse; }
                    .info-table th, .info-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    .info-table th { background-color: #f2f2f2; width: 30%; }
                    .footer { margin-top: 30px; text-align: center; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Fiche Employé</h1>
                    <p>Date d'impression: ${new Date().toLocaleDateString()}</p>
                </div>

                <table class="info-table">
                    <tr>
                        <th>ID</th>
                        <td>${id}</td>
                    </tr>
                    <tr>
                        <th>Nom complet</th>
                        <td>${fullName}</td>
                    </tr>
                    <tr>
                        <th>Poste</th>
                        <td>${position}</td>
                    </tr>
                    <tr>
                        <th>CIN</th>
                        <td>${cin}</td>
                    </tr>
                    <tr>
                        <th>CNSS</th>
                        <td>${cnss || '-'}</td>
                    </tr>
                    <tr>
                        <th>Téléphone</th>
                        <td>${phone || '-'}</td>
                    </tr>
                    <tr>
                        <th>Prix par jour</th>
                        <td>${dailyRate.toFixed(2)} MAD</td>
                    </tr>
                </table>

                <div class="footer">
                    <p>Gestion des Pointages - Document généré automatiquement</p>
                </div>
            </body>
            </html>
        `;

        // Écrire le contenu dans la fenêtre d'impression
        printWindow.document.open();
        printWindow.document.write(content);
        printWindow.document.close();

        // Imprimer après le chargement du contenu
        printWindow.onload = function() {
            printWindow.print();
        };
    }
</script>
{% endblock %}