#!/usr/bin/env python3
"""
Test script pour démarrer l'application
"""
import sys
import os

try:
    print("🔧 Importation de l'application...")
    from app import app
    print("✅ Application importée avec succès")
    
    print("🚀 Démarrage du serveur...")
    print("📍 URL: http://127.0.0.1:5001")
    print("⏹️  Appuyez sur Ctrl+C pour arrêter")
    print("-" * 50)
    
    app.run(debug=True, host='127.0.0.1', port=5001, use_reloader=False)
    
except Exception as e:
    print(f"❌ Erreur: {e}")
    import traceback
    traceback.print_exc()
    input("Appuyez sur Entrée pour fermer...")
