{% extends "base.html" %}

{% block title %}Gestion des utilisateurs - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3"><i class="fas fa-user-cog me-2"></i>Gestion des utilisateurs</h1>
        <p class="text-muted">Gérez les utilisateurs du système et leurs permissions.</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('user.add_user') }}" class="btn btn-primary">
            <i class="fas fa-user-plus me-1"></i> Ajouter un utilisateur
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>Liste des utilisateurs</h5>
            </div>
            <div class="col-md-6">
                <input type="text" id="table-filter" class="form-control" placeholder="Rechercher un utilisateur...">
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped filterable-table">
                <thead>
                    <tr>
                        <th>Nom d'utilisateur</th>
                        <th>Email</th>
                        <th>Rôle</th>
                        <th>Dernière connexion</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.username }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            {% if user.is_admin %}
                            <span class="badge bg-danger">Administrateur</span>
                            {% else %}
                            <span class="badge bg-primary">Utilisateur</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.last_login %}
                            {{ user.last_login.strftime('%d/%m/%Y %H:%M') }}
                            {% else %}
                            Jamais connecté
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('user.edit_user', id=user.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.id != current_user.id %}
                                <form action="{{ url_for('user.delete_user', id=user.id) }}" method="POST" class="d-inline">
                                    <button type="submit" class="btn btn-sm btn-danger delete-confirm" data-bs-toggle="tooltip" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                                <button type="button" class="btn btn-sm btn-info print-item" data-bs-toggle="tooltip" title="Imprimer" onclick="printUserInfo({{ user.id }}, '{{ user.username }}')">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="5" class="text-center">Aucun utilisateur trouvé</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function printUserInfo(userId, username) {
        // Créer une fenêtre d'impression
        let printWindow = window.open('', '_blank');

        // Contenu HTML à imprimer
        let content = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Informations de l'utilisateur - ${username}</title>
                <style>
                    body { font-family: Arial, sans-serif; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .info-table { width: 100%; border-collapse: collapse; }
                    .info-table th, .info-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    .info-table th { background-color: #f2f2f2; }
                    .footer { margin-top: 30px; text-align: center; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Informations de l'utilisateur</h1>
                    <p>Date d'impression: ${new Date().toLocaleDateString()}</p>
                </div>

                <table class="info-table">
                    <tr>
                        <th>ID</th>
                        <td>${userId}</td>
                    </tr>
                    <tr>
                        <th>Nom d'utilisateur</th>
                        <td>${username}</td>
                    </tr>
                </table>

                <div class="footer">
                    <p>Gestion des Pointages - Document généré automatiquement</p>
                </div>
            </body>
            </html>
        `;

        // Écrire le contenu dans la fenêtre d'impression
        printWindow.document.open();
        printWindow.document.write(content);
        printWindow.document.close();

        // Imprimer après le chargement du contenu
        printWindow.onload = function() {
            printWindow.print();
        };
    }
</script>
{% endblock %}