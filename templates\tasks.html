{% extends "base_simple.html" %}

{% block title %}Tâches - Gestion des Pointages{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-tasks"></i> Gestion des Tâches</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('add_task') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Ajouter une tâche
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-list"></i> Liste des Tâches</h5>
    </div>
    <div class="card-body">
        {% if tasks %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Titre</th>
                        <th>Description</th>
                        <th>Statut</th>
                        <th>Priorité</th>
                        <th>Assigné à</th>
                        <th>Échéance</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for task in tasks %}
                    <tr>
                        <td>{{ task.id }}</td>
                        <td><strong>{{ task.title }}</strong></td>
                        <td>{{ (task.description[:50] + '...') if task.description and task.description|length > 50 else (task.description or '-') }}</td>
                        <td>
                            {% if task.status == 'new' %}
                                <span class="badge bg-primary">Nouveau</span>
                            {% elif task.status == 'in_progress' %}
                                <span class="badge bg-warning">En cours</span>
                            {% elif task.status == 'completed' %}
                                <span class="badge bg-success">Terminé</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ task.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if task.priority == 'high' %}
                                <span class="badge bg-danger">Élevée</span>
                            {% elif task.priority == 'medium' %}
                                <span class="badge bg-warning">Moyenne</span>
                            {% elif task.priority == 'low' %}
                                <span class="badge bg-info">Faible</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ task.priority }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if task.first_name and task.last_name %}
                                {{ task.first_name }} {{ task.last_name }}
                            {% else %}
                                <span class="text-muted">Non assigné</span>
                            {% endif %}
                        </td>
                        <td>{{ task.due_date or '-' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" title="Imprimer">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucune tâche trouvée</h5>
            <p class="text-muted">Commencez par ajouter votre première tâche.</p>
            <a href="{{ url_for('add_task') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Ajouter une tâche
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
