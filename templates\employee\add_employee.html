{% extends "base.html" %}

{% block title %}Ajouter un employé - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3"><i class="fas fa-user-plus me-2"></i>Ajouter un employé</h1>
        <p class="text-muted">Ajoutez un nouvel employé au système.</p>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>Formulaire d'ajout d'employé</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('employee.add_employee') }}">
                    {{ form.hidden_tag() }}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="first_name" class="form-label">{{ form.first_name.label }}</label>
                            {{ form.first_name(class="form-control", placeholder="Prénom") }}
                            {% for error in form.first_name.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        <div class="col-md-6">
                            <label for="last_name" class="form-label">{{ form.last_name.label }}</label>
                            {{ form.last_name(class="form-control", placeholder="Nom") }}
                            {% for error in form.last_name.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="position" class="form-label">{{ form.position.label }}</label>
                            {{ form.position(class="form-control", placeholder="Poste occupé") }}
                            {% for error in form.position.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        <div class="col-md-6">
                            <label for="daily_rate" class="form-label">{{ form.daily_rate.label }}</label>
                            {{ form.daily_rate(class="form-control", placeholder="Salaire en MAD") }}
                            {% for error in form.daily_rate.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="cin" class="form-label">{{ form.cin.label }}</label>
                            {{ form.cin(class="form-control", placeholder="Numéro CIN") }}
                            {% for error in form.cin.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        <div class="col-md-6">
                            <label for="cnss" class="form-label">{{ form.cnss.label }}</label>
                            {{ form.cnss(class="form-control", placeholder="Numéro CNSS (optionnel)") }}
                            {% for error in form.cnss.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="phone" class="form-label">{{ form.phone.label }}</label>
                            {{ form.phone(class="form-control", placeholder="Numéro de téléphone (optionnel)") }}
                            {% for error in form.phone.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">{{ form.email.label }}</label>
                            {{ form.email(class="form-control", placeholder="Email (optionnel)") }}
                            {% for error in form.email.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="hire_date" class="form-label">{{ form.hire_date.label }}</label>
                            {{ form.hire_date(class="form-control", type="date") }}
                            {% for error in form.hire_date.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">{{ form.notes.label }}</label>
                        {{ form.notes(class="form-control", rows=3, placeholder="Notes additionnelles (optionnel)") }}
                        {% for error in form.notes.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{{ url_for('employee.employees') }}" class="btn btn-secondary me-md-2">Annuler</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
