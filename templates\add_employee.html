{% extends "base_simple.html" %}

{% block title %}Ajouter un Employé - Gestion des Pointages{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-user-plus"></i> Ajouter un Employé</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('employees') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour à la liste
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user"></i> Informations de l'Employé</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">Prénom *</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">Nom *</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cin" class="form-label">CIN</label>
                            <input type="text" class="form-control" id="cin" name="cin">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="position" class="form-label">Poste</label>
                            <input type="text" class="form-control" id="position" name="position">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="salary" class="form-label">Salaire (MAD)</label>
                            <input type="number" step="0.01" class="form-control" id="salary" name="salary">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="hire_date" class="form-label">Date d'embauche</label>
                            <input type="date" class="form-control" id="hire_date" name="hire_date">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Téléphone</label>
                            <input type="tel" class="form-control" id="phone" name="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">Adresse</label>
                        <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('employees') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> Aide</h6>
            </div>
            <div class="card-body">
                <p><small class="text-muted">
                    Remplissez les informations de l'employé. Les champs marqués d'un astérisque (*) sont obligatoires.
                </small></p>
                
                <hr>
                
                <h6>Conseils :</h6>
                <ul class="small text-muted">
                    <li>Vérifiez l'orthographe du nom et prénom</li>
                    <li>Le CIN doit être unique</li>
                    <li>Le salaire est en Dirhams marocains (MAD)</li>
                    <li>L'email doit être valide</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
