{% extends "base.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block content %}
<div class="tasks-page">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="page-title">Gestion des Tâches</h4>
                <p class="page-subtitle">G<PERSON>rez et suivez toutes vos tâches</p>
            </div>
            <div class="header-actions">
                <button type="button" class="btn btn-outline-secondary btn-sm me-2">
                    <i class="fas fa-filter me-1"></i> Filtrer
                </button>
                <a href="{{ url_for('task.add_task') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i> Nouvelle tâche
                </a>
            </div>
        </div>
    </div>

    <!-- Tasks Content -->
    <div class="tasks-content">
        {% if tasks %}
            <!-- Professional Tasks Table -->
            <div class="card shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 text-primary">
                            <i class="fas fa-tasks me-2"></i>Liste des Tâches
                        </h6>
                        <span class="badge bg-primary">{{ tasks|length }} tâche(s)</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0 tasks-table">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%" class="text-center">
                                        <i class="fas fa-hashtag"></i>
                                    </th>
                                    <th width="25%">
                                        <i class="fas fa-heading me-1"></i>Titre
                                    </th>
                                    <th width="20%">
                                        <i class="fas fa-align-left me-1"></i>Description
                                    </th>
                                    <th width="12%" class="text-center">
                                        <i class="fas fa-flag me-1"></i>Statut
                                    </th>
                                    <th width="10%" class="text-center">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Priorité
                                    </th>
                                    <th width="15%">
                                        <i class="fas fa-user me-1"></i>Assigné à
                                    </th>
                                    <th width="8%" class="text-center">
                                        <i class="fas fa-calendar me-1"></i>Échéance
                                    </th>
                                    <th width="5%" class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in tasks %}
                                <tr class="task-row" data-task-id="{{ task.id }}">
                                    <td class="text-center align-middle">
                                        <span class="task-number">#{{ loop.index }}</span>
                                    </td>
                                    <td class="align-middle">
                                        <div class="task-title-cell">
                                            <h6 class="mb-1 task-title-text">{{ task.title }}</h6>
                                            {% if task.category %}
                                            <small class="text-muted">
                                                <i class="fas fa-tag me-1"></i>{{ task.category }}
                                            </small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <div class="task-description-cell">
                                            {% if task.description %}
                                                <span class="description-text" data-full-description="{{ task.description|replace('\n', ' ')|replace('\r', '') }}">
                                                    {% set short_desc = task.description|replace('\n', ' ')|replace('\r', '')|truncate(60, True) %}
                                                    {{ short_desc }}
                                                </span>
                                                {% if task.description|length > 60 %}
                                                <button class="btn btn-link btn-sm p-0 ms-1"
                                                        data-bs-toggle="tooltip"
                                                        title="{{ task.description|replace('\n', ' ')|replace('\r', '') }}">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted fst-italic">Aucune description</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="text-center align-middle">
                                        <span class="badge status-badge status-{{ task.status }}">
                                            {% if task.status == 'new' %}
                                                <i class="fas fa-circle me-1"></i>Nouveau
                                            {% elif task.status == 'in_progress' %}
                                                <i class="fas fa-play-circle me-1"></i>En cours
                                            {% elif task.status == 'completed' %}
                                                <i class="fas fa-check-circle me-1"></i>Terminé
                                            {% elif task.status == 'cancelled' %}
                                                <i class="fas fa-times-circle me-1"></i>Annulé
                                            {% else %}
                                                <i class="fas fa-circle me-1"></i>{{ task.status }}
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td class="text-center align-middle">
                                        <span class="badge priority-badge priority-{{ task.priority or 'medium' }}">
                                            {% if task.priority == 'urgent' %}
                                                <i class="fas fa-fire me-1"></i>Urgent
                                            {% elif task.priority == 'high' %}
                                                <i class="fas fa-arrow-up me-1"></i>Élevée
                                            {% elif task.priority == 'low' %}
                                                <i class="fas fa-arrow-down me-1"></i>Faible
                                            {% else %}
                                                <i class="fas fa-minus me-1"></i>Moyenne
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td class="align-middle">
                                        <div class="assignee-cell">
                                            {% if task.assigned_to %}
                                                {% for employee in employees %}
                                                    {% if employee.id == task.assigned_to %}
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar-sm me-2">
                                                                <span class="avatar-initials">
                                                                    {{ employee.first_name[0] }}{{ employee.last_name[0] }}
                                                                </span>
                                                            </div>
                                                            <div>
                                                                <div class="fw-medium">{{ employee.first_name }} {{ employee.last_name }}</div>
                                                                <small class="text-muted">{{ employee.position }}</small>
                                                            </div>
                                                        </div>
                                                    {% endif %}
                                                {% endfor %}
                                            {% else %}
                                                <span class="text-muted fst-italic">
                                                    <i class="fas fa-user-slash me-1"></i>Non assigné
                                                </span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="text-center align-middle">
                                        {% if task.due_date %}
                                            <div class="due-date-cell">
                                                <div class="fw-medium">{{ task.due_date.strftime('%d/%m') }}</div>
                                                <small class="text-muted">{{ task.due_date.strftime('%Y') }}</small>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center align-middle">
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('task.edit_task', id=task.id) }}"
                                               class="btn btn-sm btn-outline-primary"
                                               data-bs-toggle="tooltip"
                                               title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-success"
                                                    onclick="sendTaskWhatsApp({{ task.id }})"
                                                    data-bs-toggle="tooltip"
                                                    title="Envoyer par WhatsApp">
                                                <i class="fab fa-whatsapp"></i>
                                            </button>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-info"
                                                    onclick="sendTaskEmail({{ task.id }})"
                                                    data-bs-toggle="tooltip"
                                                    title="Envoyer par Email">
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger"
                                                    onclick="confirmDelete({{ task.id }}, '{{ task.title }}')"
                                                    data-bs-toggle="tooltip"
                                                    title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="empty-state-table">
                <div class="text-center py-5">
                    <div class="empty-icon mb-3">
                        <i class="fas fa-tasks fa-4x text-muted"></i>
                    </div>
                    <h5 class="text-muted mb-2">Aucune tâche trouvée</h5>
                    <p class="text-muted mb-4">Commencez par créer votre première tâche pour organiser votre travail.</p>
                    <a href="{{ url_for('task.add_task') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Créer une nouvelle tâche
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title">Confirmer la suppression</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la tâche <strong id="task-title"></strong> ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Annuler</button>
                <a href="#" id="delete-link" class="btn btn-danger btn-sm">Supprimer</a>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block styles %}
<style>
.tasks-page {
    font-size: 14px;
}

.page-header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.page-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    color: #2c3e50;
}

.page-subtitle {
    font-size: 14px;
    color: #6c757d;
    margin: 0;
}

.header-actions .btn {
    font-size: 13px;
    padding: 8px 16px;
}

.tasks-content {
    margin-top: 20px;
}

/* Professional Table Styles - Enhanced */
.tasks-table {
    font-size: 13px;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tasks-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    font-size: 12px;
    padding: 18px 12px;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 10;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    letter-spacing: 0.5px;
}

.tasks-table th:first-child {
    border-top-left-radius: 12px;
}

.tasks-table th:last-child {
    border-top-right-radius: 12px;
}

.tasks-table td {
    padding: 16px 12px;
    border-bottom: 1px solid #f1f3f4;
    background: white;
    transition: all 0.3s ease;
}

.task-row {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
}

.task-row:hover {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    transform: translateY(-2px) scale(1.01);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    z-index: 5;
}

.task-row:hover td {
    background: transparent;
    border-color: rgba(102, 126, 234, 0.3);
}

.task-row:nth-child(even) {
    background: rgba(248, 249, 250, 0.5);
}

.task-row:nth-child(even):hover {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
}

.task-number {
    font-weight: 600;
    color: #667eea;
    font-size: 12px;
}

.task-title-text {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
    font-size: 14px;
}

.description-text {
    color: #6c757d;
    font-size: 12px;
    line-height: 1.4;
}

/* Status Badges - Enhanced */
.status-badge {
    font-size: 11px;
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.status-badge:hover::before {
    left: 100%;
}

.status-new {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.status-in_progress {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.status-completed {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #2d3748;
    text-shadow: 0 1px 2px rgba(255,255,255,0.5);
}

.status-cancelled {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: #2d3748;
    text-shadow: 0 1px 2px rgba(255,255,255,0.5);
}

/* Priority Badges - Enhanced */
.priority-badge {
    font-size: 11px;
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    position: relative;
    overflow: hidden;
}

.priority-urgent {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    animation: urgentPulse 2s infinite;
    box-shadow: 0 0 20px rgba(255, 65, 108, 0.6);
}

.priority-high {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.priority-medium {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.priority-low {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

@keyframes urgentPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(255, 65, 108, 0.6);
    }
    50% {
        transform: scale(1.08);
        box-shadow: 0 0 30px rgba(255, 65, 108, 0.8);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(255, 65, 108, 0.6);
    }
}

/* Avatar Styles */
.avatar-sm {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.avatar-initials {
    color: white;
    font-weight: 600;
    font-size: 12px;
}

/* Due Date Cell */
.due-date-cell {
    text-align: center;
}

/* Action Buttons - Enhanced */
.btn-group .btn {
    border-radius: 8px;
    margin: 0 3px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.btn-group .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn-group .btn:hover::before {
    left: 100%;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-outline-success:hover {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    border-color: #25d366;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
}

.btn-outline-info:hover {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border-color: #17a2b8;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
}

.btn-outline-danger:hover {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border-color: #ff6b6b;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

/* Empty State */
.empty-state-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Card Header - Enhanced */
.card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    padding: 20px 25px;
    color: white;
}

.card-header h6 {
    color: white;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.card-header .badge {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
}

/* Enhanced Visual Effects */
.task-title-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.avatar-sm {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    border: 2px solid white;
}

/* Glowing Effects */
.task-row:hover .status-badge,
.task-row:hover .priority-badge {
    transform: scale(1.05);
    filter: brightness(1.1);
}

/* Smooth Animations */
* {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive */
@media (max-width: 768px) {
    .tasks-table {
        font-size: 11px;
    }

    .tasks-table th,
    .tasks-table td {
        padding: 8px 6px;
    }

    .task-title-text {
        font-size: 12px;
    }

    .avatar-sm {
        width: 24px;
        height: 24px;
    }

    .avatar-initials {
        font-size: 10px;
    }
}







/* Modal Styles */
.modal-content {
    border-radius: 8px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
}

.modal-title {
    font-size: 16px;
    font-weight: 600;
}

/* Tooltips */
.tooltip {
    z-index: 1070;
}

.popover {
    z-index: 1060;
}
</style>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(taskId, taskTitle) {
    document.getElementById('task-title').textContent = taskTitle;

    // إنشاء form للحذف بـ POST
    const deleteButton = document.getElementById('delete-link');
    deleteButton.onclick = function(e) {
        e.preventDefault();

        // إنشاء form مخفي للحذف
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/task/delete/' + taskId;
        form.style.display = 'none';

        // إضافة CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '{{ csrf_token() }}';
        form.appendChild(csrfInput);

        document.body.appendChild(form);
        form.submit();
        return false;
    };

    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// دوال إرسال المهام عبر WhatsApp والإيميل
function sendTaskWhatsApp(taskId) {
    generateTaskPrintSheet(taskId, 'whatsapp');
}

function sendTaskEmail(taskId) {
    generateTaskPrintSheet(taskId, 'email');
}

function generateTaskPrintSheet(taskId, method) {
    // جلب معلومات المهمة من الجدول
    const taskRow = document.querySelector(`tr[data-task-id="${taskId}"]`);
    if (!taskRow) {
        alert('Erreur: Tâche non trouvée');
        return;
    }

    // استخراج المعلومات من الجدول
    const taskNumber = taskRow.querySelector('.task-number').textContent;
    const taskTitle = taskRow.querySelector('.task-title-text').textContent;
    const descriptionElement = taskRow.querySelector('.description-text');
    const taskDescription = descriptionElement ?
                           (descriptionElement.getAttribute('data-full-description') || descriptionElement.textContent) :
                           'Aucune description';
    const statusBadge = taskRow.querySelector('.status-badge').textContent.trim();
    const priorityBadge = taskRow.querySelector('.priority-badge').textContent.trim();
    const assigneeInfo = taskRow.querySelector('.assignee-cell .fw-medium') ?
                        taskRow.querySelector('.assignee-cell .fw-medium').textContent : 'Non assigné';
    const assigneePosition = taskRow.querySelector('.assignee-cell small') ?
                            taskRow.querySelector('.assignee-cell small').textContent : '';
    const dueDate = taskRow.querySelector('.due-date-cell .fw-medium') ?
                   taskRow.querySelector('.due-date-cell .fw-medium').textContent + '/' +
                   taskRow.querySelector('.due-date-cell small').textContent : 'Non définie';

    // إنشاء رسالة مبسطة مع المعلومات الأساسية فقط
    const taskSheet = `📋 FICHE DE TÂCHE

• Titre: ${taskTitle}
• Description: ${taskDescription}
• Statut: ${statusBadge}
• Priorité: ${priorityBadge}
• Assigné à: ${assigneeInfo}${assigneePosition ? ' (' + assigneePosition + ')' : ''}
• Échéance: ${dueDate}

📅 INFORMATIONS DE SUIVI:
• Date d'émission: ${new Date().toLocaleDateString('fr-FR')}
• Heure d'émission: ${new Date().toLocaleTimeString('fr-FR')}`;

    // إرسال حسب الطريقة المختارة
    if (method === 'whatsapp') {
        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(taskSheet)}`;
        window.open(whatsappUrl, '_blank');
    } else if (method === 'email') {
        const subject = `📋 Fiche de Tâche: ${taskTitle}`;
        const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(taskSheet)}`;
        window.location.href = mailtoUrl;
    }

}





// Animation pour les lignes du tableau
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Animation d'entrée pour les lignes
    const taskRows = document.querySelectorAll('.task-row');
    taskRows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';

        setTimeout(() => {
            row.style.transition = 'all 0.5s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Effet de clic sur les lignes
    taskRows.forEach(row => {
        row.addEventListener('click', function(e) {
            // Ne pas déclencher si on clique sur les boutons d'action
            if (!e.target.closest('.btn-group')) {
                const editButton = this.querySelector('.btn-outline-primary');
                if (editButton) {
                    window.location.href = editButton.href;
                }
            }
        });
    });
});
</script>
{% endblock %}
