{% extends "base.html" %}

{% block content %}
<div class="tasks-page">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="page-title">Gestion des Tâches</h4>
                <p class="page-subtitle"><PERSON><PERSON><PERSON> et suivez toutes vos tâches</p>
            </div>
            <div class="header-actions">
                <button type="button" class="btn btn-outline-secondary btn-sm me-2">
                    <i class="fas fa-filter me-1"></i> Filtrer
                </button>
                <a href="{{ url_for('task.add_task') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i> Nouvelle tâche
                </a>
            </div>
        </div>
    </div>

    <!-- Tasks Content -->
    <div class="tasks-content">
        {% if tasks %}
            <!-- Tasks Grid -->
            <div class="row">
                {% for task in tasks %}
                <div class="col-md-4 col-lg-3 mb-3">
                    <div class="task-card">
                        <div class="task-header">
                            <div class="task-status status-{{ task.status }}">
                                {% if task.status == 'new' %}
                                    <i class="fas fa-circle"></i> Nouveau
                                {% elif task.status == 'in_progress' %}
                                    <i class="fas fa-play-circle"></i> En cours
                                {% elif task.status == 'completed' %}
                                    <i class="fas fa-check-circle"></i> Terminé
                                {% elif task.status == 'cancelled' %}
                                    <i class="fas fa-times-circle"></i> Annulé
                                {% else %}
                                    <i class="fas fa-circle"></i> {{ task.status_display }}
                                {% endif %}
                            </div>
                            <div class="task-actions">
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ url_for('task.edit_task', id=task.id) }}">
                                            <i class="fas fa-edit me-2"></i>Modifier
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="sendTaskWhatsApp({{ task.id }}, '{{ task.title }}')">
                                            <i class="fab fa-whatsapp me-2"></i>WhatsApp
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="sendTaskEmail({{ task.id }}, '{{ task.title }}')">
                                            <i class="fas fa-envelope me-2"></i>Email
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="confirmDelete({{ task.id }}, '{{ task.title }}')">
                                            <i class="fas fa-trash me-2"></i>Supprimer
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="task-body">
                            <h6 class="task-title">{{ task.title }}</h6>
                            <p class="task-description">
                                {{ task.description[:80] }}{% if task.description|length > 80 %}...{% endif %}
                            </p>
                            
                            <div class="task-meta">
                                <div class="task-assignee">
                                    <i class="fas fa-user me-1"></i>
                                    <span class="assignee-name">
                                        {% if task.assigned_to %}
                                            {% for employee in employees %}
                                                {% if employee.id == task.assigned_to %}
                                                    {{ employee.first_name }} {{ employee.last_name }}
                                                {% endif %}
                                            {% endfor %}
                                        {% else %}
                                            Non assigné
                                        {% endif %}
                                    </span>
                                </div>
                                
                                {% if task.due_date %}
                                <div class="task-date">
                                    <i class="fas fa-calendar me-1"></i>
                                    <span class="due-date">{{ task.due_date.strftime('%d/%m/%Y') }}</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="task-footer">
                            <div class="task-priority priority-{{ task.priority or 'normale' }}">
                                {% if task.priority == 'haute' %}
                                    <i class="fas fa-exclamation-triangle"></i> Haute
                                {% elif task.priority == 'urgente' %}
                                    <i class="fas fa-exclamation-circle"></i> Urgente
                                {% else %}
                                    <i class="fas fa-minus"></i> Normale
                                {% endif %}
                            </div>
                            
                            <div class="task-category">
                                {% if task.category %}
                                    <span class="badge bg-secondary">{{ task.category }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <h5>Aucune tâche trouvée</h5>
                <p>Commencez par créer votre première tâche pour organiser votre travail.</p>
                <a href="{{ url_for('task.add_task') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Créer une tâche
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title">Confirmer la suppression</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la tâche <strong id="task-title"></strong> ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Annuler</button>
                <a href="#" id="delete-link" class="btn btn-danger btn-sm">Supprimer</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.tasks-page {
    font-size: 12px;
}

.page-header {
    background: white;
    padding: 15px 20px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.page-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: #2c3e50;
}

.page-subtitle {
    font-size: 11px;
    color: #6c757d;
    margin: 0;
}

.header-actions .btn {
    font-size: 11px;
    padding: 6px 12px;
}

.tasks-content {
    margin-top: 20px;
}

.task-card {
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.task-card:hover {
    box-shadow: 0 3px 8px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    border-bottom: 1px solid #f1f3f4;
}

.task-status {
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
    padding: 3px 6px;
    border-radius: 12px;
}

.status-new {
    background: #e3f2fd;
    color: #1976d2;
}

.status-in_progress {
    background: #fff3e0;
    color: #f57c00;
}

.status-completed {
    background: #e8f5e8;
    color: #388e3c;
}

.status-cancelled {
    background: #ffebee;
    color: #d32f2f;
}

/* Support pour les anciens statuts */
.status-nouveau {
    background: #e3f2fd;
    color: #1976d2;
}

.status-en_cours {
    background: #fff3e0;
    color: #f57c00;
}

.status-termine {
    background: #e8f5e8;
    color: #388e3c;
}

.status-annule {
    background: #ffebee;
    color: #d32f2f;
}

.task-actions .btn {
    font-size: 10px;
    padding: 3px 6px;
}

.task-body {
    padding: 12px;
    flex: 1;
}

.task-title {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 6px;
    color: #2c3e50;
    line-height: 1.3;
}

.task-description {
    font-size: 11px;
    color: #6c757d;
    margin-bottom: 10px;
    line-height: 1.4;
}

.task-meta {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.task-assignee, .task-date {
    font-size: 10px;
    color: #6c757d;
    display: flex;
    align-items: center;
}

.task-assignee i, .task-date i {
    width: 12px;
    font-size: 9px;
}

.task-footer {
    padding: 8px 12px;
    border-top: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.task-priority {
    font-size: 9px;
    font-weight: 500;
    text-transform: uppercase;
}

.priority-haute {
    color: #f57c00;
}

.priority-urgente {
    color: #d32f2f;
}

.priority-normale {
    color: #6c757d;
}

.task-category .badge {
    font-size: 9px;
    padding: 2px 6px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.empty-icon {
    font-size: 48px;
    color: #6c757d;
    margin-bottom: 20px;
}

.empty-state h5 {
    font-size: 16px;
    color: #2c3e50;
    margin-bottom: 10px;
}

.empty-state p {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 20px;
}

.dropdown-menu {
    font-size: 11px;
}

.dropdown-item {
    padding: 6px 12px;
}

.modal-content {
    font-size: 12px;
}

.modal-title {
    font-size: 14px;
}
</style>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(taskId, taskTitle) {
    console.log('confirmDelete called with:', taskId, taskTitle);
    document.getElementById('task-title').textContent = taskTitle;

    // تحديث رابط الحذف مباشرة
    const deleteLink = document.getElementById('delete-link');
    deleteLink.href = '/task/delete/' + taskId;

    // إظهار المودال
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function sendTaskWhatsApp(taskId, taskTitle) {
    console.log('sendTaskWhatsApp called with:', taskId, taskTitle);

    // تنظيف النص من الأحرف الخاصة
    const cleanTitle = taskTitle.replace(/'/g, '').replace(/"/g, '');
    const message = `📋 Tâche: ${cleanTitle}\n🔗 Lien: ${window.location.origin}/task/edit/${taskId}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;

    console.log('Opening WhatsApp URL:', whatsappUrl);
    window.open(whatsappUrl, '_blank');
}

function sendTaskEmail(taskId, taskTitle) {
    console.log('sendTaskEmail called with:', taskId, taskTitle);

    // تنظيف النص من الأحرف الخاصة
    const cleanTitle = taskTitle.replace(/'/g, '').replace(/"/g, '');
    const subject = `Tâche: ${cleanTitle}`;
    const body = `Bonjour,\n\nVeuillez consulter la tâche suivante:\n\nTitre: ${cleanTitle}\nLien: ${window.location.origin}/task/edit/${taskId}\n\nCordialement`;

    const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    console.log('Opening email client with URL:', mailtoUrl);
    window.location.href = mailtoUrl;
}

// Animation pour les cartes
document.addEventListener('DOMContentLoaded', function() {
    const taskCards = document.querySelectorAll('.task-card');
    taskCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
});
</script>

<style>
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}
</style>
{% endblock %}
