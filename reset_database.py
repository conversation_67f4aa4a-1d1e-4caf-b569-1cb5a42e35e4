#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour réinitialiser la base de données
"""

import os
import sys
import shutil
from datetime import datetime

def reset_database():
    """Réinitialiser complètement la base de données"""
    print("=== RÉINITIALISATION DE LA BASE DE DONNÉES ===")
    
    # Chemin vers le dossier instance
    instance_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance')
    db_path = os.path.join(instance_dir, 'app.db')
    
    # Créer une sauvegarde si la base de données existe
    if os.path.exists(db_path):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = os.path.join(instance_dir, 'backups')
        os.makedirs(backup_dir, exist_ok=True)
        backup_path = os.path.join(backup_dir, f'backup_before_reset_{timestamp}.db')
        
        try:
            shutil.copy2(db_path, backup_path)
            print(f"Sauvegarde créée: {backup_path}")
        except Exception as e:
            print(f"Erreur lors de la sauvegarde: {e}")
        
        # Supprimer l'ancienne base de données
        try:
            os.remove(db_path)
            print("Ancienne base de données supprimée")
        except Exception as e:
            print(f"Erreur lors de la suppression: {e}")
    
    # Importer et créer l'application
    try:
        from app import create_app, create_tables
        from extensions import db
        
        print("Création de la nouvelle base de données...")
        app = create_app()
        create_tables(app)
        print("Base de données réinitialisée avec succès!")
        print("Utilisateur par défaut: admin / admin")
        
    except Exception as e:
        print(f"Erreur lors de la création: {e}")
        return False
    
    return True

if __name__ == '__main__':
    success = reset_database()
    if success:
        print("\n✅ Réinitialisation terminée avec succès!")
        print("Vous pouvez maintenant démarrer l'application avec: python app.py")
    else:
        print("\n❌ Erreur lors de la réinitialisation")
        sys.exit(1)
