"""
Formulaires pour la gestion de l'entreprise et des sauvegardes
"""
from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import StringField, TextAreaField, SelectField, FloatField, BooleanField, IntegerField, TimeField
from wtforms.validators import DataRequired, Email, Optional, NumberRange, Length
from wtforms.widgets import TextArea

class CompanyInfoForm(FlaskForm):
    """Formulaire pour les informations de l'entreprise"""
    
    # Informations de base
    nom_entreprise = StringField('Nom de l\'entreprise', validators=[DataRequired(), Length(max=200)])
    raison_sociale = StringField('Raison sociale', validators=[Optional(), Length(max=200)])
    secteur_activite = StringField('Secteur d\'activité', validators=[Optional(), Length(max=100)])
    forme_juridique = SelectField('Forme juridique', 
                                choices=[
                                    ('', 'Sélectionner...'),
                                    ('SARL', 'SARL - Société à Responsabilité Limitée'),
                                    ('SA', 'SA - Société Anonyme'),
                                    ('SAS', 'SAS - Société par Actions Simplifiée'),
                                    ('EURL', 'EURL - Entreprise Unipersonnelle à Responsabilité Limitée'),
                                    ('SNC', 'SNC - Société en Nom Collectif'),
                                    ('SCS', 'SCS - Société en Commandite Simple'),
                                    ('Auto-entrepreneur', 'Auto-entrepreneur'),
                                    ('Autre', 'Autre')
                                ], validators=[Optional()])
    
    # Informations de contact
    adresse = TextAreaField('Adresse complète', validators=[Optional()], render_kw={'rows': 3})
    ville = StringField('Ville', validators=[Optional(), Length(max=100)])
    code_postal = StringField('Code postal', validators=[Optional(), Length(max=20)])
    pays = StringField('Pays', validators=[Optional(), Length(max=50)], default='Maroc')
    telephone = StringField('Téléphone', validators=[Optional(), Length(max=20)])
    fax = StringField('Fax', validators=[Optional(), Length(max=20)])
    email = StringField('Email', validators=[Optional(), Email(), Length(max=120)])
    site_web = StringField('Site web', validators=[Optional(), Length(max=200)])
    
    # Informations légales
    numero_registre_commerce = StringField('N° Registre de Commerce', validators=[Optional(), Length(max=50)])
    numero_ice = StringField('N° ICE (Identifiant Commun de l\'Entreprise)', validators=[Optional(), Length(max=50)])
    numero_if = StringField('N° IF (Identifiant Fiscal)', validators=[Optional(), Length(max=50)])
    numero_cnss = StringField('N° CNSS', validators=[Optional(), Length(max=50)])
    numero_patente = StringField('N° Patente', validators=[Optional(), Length(max=50)])
    
    # Capital et banque
    capital_social = FloatField('Capital social', validators=[Optional(), NumberRange(min=0)])
    devise_capital = SelectField('Devise du capital', 
                               choices=[
                                   ('MAD', 'MAD - Dirham Marocain'),
                                   ('EUR', 'EUR - Euro'),
                                   ('USD', 'USD - Dollar Américain'),
                                   ('GBP', 'GBP - Livre Sterling')
                               ], default='MAD', validators=[Optional()])
    banque_principale = StringField('Banque principale', validators=[Optional(), Length(max=100)])
    numero_compte_bancaire = StringField('N° Compte bancaire', validators=[Optional(), Length(max=50)])
    rib = StringField('RIB', validators=[Optional(), Length(max=50)])
    
    # Fichiers
    logo = FileField('Logo de l\'entreprise', 
                    validators=[FileAllowed(['jpg', 'jpeg', 'png', 'gif'], 'Images seulement!')])
    signature = FileField('Signature numérique', 
                         validators=[FileAllowed(['jpg', 'jpeg', 'png', 'gif'], 'Images seulement!')])
    cachet = FileField('Cachet de l\'entreprise', 
                      validators=[FileAllowed(['jpg', 'jpeg', 'png', 'gif'], 'Images seulement!')])
    
    # Pied de page
    pied_de_page = TextAreaField('Pied de page pour les impressions', 
                                validators=[Optional()], 
                                render_kw={'rows': 4, 'placeholder': 'Texte qui apparaîtra en bas de tous les documents imprimés'})
    mentions_legales = TextAreaField('Mentions légales', 
                                   validators=[Optional()], 
                                   render_kw={'rows': 6, 'placeholder': 'Mentions légales et informations complémentaires'})

class DatabaseBackupForm(FlaskForm):
    """Formulaire pour créer une sauvegarde manuelle"""
    
    description = TextAreaField('Description de la sauvegarde', 
                              validators=[Optional()], 
                              render_kw={'rows': 3, 'placeholder': 'Description optionnelle de cette sauvegarde'})
    inclure_uploads = BooleanField('Inclure les fichiers uploadés', default=True)
    compression = BooleanField('Compresser la sauvegarde', default=True)

class AutoBackupSettingsForm(FlaskForm):
    """Formulaire pour les paramètres de sauvegarde automatique"""
    
    # Activation
    is_enabled = BooleanField('Activer les sauvegardes automatiques', default=False)
    
    # Fréquence
    frequence = SelectField('Fréquence des sauvegardes', 
                          choices=[
                              ('daily', 'Quotidienne'),
                              ('weekly', 'Hebdomadaire'),
                              ('monthly', 'Mensuelle')
                          ], default='daily', validators=[DataRequired()])
    
    heure_execution = TimeField('Heure d\'exécution', validators=[DataRequired()])
    
    jour_semaine = SelectField('Jour de la semaine (pour sauvegarde hebdomadaire)', 
                             choices=[
                                 ('', 'Sélectionner...'),
                                 ('0', 'Lundi'),
                                 ('1', 'Mardi'),
                                 ('2', 'Mercredi'),
                                 ('3', 'Jeudi'),
                                 ('4', 'Vendredi'),
                                 ('5', 'Samedi'),
                                 ('6', 'Dimanche')
                             ], validators=[Optional()])
    
    jour_mois = IntegerField('Jour du mois (pour sauvegarde mensuelle)', 
                           validators=[Optional(), NumberRange(min=1, max=31)])
    
    # Rétention
    nombre_max_sauvegardes = IntegerField('Nombre maximum de sauvegardes à conserver', 
                                        validators=[DataRequired(), NumberRange(min=1, max=365)], 
                                        default=30)
    supprimer_anciennes = BooleanField('Supprimer automatiquement les anciennes sauvegardes', default=True)
    
    # Stockage
    dossier_sauvegarde = StringField('Dossier de sauvegarde', 
                                   validators=[Optional(), Length(max=500)],
                                   render_kw={'placeholder': 'Laissez vide pour utiliser le dossier par défaut'})
    inclure_uploads = BooleanField('Inclure les fichiers uploadés', default=True)
    compression = BooleanField('Compresser les sauvegardes', default=True)
    
    # Notifications
    email_notification = BooleanField('Envoyer une notification par email', default=False)
    email_destinataire = StringField('Email de notification', 
                                   validators=[Optional(), Email(), Length(max=120)],
                                   render_kw={'placeholder': '<EMAIL>'})

class DatabaseRestoreForm(FlaskForm):
    """Formulaire pour restaurer une sauvegarde"""
    
    backup_file = FileField('Fichier de sauvegarde', 
                           validators=[DataRequired(), FileAllowed(['sql', 'zip', 'gz'], 'Fichiers de sauvegarde seulement!')])
    confirm_restore = BooleanField('Je confirme vouloir restaurer cette sauvegarde (ATTENTION: cela remplacera toutes les données actuelles)', 
                                 validators=[DataRequired()])
    
class DatabaseImportForm(FlaskForm):
    """Formulaire pour importer des données"""
    
    import_file = FileField('Fichier à importer', 
                          validators=[DataRequired(), FileAllowed(['csv', 'xlsx', 'xls', 'json'], 'Fichiers CSV, Excel ou JSON seulement!')])
    table_name = SelectField('Table de destination', 
                           choices=[
                               ('', 'Sélectionner une table...'),
                               ('users', 'Utilisateurs'),
                               ('employees', 'Employés'),
                               ('simple_tasks', 'Tâches'),
                               ('attendance', 'Présences'),
                               ('financial_records', 'Enregistrements financiers')
                           ], validators=[DataRequired()])
    mode_import = SelectField('Mode d\'importation', 
                            choices=[
                                ('append', 'Ajouter aux données existantes'),
                                ('replace', 'Remplacer les données existantes'),
                                ('update', 'Mettre à jour les données existantes')
                            ], default='append', validators=[DataRequired()])
    premiere_ligne_entetes = BooleanField('La première ligne contient les en-têtes', default=True)
