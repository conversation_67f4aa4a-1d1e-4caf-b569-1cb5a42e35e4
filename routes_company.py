"""
Routes pour la gestion de l'entreprise et des sauvegardes
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app, send_file, jsonify
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
import os
import sqlite3
import shutil
import zipfile
import json
from datetime import datetime, timedelta
# import pandas as pd  # سيتم استيراده عند الحاجة فقط
from extensions import db
from models_company import CompanyInfo, DatabaseBackup, AutoBackupSettings
from forms_company import CompanyInfoForm, DatabaseBackupForm, AutoBackupSettingsForm, DatabaseRestoreForm, DatabaseImportForm

# Créer le blueprint
company_bp = Blueprint('company', __name__)

@company_bp.route('/company/info', methods=['GET', 'POST'])
@login_required
def company_info():
    """Gestion des informations de l'entreprise"""
    if not current_user.is_admin:
        flash('Accès non autorisé.', 'danger')
        return redirect(url_for('main.dashboard'))
    
    # Récupérer les informations existantes ou créer une nouvelle instance
    company = CompanyInfo.get_active_company()
    if not company:
        company = CompanyInfo()
    
    form = CompanyInfoForm(obj=company)
    
    if form.validate_on_submit():
        try:
            # Mettre à jour les informations de base
            form.populate_obj(company)
            
            # Gérer l'upload des fichiers
            upload_folder = current_app.config.get('UPLOAD_FOLDER', 'static/uploads')
            
            # Logo
            if form.logo.data:
                filename = secure_filename(f"logo_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{form.logo.data.filename}")
                logo_path = os.path.join(upload_folder, 'company', filename)
                os.makedirs(os.path.dirname(logo_path), exist_ok=True)
                form.logo.data.save(logo_path)
                company.logo_path = f"company/{filename}"
            
            # Signature
            if form.signature.data:
                filename = secure_filename(f"signature_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{form.signature.data.filename}")
                signature_path = os.path.join(upload_folder, 'company', filename)
                os.makedirs(os.path.dirname(signature_path), exist_ok=True)
                form.signature.data.save(signature_path)
                company.signature_path = f"company/{filename}"
            
            # Cachet
            if form.cachet.data:
                filename = secure_filename(f"cachet_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{form.cachet.data.filename}")
                cachet_path = os.path.join(upload_folder, 'company', filename)
                os.makedirs(os.path.dirname(cachet_path), exist_ok=True)
                form.cachet.data.save(cachet_path)
                company.cachet_path = f"company/{filename}"
            
            # Sauvegarder en base
            if not company.id:
                db.session.add(company)
            
            company.updated_at = datetime.utcnow()
            db.session.commit()
            
            flash('Informations de l\'entreprise mises à jour avec succès!', 'success')
            return redirect(url_for('company.company_info'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la mise à jour: {str(e)}', 'danger')
    
    return render_template('company/company_info.html', form=form, company=company)

@company_bp.route('/company/backup')
@login_required
def backup_management():
    """Page de gestion des sauvegardes"""
    if not current_user.is_admin:
        flash('Accès non autorisé.', 'danger')
        return redirect(url_for('main.dashboard'))
    
    # Récupérer les sauvegardes existantes
    backups = DatabaseBackup.query.order_by(DatabaseBackup.created_at.desc()).all()
    
    # Récupérer les paramètres de sauvegarde automatique
    auto_settings = AutoBackupSettings.get_settings()
    
    return render_template('company/backup_management.html', 
                         backups=backups, 
                         auto_settings=auto_settings)

@company_bp.route('/company/backup/create', methods=['GET', 'POST'])
@login_required
def create_backup():
    """Créer une sauvegarde manuelle"""
    if not current_user.is_admin:
        flash('Accès non autorisé.', 'danger')
        return redirect(url_for('main.dashboard'))
    
    form = DatabaseBackupForm()
    
    if form.validate_on_submit():
        try:
            # Créer le nom du fichier de sauvegarde
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"backup_{timestamp}.sql"
            
            # Dossier de sauvegarde
            backup_folder = os.path.join(current_app.root_path, 'backups')
            os.makedirs(backup_folder, exist_ok=True)
            backup_path = os.path.join(backup_folder, backup_filename)
            
            # Créer la sauvegarde de la base de données
            db_path = current_app.config.get('DATABASE_URL', '').replace('sqlite:///', '')
            if not db_path:
                db_path = os.path.join(current_app.instance_path, 'app.db')
            
            # Exporter la base de données
            with sqlite3.connect(db_path) as conn:
                with open(backup_path, 'w', encoding='utf-8') as f:
                    for line in conn.iterdump():
                        f.write('%s\n' % line)
            
            # Si compression demandée
            if form.compression.data:
                import gzip
                compressed_path = backup_path + '.gz'
                with open(backup_path, 'rb') as f_in:
                    with gzip.open(compressed_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                os.remove(backup_path)
                backup_path = compressed_path
                backup_filename = backup_filename + '.gz'
            
            # Inclure les fichiers uploadés si demandé
            if form.inclure_uploads.data:
                upload_folder = current_app.config.get('UPLOAD_FOLDER', 'static/uploads')
                if os.path.exists(upload_folder):
                    zip_path = backup_path.replace('.sql', '.zip').replace('.gz', '.zip')
                    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                        # Ajouter le fichier SQL
                        zipf.write(backup_path, os.path.basename(backup_path))
                        
                        # Ajouter les fichiers uploadés
                        for root, dirs, files in os.walk(upload_folder):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arcname = os.path.relpath(file_path, os.path.dirname(upload_folder))
                                zipf.write(file_path, arcname)
                    
                    os.remove(backup_path)
                    backup_path = zip_path
                    backup_filename = os.path.basename(zip_path)
            
            # Calculer la taille du fichier
            file_size = os.path.getsize(backup_path)
            
            # Enregistrer en base de données
            backup_record = DatabaseBackup(
                nom_fichier=backup_filename,
                chemin_fichier=backup_path,
                taille_fichier=file_size,
                type_sauvegarde='manuel',
                description=form.description.data,
                created_by=current_user.id
            )
            
            db.session.add(backup_record)
            db.session.commit()
            
            flash('Sauvegarde créée avec succès!', 'success')
            return redirect(url_for('company.backup_management'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la création de la sauvegarde: {str(e)}', 'danger')
    
    return render_template('company/create_backup.html', form=form)

@company_bp.route('/company/backup/download/<int:backup_id>')
@login_required
def download_backup(backup_id):
    """Télécharger une sauvegarde"""
    if not current_user.is_admin:
        flash('Accès non autorisé.', 'danger')
        return redirect(url_for('main.dashboard'))
    
    backup = DatabaseBackup.query.get_or_404(backup_id)
    
    if os.path.exists(backup.chemin_fichier):
        return send_file(backup.chemin_fichier, 
                        as_attachment=True, 
                        download_name=backup.nom_fichier)
    else:
        flash('Fichier de sauvegarde introuvable.', 'danger')
        return redirect(url_for('company.backup_management'))

@company_bp.route('/company/backup/delete/<int:backup_id>', methods=['POST'])
@login_required
def delete_backup(backup_id):
    """Supprimer une sauvegarde"""
    if not current_user.is_admin:
        flash('Accès non autorisé.', 'danger')
        return redirect(url_for('main.dashboard'))
    
    backup = DatabaseBackup.query.get_or_404(backup_id)
    
    try:
        # Supprimer le fichier physique
        if os.path.exists(backup.chemin_fichier):
            os.remove(backup.chemin_fichier)
        
        # Supprimer l'enregistrement de la base
        db.session.delete(backup)
        db.session.commit()
        
        flash('Sauvegarde supprimée avec succès!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la suppression: {str(e)}', 'danger')
    
    return redirect(url_for('company.backup_management'))

@company_bp.route('/company/backup/auto-settings', methods=['GET', 'POST'])
@login_required
def auto_backup_settings():
    """Paramètres de sauvegarde automatique"""
    if not current_user.is_admin:
        flash('Accès non autorisé.', 'danger')
        return redirect(url_for('main.dashboard'))
    
    settings = AutoBackupSettings.get_settings()
    form = AutoBackupSettingsForm(obj=settings)
    
    if form.validate_on_submit():
        try:
            form.populate_obj(settings)
            settings.updated_at = datetime.utcnow()
            
            db.session.commit()
            flash('Paramètres de sauvegarde automatique mis à jour!', 'success')
            return redirect(url_for('company.backup_management'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la mise à jour: {str(e)}', 'danger')
    
    return render_template('company/auto_backup_settings.html', form=form, settings=settings)
