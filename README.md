# Gestion des Pointages

Un système de gestion multi-utilisateurs basé sur le web pour la gestion des employés, des tâches, des présences et des finances.

## 🎯 Description

Gestion des Pointages est une application web professionnelle conçue pour être installée et exécutée sur un serveur local, permettant à plusieurs utilisateurs d'y accéder simultanément via des navigateurs web, sans nécessiter de connexion Internet.

Le système est léger, sécurisé et facile à installer, ce qui le rend adapté aux organisations, associations, établissements d'enseignement ou petites et moyennes entreprises qui ont besoin d'une solution interne simple et efficace sans dépendre de serveurs externes ou de services cloud payants.

## 🔧 Caractéristiques techniques

- ✅ Application web accessible via navigateur (Chrome, Firefox, Edge...)
- ✅ Support multi-utilisateurs (avec connexion et comptes)
- ✅ Fonctionne sur un serveur local au sein du réseau (peut être installé sur n'importe quel ordinateur)
- ✅ Base de données locale (PostgreSQL ou SQLite)
- ✅ Interface utilisateur conviviale en français
- ✅ Extensible et évolutif
- ✅ Ne nécessite pas de connexion Internet pour fonctionner

## 🧰 Technologies utilisées

| Technologie | Utilisation |
|-------------|-------------|
| Python (Flask) | Backend |
| HTML/CSS/JS | Frontend |
| Flask-Login | Gestion des sessions et des utilisateurs |
| SQLite/PostgreSQL | Base de données |
| Bootstrap | Design d'interface responsive |

## 💡 Scénario d'utilisation

1. Le programme est installé sur un ordinateur central (par exemple, l'ordinateur du gardien ou du secrétaire)
2. Les autres utilisateurs sur le même réseau ouvrent leur navigateur et accèdent au serveur (par exemple, http://***********:5000)
3. Chaque utilisateur se connecte avec ses identifiants et commence à travailler sur le système
4. Les données sont synchronisées en temps réel dans la même base de données, et chaque utilisateur ne peut accéder qu'aux fonctionnalités autorisées

## 📋 Fonctionnalités principales

### Tableau de bord
- Vue d'ensemble des statistiques importantes
- Accès rapide aux différentes fonctionnalités

### Gestion des utilisateurs
- Ajout, modification et suppression d'utilisateurs
- Attribution de permissions spécifiques

### Gestion des employés
- Enregistrement des informations des employés
- Suivi des données personnelles et professionnelles

### Gestion des tâches
- Création et assignation de tâches aux employés
- Suivi de l'avancement des tâches

### Gestion des présences
- Enregistrement quotidien des présences
- Visualisation des présences sur un calendrier

### Gestion financière
- Enregistrement des encaissements et décaissements
- Génération de rapports financiers

## 🚀 Installation

### Prérequis
- Python 3.8 ou supérieur
- pip (gestionnaire de paquets Python)

### Étapes d'installation

1. Cloner le dépôt :
   ```
   git clone https://github.com/votre-utilisateur/gestion-des-pointages.git
   cd gestion-des-pointages
   ```

2. Créer un environnement virtuel :
   ```
   python -m venv venv
   ```

3. Activer l'environnement virtuel :
   - Sous Windows :
     ```
     venv\Scripts\activate
     ```
   - Sous Linux/Mac :
     ```
     source venv/bin/activate
     ```

4. Installer les dépendances :
   ```
   pip install -r requirements.txt
   ```

5. Configurer les variables d'environnement :
   - Copier le fichier `.env.example` vers `.env`
   - Modifier les valeurs selon votre configuration

6. Initialiser la base de données :
   ```
   flask db init
   flask db migrate -m "Initial migration"
   flask db upgrade
   ```

7. Lancer l'application :
   ```
   flask run --host=0.0.0.0
   ```

8. Accéder à l'application :
   - Ouvrir un navigateur et accéder à `http://localhost:5000` ou `http://IP_DE_VOTRE_MACHINE:5000`

## 👥 Utilisateur par défaut

Après l'installation, un utilisateur administrateur est créé automatiquement :
- Nom d'utilisateur : `admin`
- Mot de passe : `admin`

**Important** : Changez ce mot de passe immédiatement après la première connexion !

## 📝 Licence

Ce projet est sous licence [MIT](LICENSE).

## 📞 Contact

Pour toute question ou suggestion, veuillez contacter [<EMAIL>].
