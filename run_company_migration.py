#!/usr/bin/env python3
"""
Script pour exécuter la migration des tables de l'entreprise
"""
import sys
import os

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    try:
        from migrations.create_company_tables import create_company_tables, check_tables_exist
        
        print("🏢 Migration des Tables de l'Entreprise")
        print("=" * 50)
        print()
        
        # Vérifier l'état actuel
        print("🔍 Vérification de l'état actuel...")
        if check_tables_exist():
            print("ℹ️  Les tables de l'entreprise existent déjà.")
            print()
            response = input("❓ Voulez-vous recréer les tables ? (y/N): ")
            if response.lower() != 'y':
                print("❌ Migration annulée par l'utilisateur.")
                sys.exit(0)
            print()
        
        # Exécuter la migration
        print("🚀 Création des tables de l'entreprise...")
        create_company_tables()
        
        print()
        print("=" * 50)
        print("✅ Migration terminée avec succès!")
        print()
        print("📋 Nouvelles fonctionnalités disponibles:")
        print("   • Gestion des informations de l'entreprise")
        print("   • Système de sauvegarde automatique")
        print("   • Import/Export de données")
        print("   • Gestion des logos et pieds de page")
        print()
        print("🔗 Accès via le menu: Entreprise > Informations")
        print()
        
    except ImportError as e:
        print(f"❌ Erreur d'importation: {e}")
        print("Assurez-vous que tous les fichiers sont présents.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Erreur lors de la migration: {e}")
        sys.exit(1)
    
    input("Appuyez sur Entrée pour continuer...")
