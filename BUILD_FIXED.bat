@echo off
chcp 65001 >nul
echo ========================================
echo   إصلاح وبناء نظام إدارة الحضور
echo ========================================
echo.

echo 🔧 تنظيف الملفات السابقة...
if exist "dist" rmdir /s /q dist
if exist "build" rmdir /s /q build
if exist "__pycache__" rmdir /s /q __pycache__

echo.
echo 📦 بناء التطبيق بدون خلفية سوداء...
pyinstaller GestionPointages.spec

echo.
echo 📁 إنشاء مجلد التوزيع...
if not exist "dist\GestionPointages_Final" mkdir "dist\GestionPointages_Final"

echo.
echo 📋 نسخ الملفات المطلوبة...
copy "dist\GestionPointages.exe" "dist\GestionPointages_Final\"
xcopy "templates" "dist\GestionPointages_Final\templates\" /E /I /Y
xcopy "static" "dist\GestionPointages_Final\static\" /E /I /Y
xcopy "instance" "dist\GestionPointages_Final\instance\" /E /I /Y

echo.
echo ✅ تم البناء بنجاح!
echo 📂 الملف التنفيذي: dist\GestionPointages_Final\GestionPointages.exe
echo.
echo 🚀 تشغيل التطبيق للاختبار...
start "" "dist\GestionPointages_Final\GestionPointages.exe"

pause
