#!/usr/bin/env python3
"""
Script de démarrage SANS CSRF (pour tests)
"""
import os
import sys

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🚀 GESTION DES POINTAGES - DÉMARRAGE SANS CSRF (TEST)")
    print("=" * 60)
    
    try:
        # Import et création de l'application
        print("📦 Chargement de l'application...")
        from app import create_app, create_tables
        
        app = create_app()
        
        # Désactiver CSRF pour les tests
        app.config['WTF_CSRF_ENABLED'] = False
        app.config['TESTING'] = True
        app.config['SECRET_KEY'] = 'test-key-no-csrf'
        
        print("✅ Application créée (CSRF désactivé)")
        
        # Création des tables et utilisateur admin
        print("🗄️  Initialisation de la base de données...")
        create_tables(app)
        print("✅ Base de données initialisée")
        
        # Vérifier le port
        import socket
        port = 5003  # Port différent pour éviter les conflits
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
        except OSError:
            port = 5004
        
        # Informations de connexion
        print("\n" + "=" * 60)
        print("🌐 SERVEUR DE TEST DÉMARRÉ!")
        print("=" * 60)
        print(f"📍 URL: http://127.0.0.1:{port}")
        print("👤 Utilisateur: admin")
        print("🔑 Mot de passe: admin")
        print("=" * 60)
        print("⚠️  CSRF DÉSACTIVÉ - POUR TESTS UNIQUEMENT")
        print("⏹️  Appuyez sur Ctrl+C pour arrêter le serveur")
        print("=" * 60)
        
        # Démarrage du serveur
        app.run(
            host='127.0.0.1',
            port=port,
            debug=True,
            use_reloader=False,
            threaded=True
        )
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("\n👋 Au revoir!")
        input("Appuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
