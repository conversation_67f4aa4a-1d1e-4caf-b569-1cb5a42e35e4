#!/usr/bin/env python3
"""
Script de démarrage alternatif avec port différent
"""
import os
import sys

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def find_free_port():
    """Trouve un port libre"""
    import socket
    ports_to_try = [5001, 5000, 8000, 8080, 3000]
    
    for port in ports_to_try:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return port
        except OSError:
            continue
    return 9999  # Port par défaut si aucun n'est libre

def main():
    print("🚀 GESTION DES POINTAGES - DÉMARRAGE ALTERNATIF")
    print("=" * 55)
    
    try:
        # Trouver un port libre
        port = find_free_port()
        print(f"🔍 Port sélectionné: {port}")
        
        # Import et création de l'application
        print("📦 Chargement de l'application...")
        from app import create_app, create_tables
        
        app = create_app()
        print("✅ Application créée avec succès")
        
        # Création des tables
        print("🗄️  Initialisation de la base de données...")
        create_tables(app)
        print("✅ Base de données initialisée")
        
        # Informations de connexion
        print("\n" + "=" * 55)
        print("🌐 SERVEUR DÉMARRÉ AVEC SUCCÈS!")
        print("=" * 55)
        print(f"📍 URL: http://127.0.0.1:{port}")
        print("👤 Utilisateur: admin")
        print("🔑 Mot de passe: admin")
        print("=" * 55)
        print("⏹️  Appuyez sur Ctrl+C pour arrêter le serveur")
        print("=" * 55)
        
        # Démarrage du serveur
        app.run(
            host='127.0.0.1',
            port=port,
            debug=True,
            use_reloader=False
        )
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("\n👋 Au revoir!")
        input("Appuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
